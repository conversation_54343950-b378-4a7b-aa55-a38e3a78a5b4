using UnityEngine;

/// <summary>
/// Base class for all space entities in the EVE Online-inspired space game
/// Handles grid management, scaling, and coordinate systems
/// </summary>
public class Entity : MonoBehaviour
{
    [Header("Entity Properties")]
    [SerializeField] private float radiusKm = 0.5f; // Default 0.5km radius
    [SerializeField] private float massKg = 1000f; // Mass in kilograms
    [SerializeField] private bool isPlayer = false;
    [SerializeField] private EntityType entityType = EntityType.Ship;

    [Header("Grid System")]
    [SerializeField] private bool createOwnGrid = true; // Create own grid when outside others

    [Header("Rendering")]
    [SerializeField] private float lodDistance1 = 1000f; // 1km - full detail
    [SerializeField] private float lodDistance2 = 10000f; // 10km - medium detail
    [SerializeField] private float lodDistance3 = 50000f; // 50km - low detail

    // Grid system references
    private SpaceGrid currentGrid;
    private Vector3 universalPosition;
    private Vector3 lastPosition;

    // Precise positioning system
    private Vector3 localGridPosition; // Position within the grid (-1000km to +1000km)
    private Vector3 preciseUniversalPosition; // Precise universal position using grid coordinates

    // Rendering and scaling
    private float currentScale = 1f;
    private int currentLOD = 0;
    private Renderer[] renderers;
    private LODGroup lodGroup;

    // Movement tracking
    private bool hasMovedSignificantly = false;
    private float movementThreshold = 10f; // 10 meters

    public enum EntityType
    {
        Ship,
        Station,
        Asteroid,
        Planet,
        Star,
        Gate,
        Beacon
    }

    void Start()
    {
        Initialize();
    }

    void Update()
    {
        UpdatePosition();
        UpdateLOD();
        CheckGridBoundaries();
    }

    /// <summary>
    /// Initializes the entity and registers it with the grid system
    /// </summary>
    void Initialize()
    {
        // Cache components
        renderers = GetComponentsInChildren<Renderer>();
        lodGroup = GetComponent<LODGroup>();

        // Set initial positions
        lastPosition = transform.position;
        universalPosition = transform.position;

        // Register with coordinate manager (with retry if not ready)
        RegisterWithCoordinateManager();

        // Register with grid system
        if (SpaceGridSystem.Instance != null)
        {
            RegisterWithGridSystem();
        }
        else
        {
            // Wait for grid system to initialize
            Invoke(nameof(RegisterWithGridSystem), 0.1f);
        }
    }

    void RegisterWithCoordinateManager()
    {
        if (SpaceCoordinateManager.Instance != null)
        {
            SpaceCoordinateManager.Instance.RegisterEntity(this);
        }
        else
        {
            // Retry in a moment if coordinate manager isn't ready
            Invoke(nameof(RegisterWithCoordinateManager), 0.1f);
        }
    }

    /// <summary>
    /// Registers this entity with the appropriate grid
    /// </summary>
    void RegisterWithGridSystem()
    {
        if (SpaceGridSystem.Instance == null) return;

        // Find existing grid or create new one
        SpaceGrid targetGrid = SpaceGridSystem.Instance.GetGridAtPosition(transform.position);

        if (targetGrid != null)
        {
            targetGrid.AddEntity(this);
        }
        else if (createOwnGrid)
        {
            targetGrid = SpaceGridSystem.Instance.CreateGridForEntity(this, transform.position);
        }

        currentGrid = targetGrid;
    }

    /// <summary>
    /// Updates position tracking and universal coordinates
    /// </summary>
    void UpdatePosition()
    {
        Vector3 deltaMovement = transform.position - lastPosition;

        if (deltaMovement.magnitude > movementThreshold)
        {
            hasMovedSignificantly = true;
            universalPosition += deltaMovement;
        }

        lastPosition = transform.position;
    }

    /// <summary>
    /// Updates Level of Detail based on distance to player
    /// </summary>
    void UpdateLOD()
    {
        if (SpaceGridSystem.Instance == null || isPlayer) return;

        float distanceToPlayer = Vector3.Distance(transform.position, Vector3.zero); // Player is at origin

        // Determine LOD level
        int newLOD = 0;
        if (distanceToPlayer > lodDistance3)
            newLOD = 3; // Very low detail or hidden
        else if (distanceToPlayer > lodDistance2)
            newLOD = 2; // Low detail
        else if (distanceToPlayer > lodDistance1)
            newLOD = 1; // Medium detail
        else
            newLOD = 0; // Full detail

        // Apply LOD changes
        if (newLOD != currentLOD)
        {
            ApplyLOD(newLOD);
            currentLOD = newLOD;
        }

        // Apply distance-based scaling
        float scaleFactor = SpaceGridSystem.Instance.GetScaleFactorForDistance(distanceToPlayer);
        if (Mathf.Abs(scaleFactor - currentScale) > 0.01f)
        {
            ApplyScale(scaleFactor);
            currentScale = scaleFactor;
        }
    }

    /// <summary>
    /// Applies Level of Detail settings
    /// </summary>
    void ApplyLOD(int lodLevel)
    {
        if (lodGroup != null)
        {
            // Use Unity's LODGroup if available
            return;
        }

        // Manual LOD implementation
        switch (lodLevel)
        {
            case 0: // Full detail
                SetRenderersEnabled(true);
                break;
            case 1: // Medium detail
                SetRenderersEnabled(true);
                break;
            case 2: // Low detail
                SetRenderersEnabled(true);
                break;
            case 3: // Very low detail or hidden
                SetRenderersEnabled(false);
                break;
        }
    }

    /// <summary>
    /// Applies scaling to the entity
    /// </summary>
    void ApplyScale(float scaleFactor)
    {
        transform.localScale = Vector3.one * scaleFactor;
    }

    /// <summary>
    /// Enables or disables all renderers
    /// </summary>
    void SetRenderersEnabled(bool enabled)
    {
        foreach (var renderer in renderers)
        {
            if (renderer != null)
                renderer.enabled = enabled;
        }
    }

    /// <summary>
    /// Checks if the entity has moved outside its current grid
    /// </summary>
    void CheckGridBoundaries()
    {
        if (!hasMovedSignificantly || currentGrid == null) return;

        if (!currentGrid.ContainsPosition(transform.position))
        {
            // Entity has moved outside its current grid
            HandleGridTransition();
        }

        hasMovedSignificantly = false;
    }

    /// <summary>
    /// Handles transition between grids
    /// </summary>
    void HandleGridTransition()
    {
        if (SpaceGridSystem.Instance == null) return;

        // Remove from current grid
        currentGrid?.RemoveEntity(this);

        // Find new grid or create one
        SpaceGrid newGrid = SpaceGridSystem.Instance.GetGridAtPosition(transform.position);

        if (newGrid != null)
        {
            newGrid.AddEntity(this);
        }
        else if (createOwnGrid)
        {
            newGrid = SpaceGridSystem.Instance.CreateGridForEntity(this, transform.position);
        }

        currentGrid = newGrid;
    }

    /// <summary>
    /// Sets the current grid for this entity
    /// </summary>
    public void SetCurrentGrid(SpaceGrid grid)
    {
        currentGrid = grid;
    }

    /// <summary>
    /// Gets the entity's position in universal coordinates
    /// </summary>
    public Vector3 GetUniversalPosition()
    {
        if (SpaceGridSystem.Instance != null)
        {
            return SpaceGridSystem.Instance.LocalToUniversal(transform.position);
        }
        return universalPosition;
    }

    /// <summary>
    /// Sets the entity's position using universal coordinates
    /// </summary>
    public void SetUniversalPosition(Vector3 universalPos)
    {
        if (SpaceGridSystem.Instance != null)
        {
            transform.position = SpaceGridSystem.Instance.UniversalToLocal(universalPos);
        }
        else
        {
            transform.position = universalPos;
        }
        universalPosition = universalPos;
    }

    /// <summary>
    /// Gets the precise universal position using the coordinate manager
    /// </summary>
    public Vector3 GetPreciseUniversalPosition()
    {
        if (SpaceCoordinateManager.Instance != null)
        {
            return SpaceCoordinateManager.Instance.GetEntityUniversalPosition(this);
        }

        // Fallback to old method
        return GetUniversalPosition();
    }

    /// <summary>
    /// Sets the precise universal position using the coordinate manager
    /// </summary>
    public void SetPreciseUniversalPosition(Vector3 precisePos)
    {
        if (SpaceCoordinateManager.Instance != null)
        {
            SpaceCoordinateManager.Instance.SetEntityUniversalPosition(this, precisePos);
        }
        else
        {
            // Fallback to old method
            SetUniversalPosition(precisePos);
        }
    }

    /// <summary>
    /// Gets the distance to another entity using precise coordinates
    /// </summary>
    public float GetPreciseDistanceTo(Entity other)
    {
        if (SpaceCoordinateManager.Instance != null)
        {
            return SpaceCoordinateManager.Instance.GetDistanceBetweenEntities(this, other);
        }

        // Fallback calculation
        Vector3 thisPos = GetUniversalPosition();
        Vector3 otherPos = other.GetUniversalPosition();
        return Vector3.Distance(thisPos, otherPos);
    }

    void OnDestroy()
    {
        // Remove from grid when destroyed
        currentGrid?.RemoveEntity(this);
    }

    void OnDrawGizmosSelected()
    {
        // Draw entity radius
        Gizmos.color = Color.cyan;
        Gizmos.DrawWireSphere(transform.position, radiusKm * 1000f); // Convert km to meters for gizmo

        // Draw grid connection
        if (currentGrid != null)
        {
            Gizmos.color = Color.yellow;
            Gizmos.DrawLine(transform.position, currentGrid.Center);
        }
    }

    // Properties
    public float RadiusKm => radiusKm;
    public float Mass => massKg;
    public bool IsPlayer => isPlayer;
    public EntityType Type => entityType;
    public SpaceGrid CurrentGrid => currentGrid;
    public Vector3 UniversalPosition => GetUniversalPosition();
    public int CurrentLOD => currentLOD;
    public float CurrentScale => currentScale;
}
