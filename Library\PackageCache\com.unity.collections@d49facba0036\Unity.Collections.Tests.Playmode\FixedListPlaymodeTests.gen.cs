﻿
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     TextTransform Samples/Packages/com.unity.collections/Unity.Collections.Tests.Playmode/FixedListPlayModeTests.tt
//
//     These tests require UNITY_DOTS_DEBUG to be enabled.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.Collections;
using NUnit.Framework;
using UnityEngine.TestTools;
using System;
using Unity.Collections;

[TestFixture]
internal class FixedListPlaymodeTests
{
    [UnityTest]
#if UNITY_EDITOR // standalone only
        [Ignore("Test is for standalone builds only")]
#endif
    public IEnumerator FixedListPlaymode_AreCollectionsChecksEnabled()
    {
#if ENABLE_UNITY_COLLECTIONS_CHECKS
        Assert.IsTrue(false, "Collections checks should not be enabled in standalone playmode tests");
#endif
#if !UNITY_DOTS_DEBUG
        Assert.IsTrue(false, "UNITY_DOTS_DEBUG should be defined in standalone playmode tests");
#endif
        yield break;
    }

    [UnityTest]
    public IEnumerator FixedList32BytePlaymode_GenericHasExpectedCapacity()
    {
        var list = new FixedList32Bytes<byte>();
        var expectedCapacity = list.Capacity;
        for(int i = 0; i < expectedCapacity; ++i)
            list.Add((byte)i);
        Assert.Throws<IndexOutOfRangeException> (() => { list.Add((byte)expectedCapacity); });

        yield break;
    }

    [UnityTest]
    public IEnumerator FixedList64BytePlaymode_GenericHasExpectedCapacity()
    {
        var list = new FixedList64Bytes<byte>();
        var expectedCapacity = list.Capacity;
        for(int i = 0; i < expectedCapacity; ++i)
            list.Add((byte)i);
        Assert.Throws<IndexOutOfRangeException> (() => { list.Add((byte)expectedCapacity); });

        yield break;
    }

    [UnityTest]
    public IEnumerator FixedList128BytePlaymode_GenericHasExpectedCapacity()
    {
        var list = new FixedList128Bytes<byte>();
        var expectedCapacity = list.Capacity;
        for(int i = 0; i < expectedCapacity; ++i)
            list.Add((byte)i);
        Assert.Throws<IndexOutOfRangeException> (() => { list.Add((byte)expectedCapacity); });

        yield break;
    }

    [UnityTest]
    public IEnumerator FixedList32BytePlaymode_HasExpectedCapacity()
    {
        var list = new FixedList32Bytes<byte>();
        var expectedCapacity = list.Capacity;
        for(int i = 0; i < expectedCapacity; ++i)
            list.Add((byte)i);
        Assert.Throws<IndexOutOfRangeException> (() => { list.Add((byte)expectedCapacity); });

        yield break;
    }

    [UnityTest]
    public IEnumerator FixedList64BytePlaymode_HasExpectedCapacity()
    {
        var list = new FixedList64Bytes<byte>();
        var expectedCapacity = list.Capacity;
        for(int i = 0; i < expectedCapacity; ++i)
            list.Add((byte)i);
        Assert.Throws<IndexOutOfRangeException> (() => { list.Add((byte)expectedCapacity); });

        yield break;
    }

    [UnityTest]
    public IEnumerator FixedList64BytePlaymode_To_FixedList32Byte()
    {
        var a = new FixedList64Bytes<byte>();
        for(var i = 0; i < 62; ++i)
            a.Add((byte)i);
        Assert.Throws<IndexOutOfRangeException> (() => { var b = new FixedList32Bytes<byte>(a); } );
        yield break;
    }

    [UnityTest]
    public IEnumerator FixedList128BytePlaymode_HasExpectedCapacity()
    {
        var list = new FixedList128Bytes<byte>();
        var expectedCapacity = list.Capacity;
        for(int i = 0; i < expectedCapacity; ++i)
            list.Add((byte)i);
        Assert.Throws<IndexOutOfRangeException> (() => { list.Add((byte)expectedCapacity); });

        yield break;
    }

    [UnityTest]
    public IEnumerator FixedList128BytePlaymode_To_FixedList32Byte()
    {
        var a = new FixedList128Bytes<byte>();
        for(var i = 0; i < 126; ++i)
            a.Add((byte)i);
        Assert.Throws<IndexOutOfRangeException> (() => { var b = new FixedList32Bytes<byte>(a); } );
        yield break;
    }

    [UnityTest]
    public IEnumerator FixedList128BytePlaymode_To_FixedList64Byte()
    {
        var a = new FixedList128Bytes<byte>();
        for(var i = 0; i < 126; ++i)
            a.Add((byte)i);
        Assert.Throws<IndexOutOfRangeException> (() => { var b = new FixedList64Bytes<byte>(a); } );
        yield break;
    }

    [UnityTest]
    public IEnumerator FixedList32IntPlaymode_GenericHasExpectedCapacity()
    {
        var list = new FixedList32Bytes<int>();
        var expectedCapacity = list.Capacity;
        for(int i = 0; i < expectedCapacity; ++i)
            list.Add((int)i);
        Assert.Throws<IndexOutOfRangeException> (() => { list.Add((int)expectedCapacity); });

        yield break;
    }

    [UnityTest]
    public IEnumerator FixedList64IntPlaymode_GenericHasExpectedCapacity()
    {
        var list = new FixedList64Bytes<int>();
        var expectedCapacity = list.Capacity;
        for(int i = 0; i < expectedCapacity; ++i)
            list.Add((int)i);
        Assert.Throws<IndexOutOfRangeException> (() => { list.Add((int)expectedCapacity); });

        yield break;
    }

    [UnityTest]
    public IEnumerator FixedList128IntPlaymode_GenericHasExpectedCapacity()
    {
        var list = new FixedList128Bytes<int>();
        var expectedCapacity = list.Capacity;
        for(int i = 0; i < expectedCapacity; ++i)
            list.Add((int)i);
        Assert.Throws<IndexOutOfRangeException> (() => { list.Add((int)expectedCapacity); });

        yield break;
    }

    [UnityTest]
    public IEnumerator FixedList32IntPlaymode_HasExpectedCapacity()
    {
        var list = new FixedList32Bytes<int>();
        var expectedCapacity = list.Capacity;
        for(int i = 0; i < expectedCapacity; ++i)
            list.Add((int)i);
        Assert.Throws<IndexOutOfRangeException> (() => { list.Add((int)expectedCapacity); });

        yield break;
    }

    [UnityTest]
    public IEnumerator FixedList64IntPlaymode_HasExpectedCapacity()
    {
        var list = new FixedList64Bytes<int>();
        var expectedCapacity = list.Capacity;
        for(int i = 0; i < expectedCapacity; ++i)
            list.Add((int)i);
        Assert.Throws<IndexOutOfRangeException> (() => { list.Add((int)expectedCapacity); });

        yield break;
    }

    [UnityTest]
    public IEnumerator FixedList64IntPlaymode_To_FixedList32Int()
    {
        var a = new FixedList64Bytes<int>();
        for(var i = 0; i < 15; ++i)
            a.Add((int)i);
        Assert.Throws<IndexOutOfRangeException> (() => { var b = new FixedList32Bytes<int>(a); } );
        yield break;
    }

    [UnityTest]
    public IEnumerator FixedList128IntPlaymode_HasExpectedCapacity()
    {
        var list = new FixedList128Bytes<int>();
        var expectedCapacity = list.Capacity;
        for(int i = 0; i < expectedCapacity; ++i)
            list.Add((int)i);
        Assert.Throws<IndexOutOfRangeException> (() => { list.Add((int)expectedCapacity); });

        yield break;
    }

    [UnityTest]
    public IEnumerator FixedList128IntPlaymode_To_FixedList32Int()
    {
        var a = new FixedList128Bytes<int>();
        for(var i = 0; i < 31; ++i)
            a.Add((int)i);
        Assert.Throws<IndexOutOfRangeException> (() => { var b = new FixedList32Bytes<int>(a); } );
        yield break;
    }

    [UnityTest]
    public IEnumerator FixedList128IntPlaymode_To_FixedList64Int()
    {
        var a = new FixedList128Bytes<int>();
        for(var i = 0; i < 31; ++i)
            a.Add((int)i);
        Assert.Throws<IndexOutOfRangeException> (() => { var b = new FixedList64Bytes<int>(a); } );
        yield break;
    }

    [UnityTest]
    public IEnumerator FixedList32FloatPlaymode_GenericHasExpectedCapacity()
    {
        var list = new FixedList32Bytes<float>();
        var expectedCapacity = list.Capacity;
        for(int i = 0; i < expectedCapacity; ++i)
            list.Add((float)i);
        Assert.Throws<IndexOutOfRangeException> (() => { list.Add((float)expectedCapacity); });

        yield break;
    }

    [UnityTest]
    public IEnumerator FixedList64FloatPlaymode_GenericHasExpectedCapacity()
    {
        var list = new FixedList64Bytes<float>();
        var expectedCapacity = list.Capacity;
        for(int i = 0; i < expectedCapacity; ++i)
            list.Add((float)i);
        Assert.Throws<IndexOutOfRangeException> (() => { list.Add((float)expectedCapacity); });

        yield break;
    }

    [UnityTest]
    public IEnumerator FixedList128FloatPlaymode_GenericHasExpectedCapacity()
    {
        var list = new FixedList128Bytes<float>();
        var expectedCapacity = list.Capacity;
        for(int i = 0; i < expectedCapacity; ++i)
            list.Add((float)i);
        Assert.Throws<IndexOutOfRangeException> (() => { list.Add((float)expectedCapacity); });

        yield break;
    }

    [UnityTest]
    public IEnumerator FixedList32FloatPlaymode_HasExpectedCapacity()
    {
        var list = new FixedList32Bytes<float>();
        var expectedCapacity = list.Capacity;
        for(int i = 0; i < expectedCapacity; ++i)
            list.Add((float)i);
        Assert.Throws<IndexOutOfRangeException> (() => { list.Add((float)expectedCapacity); });

        yield break;
    }

    [UnityTest]
    public IEnumerator FixedList64FloatPlaymode_HasExpectedCapacity()
    {
        var list = new FixedList64Bytes<float>();
        var expectedCapacity = list.Capacity;
        for(int i = 0; i < expectedCapacity; ++i)
            list.Add((float)i);
        Assert.Throws<IndexOutOfRangeException> (() => { list.Add((float)expectedCapacity); });

        yield break;
    }

    [UnityTest]
    public IEnumerator FixedList64FloatPlaymode_To_FixedList32Float()
    {
        var a = new FixedList64Bytes<float>();
        for(var i = 0; i < 15; ++i)
            a.Add((float)i);
        Assert.Throws<IndexOutOfRangeException> (() => { var b = new FixedList32Bytes<float>(a); } );
        yield break;
    }

    [UnityTest]
    public IEnumerator FixedList128FloatPlaymode_HasExpectedCapacity()
    {
        var list = new FixedList128Bytes<float>();
        var expectedCapacity = list.Capacity;
        for(int i = 0; i < expectedCapacity; ++i)
            list.Add((float)i);
        Assert.Throws<IndexOutOfRangeException> (() => { list.Add((float)expectedCapacity); });

        yield break;
    }

    [UnityTest]
    public IEnumerator FixedList128FloatPlaymode_To_FixedList32Float()
    {
        var a = new FixedList128Bytes<float>();
        for(var i = 0; i < 31; ++i)
            a.Add((float)i);
        Assert.Throws<IndexOutOfRangeException> (() => { var b = new FixedList32Bytes<float>(a); } );
        yield break;
    }

    [UnityTest]
    public IEnumerator FixedList128FloatPlaymode_To_FixedList64Float()
    {
        var a = new FixedList128Bytes<float>();
        for(var i = 0; i < 31; ++i)
            a.Add((float)i);
        Assert.Throws<IndexOutOfRangeException> (() => { var b = new FixedList64Bytes<float>(a); } );
        yield break;
    }

}
