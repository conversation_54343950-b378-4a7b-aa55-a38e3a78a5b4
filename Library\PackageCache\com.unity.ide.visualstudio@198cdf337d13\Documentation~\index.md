# Code Editor Package for Visual Studio

## About Visual Studio Editor

The Visual Studio Editor package provides the Unity Editor with support for Unity-specific features from the [Visual Studio Tools for Unity](https://docs.microsoft.com/en-us/visualstudio/gamedev/unity/get-started/visual-studio-tools-for-unity) extension in [Visual Studio](https://visualstudio.microsoft.com/) and the [Unity for Visual Studio Code](https://marketplace.visualstudio.com/items?itemName=visualstudiotoolsforunity.vstuc) extension in [Visual Studio Code](https://code.visualstudio.com/). These include IntelliSense auto-complete suggestions, C# editing, and debugging.

## Installation

This package is a built-in package and installed by default.

**Note**: If you’re using a version of the Unity Editor before 2019.4, you’ll need to install this package through the package manager.

## Requirements

This version of the Visual Studio Editor package is compatible with the following versions of the Unity Editor:

* 2019.4 and later

To use this package, you must have the following third-party products installed:

* **On Windows**: Visual Studio 2019 version 16.9 or newer with [Visual Studio Tools for Unity](https://docs.microsoft.com/en-us/visualstudio/gamedev/unity/get-started/visual-studio-tools-for-unity) 4.0.9 or newer.
* **On macOS**: Visual Studio Code with [Unity for Visual Studio Code](https://marketplace.visualstudio.com/items?itemName=visualstudiotoolsforunity.vstuc) 0.9.0 or newer.

For more information on using Visual Studio with Unity, refer to Microsoft's [Visual Studio Tools for Unity](https://docs.microsoft.com/en-us/visualstudio/gamedev/unity/get-started/visual-studio-tools-for-unity) documentation.

For more information on using VS Code with Unity, refer to the Visual Studio Code [Unity development with VS code](https://code.visualstudio.com/docs/other/unity) documentation.

## Submitting issues

This package is maintained by Microsoft and Unity. Submit issues directly from the **Help** menu in Visual Studio (**Help** > **Submit Feedback** > **Report a Problem**) or Visual Studio Code (**Help** > **Report an Issue**). Unity will make this package accessible to the public on GitHub in the future.
