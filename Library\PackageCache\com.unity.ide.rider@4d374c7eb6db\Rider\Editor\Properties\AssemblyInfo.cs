using System.Reflection;
using System.Runtime.CompilerServices;

[assembly: <PERSON><PERSON><PERSON><PERSON>("Unity.Rider.Editor")]
[assembly: InternalsVisibleTo("Unity.Rider.EditorTests")]
[assembly: InternalsVisibleTo("Unity.PackageValidationSuite.Editor")]
[assembly: InternalsVisibleTo("Assembly-CSharp-Editor")]
[assembly: InternalsVisibleTo("DynamicProxyGenAssembly2")]

[assembly: AssemblyVersion("3.0.7")]
