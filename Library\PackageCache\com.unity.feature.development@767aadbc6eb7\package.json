{"name": "com.unity.feature.development", "displayName": "Engineering", "version": "1.0.2", "type": "feature", "_upm": {"quickstart": "https://docs.unity3d.com/Documentation/Manual/DeveloperToolsFeature.html"}, "description": "Optimize your development experience in Unity with the Dev Tools feature set. Enable support for multiple integrated development environments (IDE) for editing your Unity code. Get access to development tools to help you test and analyze your project’s performance.", "dependencies": {"com.unity.ide.visualstudio": "default", "com.unity.ide.rider": "default", "com.unity.editorcoroutines": "default", "com.unity.performance.profile-analyzer": "default", "com.unity.test-framework": "default", "com.unity.testtools.codecoverage": "default"}, "_fingerprint": "767aadbc6eb72681a4ca807c8fa248e0230a0cef"}