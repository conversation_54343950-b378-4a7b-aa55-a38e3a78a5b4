using UnityEngine;

/// <summary>
/// Comprehensive scene setup script for the EVE Online-inspired space system
/// Automatically configures all necessary components and creates a demo scene
/// </summary>
public class SpaceSceneSetup : MonoBehaviour
{
    [Header("Scene Configuration")]
    [SerializeField] private bool autoSetupOnStart = true;
    [SerializeField] private bool createDemoContent = true;
    [SerializeField] private bool setupDebugger = true;
    
    [Header("Player Ship Configuration")]
    [SerializeField] private GameObject playerShipPrefab;
    [SerializeField] private Vector3 playerStartPosition = Vector3.zero;
    [SerializeField] private bool addShipController = true;
    
    [Header("Camera Configuration")]
    [SerializeField] private Camera mainCamera;
    [SerializeField] private bool setupOrbitCamera = true;
    [SerializeField] private bool setupSpaceCamera = true;
    [SerializeField] private float initialCameraDistance = 1000f;
    
    [Header("Demo Content")]
    [SerializeField] private bool createSpaceStation = true;
    [SerializeField] private bool createAsteroidField = true;
    [SerializeField] private bool createPlanets = false;
    [SerializeField] private int asteroidCount = 50;
    
    // Created objects
    private GameObject playerShip;
    private GameObject spaceGridSystemGO;
    private GameObject gridDebuggerGO;
    
    void Start()
    {
        if (autoSetupOnStart)
        {
            SetupCompleteScene();
        }
    }
    
    /// <summary>
    /// Sets up the complete space scene with all necessary components
    /// </summary>
    [ContextMenu("Setup Complete Scene")]
    public void SetupCompleteScene()
    {
        Debug.Log("Setting up complete EVE Online-inspired space scene...");
        
        // Step 1: Setup core systems
        SetupSpaceGridSystem();
        
        // Step 2: Setup camera
        SetupCamera();
        
        // Step 3: Create player ship
        CreatePlayerShip();
        
        // Step 4: Setup debugger
        if (setupDebugger)
        {
            SetupGridDebugger();
        }
        
        // Step 5: Create demo content
        if (createDemoContent)
        {
            CreateDemoContent();
        }
        
        // Step 6: Final configuration
        FinalizeSetup();
        
        Debug.Log("Space scene setup complete!");
        LogSetupInstructions();
    }
    
    /// <summary>
    /// Sets up the SpaceGridSystem
    /// </summary>
    void SetupSpaceGridSystem()
    {
        // Check if SpaceGridSystem already exists
        SpaceGridSystem existingSystem = FindObjectOfType<SpaceGridSystem>();
        if (existingSystem != null)
        {
            spaceGridSystemGO = existingSystem.gameObject;
            Debug.Log("Using existing SpaceGridSystem");
            return;
        }
        
        // Create new SpaceGridSystem
        spaceGridSystemGO = new GameObject("SpaceGridSystem");
        SpaceGridSystem gridSystem = spaceGridSystemGO.AddComponent<SpaceGridSystem>();
        
        Debug.Log("SpaceGridSystem created");
    }
    
    /// <summary>
    /// Sets up the camera with orbit and space controls
    /// </summary>
    void SetupCamera()
    {
        // Find main camera if not assigned
        if (mainCamera == null)
        {
            mainCamera = Camera.main;
            if (mainCamera == null)
            {
                Debug.LogError("No main camera found! Please assign a camera.");
                return;
            }
        }
        
        // Setup orbit camera
        if (setupOrbitCamera)
        {
            OrbitCamera orbitCamera = mainCamera.GetComponent<OrbitCamera>();
            if (orbitCamera == null)
            {
                orbitCamera = mainCamera.gameObject.AddComponent<OrbitCamera>();
            }
            
            // Configure orbit camera settings
            ConfigureOrbitCamera(orbitCamera);
        }
        
        // Setup space camera controller
        if (setupSpaceCamera)
        {
            SpaceCameraController spaceCamera = mainCamera.GetComponent<SpaceCameraController>();
            if (spaceCamera == null)
            {
                spaceCamera = mainCamera.gameObject.AddComponent<SpaceCameraController>();
            }
        }
        
        // Position camera for good initial view
        mainCamera.transform.position = new Vector3(0, 500, -initialCameraDistance);
        mainCamera.transform.LookAt(Vector3.zero);
        
        // Configure camera for space rendering
        ConfigureCameraForSpace();
        
        Debug.Log("Camera setup complete");
    }
    
    /// <summary>
    /// Configures orbit camera settings
    /// </summary>
    void ConfigureOrbitCamera(OrbitCamera orbitCamera)
    {
        // Use reflection to set private fields since they're not exposed
        var orbitCameraType = typeof(OrbitCamera);
        
        // Set orbit speed
        var orbitSpeedField = orbitCameraType.GetField("orbitSpeed", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (orbitSpeedField != null)
        {
            orbitSpeedField.SetValue(orbitCamera, SpaceConstants.ORBIT_SPEED_MULTIPLIER);
        }
        
        // Set zoom settings
        var minDistanceField = orbitCameraType.GetField("minDistance", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (minDistanceField != null)
        {
            minDistanceField.SetValue(orbitCamera, SpaceConstants.MIN_ZOOM_DISTANCE_M);
        }
        
        var maxDistanceField = orbitCameraType.GetField("maxDistance", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (maxDistanceField != null)
        {
            maxDistanceField.SetValue(orbitCamera, SpaceConstants.MAX_ZOOM_DISTANCE_M);
        }
        
        var defaultDistanceField = orbitCameraType.GetField("defaultDistance", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (defaultDistanceField != null)
        {
            defaultDistanceField.SetValue(orbitCamera, SpaceConstants.DEFAULT_ZOOM_DISTANCE_M);
        }
    }
    
    /// <summary>
    /// Configures camera settings optimized for space rendering
    /// </summary>
    void ConfigureCameraForSpace()
    {
        mainCamera.nearClipPlane = 0.1f;
        mainCamera.farClipPlane = 1000000f; // 1000km
        mainCamera.allowHDR = true;
        mainCamera.clearFlags = CameraClearFlags.Skybox;
        
        // Set background to black for space
        if (mainCamera.clearFlags == CameraClearFlags.SolidColor)
        {
            mainCamera.backgroundColor = Color.black;
        }
    }
    
    /// <summary>
    /// Creates the player ship
    /// </summary>
    void CreatePlayerShip()
    {
        if (playerShipPrefab != null)
        {
            playerShip = Instantiate(playerShipPrefab, playerStartPosition, Quaternion.identity);
        }
        else
        {
            // Create a simple cube as player ship
            playerShip = GameObject.CreatePrimitive(PrimitiveType.Cube);
            playerShip.transform.position = playerStartPosition;
            playerShip.transform.localScale = Vector3.one * 50f; // 50m ship
            
            // Add a simple material
            Renderer renderer = playerShip.GetComponent<Renderer>();
            if (renderer != null)
            {
                renderer.material.color = Color.cyan;
            }
        }
        
        playerShip.name = "PlayerShip";
        
        // Add Entity component
        Entity playerEntity = playerShip.GetComponent<Entity>();
        if (playerEntity == null)
        {
            playerEntity = playerShip.AddComponent<Entity>();
        }
        
        // Configure as player entity
        ConfigurePlayerEntity(playerEntity);
        
        // Add ship controller
        if (addShipController)
        {
            SpaceshipController shipController = playerShip.GetComponent<SpaceshipController>();
            if (shipController == null)
            {
                shipController = playerShip.AddComponent<SpaceshipController>();
            }
        }
        
        // Set camera target
        if (mainCamera != null)
        {
            OrbitCamera orbitCamera = mainCamera.GetComponent<OrbitCamera>();
            if (orbitCamera != null)
            {
                orbitCamera.SetTarget(playerShip.transform);
            }
        }
        
        // Configure SpaceGridSystem player reference
        ConfigureGridSystemPlayerReference();
        
        Debug.Log("Player ship created and configured");
    }
    
    /// <summary>
    /// Configures the player entity using reflection
    /// </summary>
    void ConfigurePlayerEntity(Entity entity)
    {
        var entityType = typeof(Entity);
        
        // Set as player
        var isPlayerField = entityType.GetField("isPlayer", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (isPlayerField != null)
        {
            isPlayerField.SetValue(entity, true);
        }
        
        // Set entity type
        var entityTypeField = entityType.GetField("entityType", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (entityTypeField != null)
        {
            entityTypeField.SetValue(entity, Entity.EntityType.Ship);
        }
        
        // Set radius and mass
        var radiusField = entityType.GetField("radiusKm", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (radiusField != null)
        {
            radiusField.SetValue(entity, SpaceConstants.EntityTypeConfig.Ship.defaultRadius);
        }
        
        var massField = entityType.GetField("massKg", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (massField != null)
        {
            massField.SetValue(entity, SpaceConstants.EntityTypeConfig.Ship.defaultMass);
        }
    }
    
    /// <summary>
    /// Configures the SpaceGridSystem player reference
    /// </summary>
    void ConfigureGridSystemPlayerReference()
    {
        if (SpaceGridSystem.Instance != null && playerShip != null)
        {
            var gridSystemType = typeof(SpaceGridSystem);
            var playerTransformField = gridSystemType.GetField("playerTransform", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (playerTransformField != null)
            {
                playerTransformField.SetValue(SpaceGridSystem.Instance, playerShip.transform);
            }
        }
    }
    
    /// <summary>
    /// Sets up the grid debugger
    /// </summary>
    void SetupGridDebugger()
    {
        GridDebugger existingDebugger = FindObjectOfType<GridDebugger>();
        if (existingDebugger != null)
        {
            gridDebuggerGO = existingDebugger.gameObject;
            Debug.Log("Using existing GridDebugger");
            return;
        }
        
        gridDebuggerGO = new GameObject("GridDebugger");
        gridDebuggerGO.AddComponent<GridDebugger>();
        
        Debug.Log("GridDebugger created");
    }
    
    /// <summary>
    /// Creates demo content for testing
    /// </summary>
    void CreateDemoContent()
    {
        if (createSpaceStation)
        {
            CreateDemoSpaceStation();
        }
        
        if (createAsteroidField)
        {
            CreateDemoAsteroidField();
        }
        
        if (createPlanets)
        {
            CreateDemoPlanets();
        }
    }
    
    /// <summary>
    /// Creates a demo space station
    /// </summary>
    void CreateDemoSpaceStation()
    {
        Vector3 stationPosition = SpaceCoordinates.RandomPositionInDisk(Vector3.zero, 50f, 5f); // 50km from player
        
        GameObject station = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
        station.transform.position = stationPosition;
        station.transform.localScale = new Vector3(500f, 100f, 500f); // Large station
        station.name = "SpaceStation";
        
        // Configure station entity
        Entity stationEntity = station.AddComponent<Entity>();
        ConfigureEntityWithType(stationEntity, Entity.EntityType.Station);
        
        // Add visual distinction
        Renderer stationRenderer = station.GetComponent<Renderer>();
        if (stationRenderer != null)
        {
            stationRenderer.material.color = Color.gray;
        }
        
        Debug.Log($"Demo space station created at {SpaceCoordinates.FormatDistance(stationPosition.magnitude)}");
    }
    
    /// <summary>
    /// Creates a demo asteroid field
    /// </summary>
    void CreateDemoAsteroidField()
    {
        GameObject asteroidParent = new GameObject("AsteroidField");
        
        for (int i = 0; i < asteroidCount; i++)
        {
            Vector3 asteroidPosition = SpaceCoordinates.RandomPositionInDisk(Vector3.zero, 100f, 2f); // 100km field
            
            GameObject asteroid = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            asteroid.transform.position = asteroidPosition;
            asteroid.transform.rotation = Random.rotation;
            asteroid.transform.localScale = Vector3.one * Random.Range(10f, 100f);
            asteroid.transform.SetParent(asteroidParent.transform);
            asteroid.name = $"Asteroid_{i:D3}";
            
            // Configure asteroid entity
            Entity asteroidEntity = asteroid.AddComponent<Entity>();
            ConfigureEntityWithType(asteroidEntity, Entity.EntityType.Asteroid);
            
            // Add visual distinction
            Renderer asteroidRenderer = asteroid.GetComponent<Renderer>();
            if (asteroidRenderer != null)
            {
                asteroidRenderer.material.color = new Color(0.6f, 0.4f, 0.2f); // Brown
            }
        }
        
        Debug.Log($"Demo asteroid field created with {asteroidCount} asteroids");
    }
    
    /// <summary>
    /// Creates demo planets
    /// </summary>
    void CreateDemoPlanets()
    {
        // Create a few planets at different distances
        for (int i = 0; i < 3; i++)
        {
            float distance = (i + 1) * 5f; // 5, 10, 15 AU
            Vector3 planetPosition = SpaceCoordinates.RandomPositionInDisk(Vector3.zero, distance * SpaceConstants.KM_PER_AU, 0.1f);
            
            GameObject planet = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            planet.transform.position = planetPosition;
            planet.transform.localScale = Vector3.one * Random.Range(1000f, 5000f); // Large planets
            planet.name = $"Planet_{i + 1}";
            
            // Configure planet entity
            Entity planetEntity = planet.AddComponent<Entity>();
            ConfigureEntityWithType(planetEntity, Entity.EntityType.Planet);
            
            // Add visual distinction
            Renderer planetRenderer = planet.GetComponent<Renderer>();
            if (planetRenderer != null)
            {
                planetRenderer.material.color = Random.ColorHSV(0f, 1f, 0.5f, 1f, 0.5f, 1f);
            }
        }
        
        Debug.Log("Demo planets created");
    }
    
    /// <summary>
    /// Configures an entity with a specific type
    /// </summary>
    void ConfigureEntityWithType(Entity entity, Entity.EntityType entityType)
    {
        var config = SpaceConstants.GetEntityConfig(entityType);
        var entityTypeReflection = typeof(Entity);
        
        // Set entity type
        var entityTypeField = entityTypeReflection.GetField("entityType", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (entityTypeField != null)
        {
            entityTypeField.SetValue(entity, entityType);
        }
        
        // Set radius and mass
        var radiusField = entityTypeReflection.GetField("radiusKm", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (radiusField != null)
        {
            radiusField.SetValue(entity, config.defaultRadius);
        }
        
        var massField = entityTypeReflection.GetField("massKg", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (massField != null)
        {
            massField.SetValue(entity, config.defaultMass);
        }
        
        // Set create own grid
        var createOwnGridField = entityTypeReflection.GetField("createOwnGrid", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (createOwnGridField != null)
        {
            createOwnGridField.SetValue(entity, config.createOwnGrid);
        }
    }
    
    /// <summary>
    /// Finalizes the scene setup
    /// </summary>
    void FinalizeSetup()
    {
        // Ensure all systems are properly initialized
        if (SpaceGridSystem.Instance != null)
        {
            SpaceGridSystem.Instance.enabled = true;
        }
        
        // Set time scale to normal
        Time.timeScale = 1f;
        
        Debug.Log("Scene finalization complete");
    }
    
    /// <summary>
    /// Logs setup instructions for the user
    /// </summary>
    void LogSetupInstructions()
    {
        Debug.Log("=== EVE ONLINE-INSPIRED SPACE SYSTEM READY ===");
        Debug.Log("Controls:");
        Debug.Log("- WASD/Arrow Keys: Move ship");
        Debug.Log("- Left Click + Drag: Orbit camera");
        Debug.Log("- Mouse Wheel: Zoom in/out");
        Debug.Log("- Space: Random warp");
        Debug.Log("- X: Brake");
        Debug.Log("- F1-F5: Debug toggles");
        Debug.Log("- F: Focus on space station");
        Debug.Log("- R: Return to player");
        Debug.Log("- G: Show grid info");
        Debug.Log("===============================================");
    }
}
