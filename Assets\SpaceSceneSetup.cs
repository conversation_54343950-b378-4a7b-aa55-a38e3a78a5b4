using UnityEngine;

/// <summary>
/// Comprehensive scene setup script for the EVE Online-inspired space system
/// Automatically configures all necessary components and creates a demo scene
/// </summary>
public class SpaceSceneSetup : MonoBehaviour
{
    [Header("Scene Configuration")]
    [SerializeField] private bool autoSetupOnStart = true;
    [SerializeField] private bool createDemoContent = true;
    [SerializeField] private bool setupDebugger = true;
    
    [Header("Player Ship Configuration")]
    [SerializeField] private GameObject playerShipPrefab;
    [SerializeField] private Vector3 playerStartPosition = Vector3.zero;
    [SerializeField] private bool addShipController = true;
    
    [Header("Camera Configuration")]
    [SerializeField] private Camera mainCamera;
    [SerializeField] private bool setupOrbitCamera = true;
    [SerializeField] private bool setupSpaceCamera = true;
    [SerializeField] private float initialCameraDistance = 1000f;
    
    [Header("Solar System Configuration")]
    [SerializeField] private float systemRadiusAU = 18f; // Solar system radius in AU
    [SerializeField] private bool createCentralStar = true;
    [SerializeField] private Vector3 starPosition = Vector3.zero; // Star at system center

    [Header("Planetary System")]
    [SerializeField] private bool createPlanets = true;
    [SerializeField] private int numberOfPlanets = 8;
    [SerializeField] private float innerPlanetMinAU = 0.5f; // Closest planet distance
    [SerializeField] private float outerPlanetMaxAU = 15f; // Farthest planet distance

    [Header("Space Stations")]
    [SerializeField] private bool createSpaceStations = true;
    [SerializeField] private int numberOfStations = 12;
    [SerializeField] private float stationMinDistanceAU = 1f;
    [SerializeField] private float stationMaxDistanceAU = 10f;

    [Header("Asteroid Belts")]
    [SerializeField] private bool createAsteroidBelts = true;
    [SerializeField] private int numberOfBelts = 3;
    [SerializeField] private int asteroidsPerBelt = 50;
    [SerializeField] private float beltMinDistanceAU = 2f;
    [SerializeField] private float beltMaxDistanceAU = 8f;
    [SerializeField] private float beltThicknessAU = 0.2f; // Vertical thickness of belts

    [Header("Jump Gates")]
    [SerializeField] private bool createJumpGates = true;
    [SerializeField] private int numberOfGates = 4;
    [SerializeField] private float gateDistanceAU = 16f; // Near system edge

    [Header("Navigation Beacons")]
    [SerializeField] private bool createBeacons = true;
    [SerializeField] private int numberOfBeacons = 8;
    [SerializeField] private float beaconMinDistanceAU = 0.1f;
    [SerializeField] private float beaconMaxDistanceAU = 12f;

    [Header("Entity Prefabs (Optional)")]
    [SerializeField] private GameObject stationPrefab;
    [SerializeField] private GameObject starPrefab;
    [SerializeField] private GameObject asteroidPrefab;
    [SerializeField] private GameObject beaconPrefab;
    [SerializeField] private GameObject gatePrefab;
    [SerializeField] private GameObject asteroidBeltPrefab;

    // Created objects
    private GameObject playerShip;
    private GameObject spaceGridSystemGO;
    private GameObject gridDebuggerGO;
    private GameObject systemParent;

    // Coordinate system constants
    private const float METERS_PER_SCALED_UNIT = 1000000f; // 1.0 = 1000km = 1,000,000m
    private const float AU_TO_SCALED_UNITS = 149597870.7f / METERS_PER_SCALED_UNIT; // 1 AU in scaled units

    /// <summary>
    /// Converts AU distance to scaled coordinates
    /// </summary>
    private float AUToScaledUnits(float au)
    {
        return au * AU_TO_SCALED_UNITS;
    }

    // System object containers
    private GameObject planetsContainer;
    private GameObject stationsContainer;
    private GameObject asteroidsContainer;
    private GameObject gatesContainer;
    private GameObject beaconsContainer;
    
    void Start()
    {
        if (autoSetupOnStart)
        {
            SetupCompleteScene();
        }
    }
    
    /// <summary>
    /// Sets up the complete space scene with all necessary components
    /// </summary>
    [ContextMenu("Setup Complete Scene")]
    public void SetupCompleteScene()
    {
        // Step 1: Setup coordinate manager
        SetupCoordinateManager();

        // Step 2: Setup core systems
        SetupSpaceGridSystem();
        
        // Step 2: Setup camera
        SetupCamera();
        
        // Step 3: Create player ship
        CreatePlayerShip();
        
        // Step 4: Setup debugger
        if (setupDebugger)
        {
            SetupGridDebugger();
        }
        
        // Step 5: Create solar system
        CreateSolarSystem();
        
        // Step 6: Final configuration
        FinalizeSetup();
        
        LogSetupInstructions();
    }
    
    /// <summary>
    /// Sets up the SpaceCoordinateManager
    /// </summary>
    void SetupCoordinateManager()
    {
        // Check if SpaceCoordinateManager already exists
        SpaceCoordinateManager existingManager = FindObjectOfType<SpaceCoordinateManager>();
        if (existingManager != null)
        {
            Debug.Log("SpaceCoordinateManager already exists");
            return;
        }

        // Create SpaceCoordinateManager
        GameObject coordManagerGO = new GameObject("SpaceCoordinateManager");
        SpaceCoordinateManager coordManager = coordManagerGO.AddComponent<SpaceCoordinateManager>();

        Debug.Log("SpaceCoordinateManager created successfully");
    }

    /// <summary>
    /// Sets up the SpaceGridSystem
    /// </summary>
    void SetupSpaceGridSystem()
    {
        // Check if SpaceGridSystem already exists
        SpaceGridSystem existingSystem = FindObjectOfType<SpaceGridSystem>();
        if (existingSystem != null)
        {
            spaceGridSystemGO = existingSystem.gameObject;

            return;
        }
        
        // Create new SpaceGridSystem
        spaceGridSystemGO = new GameObject("SpaceGridSystem");
        SpaceGridSystem gridSystem = spaceGridSystemGO.AddComponent<SpaceGridSystem>();
    }
    
    /// <summary>
    /// Sets up the camera with orbit and space controls
    /// </summary>
    void SetupCamera()
    {
        // Find main camera if not assigned
        if (mainCamera == null)
        {
            mainCamera = Camera.main;
            if (mainCamera == null)
            {

                return;
            }
        }
        
        // Setup orbit camera
        if (setupOrbitCamera)
        {
            OrbitCamera orbitCamera = mainCamera.GetComponent<OrbitCamera>();
            if (orbitCamera == null)
            {
                orbitCamera = mainCamera.gameObject.AddComponent<OrbitCamera>();
            }
            
            // Configure orbit camera settings
            ConfigureOrbitCamera(orbitCamera);
        }
        
        // Setup space camera controller
        if (setupSpaceCamera)
        {
            SpaceCameraController spaceCamera = mainCamera.GetComponent<SpaceCameraController>();
            if (spaceCamera == null)
            {
                spaceCamera = mainCamera.gameObject.AddComponent<SpaceCameraController>();
            }
        }
        
        // Position camera for good initial view
        mainCamera.transform.position = new Vector3(0, 500, -initialCameraDistance);
        mainCamera.transform.LookAt(Vector3.zero);
        
        // Configure camera for space rendering
        ConfigureCameraForSpace();
        
    }

    /// <summary>
    /// Configures orbit camera settings
    /// </summary>
    void ConfigureOrbitCamera(OrbitCamera orbitCamera)
    {
        // Use reflection to set private fields since they're not exposed
        var orbitCameraType = typeof(OrbitCamera);
        
        // Set orbit speed
        var orbitSpeedField = orbitCameraType.GetField("orbitSpeed", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (orbitSpeedField != null)
        {
            orbitSpeedField.SetValue(orbitCamera, SpaceConstants.ORBIT_SPEED_MULTIPLIER);
        }
        
        // Set zoom settings
        var minDistanceField = orbitCameraType.GetField("minDistance", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (minDistanceField != null)
        {
            minDistanceField.SetValue(orbitCamera, SpaceConstants.MIN_ZOOM_DISTANCE_M);
        }
        
        var maxDistanceField = orbitCameraType.GetField("maxDistance", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (maxDistanceField != null)
        {
            maxDistanceField.SetValue(orbitCamera, SpaceConstants.MAX_ZOOM_DISTANCE_M);
        }
        
        var defaultDistanceField = orbitCameraType.GetField("defaultDistance", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (defaultDistanceField != null)
        {
            defaultDistanceField.SetValue(orbitCamera, SpaceConstants.DEFAULT_ZOOM_DISTANCE_M);
        }
    }
    
    /// <summary>
    /// Configures camera settings optimized for space rendering
    /// </summary>
    void ConfigureCameraForSpace()
    {
        mainCamera.nearClipPlane = 0.1f;
        mainCamera.farClipPlane = 1000000f; // 1000km
        mainCamera.allowHDR = true;
        mainCamera.clearFlags = CameraClearFlags.Skybox;
        
        // Set background to black for space
        if (mainCamera.clearFlags == CameraClearFlags.SolidColor)
        {
            mainCamera.backgroundColor = Color.black;
        }
    }
    
    /// <summary>
    /// Creates the player ship
    /// </summary>
    void CreatePlayerShip()
    {
        if (playerShipPrefab != null)
        {
            playerShip = Instantiate(playerShipPrefab, playerStartPosition, Quaternion.identity);
        }
        else
        {
            // Create a simple cube as player ship
            playerShip = GameObject.CreatePrimitive(PrimitiveType.Cube);
            playerShip.transform.position = playerStartPosition;
            playerShip.transform.localScale = Vector3.one * 50f; // 50m ship
            
            // Add a simple material
            Renderer renderer = playerShip.GetComponent<Renderer>();
            if (renderer != null)
            {
                renderer.material.color = Color.cyan;
            }
        }
        
        playerShip.name = "PlayerShip";
        
        // Add Entity component
        Entity playerEntity = playerShip.GetComponent<Entity>();
        if (playerEntity == null)
        {
            playerEntity = playerShip.AddComponent<Entity>();
        }
        
        // Configure as player entity
        ConfigurePlayerEntity(playerEntity);
        
        // Add ship controller
        if (addShipController)
        {
            SpaceshipController shipController = playerShip.GetComponent<SpaceshipController>();
            if (shipController == null)
            {
                shipController = playerShip.AddComponent<SpaceshipController>();
            }

            // Add warp UI
            WarpUI warpUI = playerShip.GetComponent<WarpUI>();
            if (warpUI == null)
            {
                warpUI = playerShip.AddComponent<WarpUI>();
            }

            // Add EVE-style UI
            EVEStyleUI eveUI = playerShip.GetComponent<EVEStyleUI>();
            if (eveUI == null)
            {
                eveUI = playerShip.AddComponent<EVEStyleUI>();
            }

            // Add System Map
            SystemMap systemMap = playerShip.GetComponent<SystemMap>();
            if (systemMap == null)
            {
                systemMap = playerShip.AddComponent<SystemMap>();
            }

            // Add Warp Debugger
            WarpDebugger warpDebugger = playerShip.GetComponent<WarpDebugger>();
            if (warpDebugger == null)
            {
                warpDebugger = playerShip.AddComponent<WarpDebugger>();
            }

            // Add Entity Info Window
            EntityInfoWindow infoWindow = playerShip.GetComponent<EntityInfoWindow>();
            if (infoWindow == null)
            {
                infoWindow = playerShip.AddComponent<EntityInfoWindow>();
            }

            // Add Targeting System
            TargetingSystem targetingSystem = playerShip.GetComponent<TargetingSystem>();
            if (targetingSystem == null)
            {
                targetingSystem = playerShip.AddComponent<TargetingSystem>();
            }

            // Add Ship Command System
            ShipCommandSystem commandSystem = playerShip.GetComponent<ShipCommandSystem>();
            if (commandSystem == null)
            {
                commandSystem = playerShip.AddComponent<ShipCommandSystem>();
            }

            // Add Target Window UI
            TargetWindowUI targetWindowUI = playerShip.GetComponent<TargetWindowUI>();
            if (targetWindowUI == null)
            {
                targetWindowUI = playerShip.AddComponent<TargetWindowUI>();
            }

            // Add Entity Context Menu
            EntityContextMenu contextMenu = playerShip.GetComponent<EntityContextMenu>();
            if (contextMenu == null)
            {
                contextMenu = playerShip.AddComponent<EntityContextMenu>();
            }

            // Add Capacitor System
            CapacitorSystem capacitorSystem = playerShip.GetComponent<CapacitorSystem>();
            if (capacitorSystem == null)
            {
                capacitorSystem = playerShip.AddComponent<CapacitorSystem>();
            }

            // Add Ship Status System
            ShipStatusSystem statusSystem = playerShip.GetComponent<ShipStatusSystem>();
            if (statusSystem == null)
            {
                statusSystem = playerShip.AddComponent<ShipStatusSystem>();
            }

            // Add Ship Status UI
            ShipStatusUI statusUI = playerShip.GetComponent<ShipStatusUI>();
            if (statusUI == null)
            {
                statusUI = playerShip.AddComponent<ShipStatusUI>();
            }

            // Ensure SpaceCoordinateManager has the correct player entity
            if (SpaceCoordinateManager.Instance != null)
            {
                Entity shipEntity = playerShip.GetComponent<Entity>();
                if (shipEntity != null)
                {
                    SpaceCoordinateManager.Instance.SetPlayerEntity(shipEntity);
                    Debug.Log($"Set player entity in SpaceCoordinateManager: {shipEntity.name}");
                }
                else
                {
                    Debug.LogWarning("Player ship has no Entity component!");
                }
            }
        }
        
        // Set camera target
        if (mainCamera != null)
        {
            OrbitCamera orbitCamera = mainCamera.GetComponent<OrbitCamera>();
            if (orbitCamera != null)
            {
                orbitCamera.SetTarget(playerShip.transform);
            }
        }
        
        // Configure SpaceGridSystem player reference
        ConfigureGridSystemPlayerReference();
        
    }

    /// <summary>
    /// Configures the player entity using reflection
    /// </summary>
    void ConfigurePlayerEntity(Entity entity)
    {
        var entityType = typeof(Entity);
        
        // Set as player
        var isPlayerField = entityType.GetField("isPlayer", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (isPlayerField != null)
        {
            isPlayerField.SetValue(entity, true);
        }
        
        // Set entity type
        var entityTypeField = entityType.GetField("entityType", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (entityTypeField != null)
        {
            entityTypeField.SetValue(entity, Entity.EntityType.Ship);
        }
        
        // Set radius and mass
        var radiusField = entityType.GetField("radiusKm", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (radiusField != null)
        {
            radiusField.SetValue(entity, SpaceConstants.EntityTypeConfig.Ship.defaultRadius);
        }
        
        var massField = entityType.GetField("massKg", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (massField != null)
        {
            massField.SetValue(entity, SpaceConstants.EntityTypeConfig.Ship.defaultMass);
        }
    }
    
    /// <summary>
    /// Configures the SpaceGridSystem player reference
    /// </summary>
    void ConfigureGridSystemPlayerReference()
    {
        if (SpaceGridSystem.Instance != null && playerShip != null)
        {
            var gridSystemType = typeof(SpaceGridSystem);
            var playerTransformField = gridSystemType.GetField("playerTransform", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (playerTransformField != null)
            {
                playerTransformField.SetValue(SpaceGridSystem.Instance, playerShip.transform);
            }
        }
    }
    
    /// <summary>
    /// Sets up the grid debugger
    /// </summary>
    void SetupGridDebugger()
    {
        GridDebugger existingDebugger = FindObjectOfType<GridDebugger>();
        if (existingDebugger != null)
        {
            gridDebuggerGO = existingDebugger.gameObject;
            return;
        }

        gridDebuggerGO = new GameObject("GridDebugger");
        gridDebuggerGO.AddComponent<GridDebugger>();
    }
    
    /// <summary>
    /// Creates a complete solar system with realistic distribution
    /// </summary>
    void CreateSolarSystem()
    {
        // Create system organization containers
        CreateSystemContainers();

        // Create central star
        if (createCentralStar)
        {
            CreateCentralStar();
        }

        // Create planetary system
        if (createPlanets)
        {
            CreatePlanetarySystem();
        }

        // Create space stations
        Debug.Log($"Checking space station creation: createSpaceStations = {createSpaceStations}");
        if (createSpaceStations)
        {
            Debug.Log("Calling CreateSpaceStations()...");
            CreateSpaceStations();
            Debug.Log("CreateSpaceStations() completed");
        }
        else
        {
            Debug.Log("Space station creation is disabled");
        }

        // Create asteroid belts
        if (createAsteroidBelts)
        {
            CreateAsteroidBelts();
        }

        // Create jump gates
        if (createJumpGates)
        {
            CreateJumpGates();
        }

        // Create navigation beacons
        if (createBeacons)
        {
            CreateNavigationBeacons();
        }


    }

    /// <summary>
    /// Creates organizational containers for system objects
    /// </summary>
    void CreateSystemContainers()
    {
        systemParent = new GameObject($"SolarSystem_{systemRadiusAU}AU");

        planetsContainer = new GameObject("Planets");
        planetsContainer.transform.SetParent(systemParent.transform);

        stationsContainer = new GameObject("SpaceStations");
        stationsContainer.transform.SetParent(systemParent.transform);

        asteroidsContainer = new GameObject("AsteroidBelts");
        asteroidsContainer.transform.SetParent(systemParent.transform);

        gatesContainer = new GameObject("JumpGates");
        gatesContainer.transform.SetParent(systemParent.transform);

        beaconsContainer = new GameObject("NavigationBeacons");
        beaconsContainer.transform.SetParent(systemParent.transform);
    }
    
    /// <summary>
    /// Creates the central star of the solar system
    /// </summary>
    void CreateCentralStar()
    {
        GameObject star;

        // Use prefab if assigned, otherwise create primitive
        if (starPrefab != null)
        {
            star = Instantiate(starPrefab, starPosition, Quaternion.identity);
            star.name = "CentralStar";
            Debug.Log("Created central star from prefab");
        }
        else
        {
            // Fallback to primitive sphere
            star = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            star.transform.position = starPosition;
            // Scale in scaled coordinates: 1392000km = 1.392 scaled units
            star.transform.localScale = Vector3.one * (1392000f / 1000f / 1000f); // 1392000km in scaled coords
            star.name = "CentralStar";

            // Add visual distinction for primitive
            Renderer starRenderer = star.GetComponent<Renderer>();
            if (starRenderer != null)
            {
                starRenderer.material.color = Color.yellow;
                starRenderer.material.SetColor("_EmissionColor", Color.yellow * 2f);
            }
            Debug.Log("Created central star from primitive (no prefab assigned)");
        }

        star.transform.SetParent(systemParent.transform);

        // Configure star entity (add Entity component if not present)
        Entity starEntity = star.GetComponent<Entity>();
        if (starEntity == null)
        {
            starEntity = star.AddComponent<Entity>();
        }
        ConfigureEntityWithType(starEntity, Entity.EntityType.Star);

        // Set star-specific properties
        SetEntityRadius(starEntity, 696f); // 696,000 km radius (Sun-like)
        SetEntityMass(starEntity, 1.989e30f); // Solar mass
    }

    /// <summary>
    /// Creates the planetary system with realistic orbital distances
    /// </summary>
    void CreatePlanetarySystem()
    {
        for (int i = 0; i < numberOfPlanets; i++)
        {
            // Calculate orbital distance using a logarithmic distribution
            float normalizedPosition = (float)i / (numberOfPlanets - 1);
            float orbitalDistanceAU = Mathf.Lerp(innerPlanetMinAU, outerPlanetMaxAU,
                                                Mathf.Pow(normalizedPosition, 1.5f));

            // Random orbital angle
            float orbitalAngle = Random.Range(0f, 360f);

            // Calculate position in Unity units (AU distances)
            Vector3 planetPosition = new Vector3(
                Mathf.Cos(orbitalAngle * Mathf.Deg2Rad) * SpaceCoordinates.AUToUnityUnits(orbitalDistanceAU),
                Random.Range(-0.1f, 0.1f) * SpaceCoordinates.AUToUnityUnits(orbitalDistanceAU), // Slight inclination
                Mathf.Sin(orbitalAngle * Mathf.Deg2Rad) * SpaceCoordinates.AUToUnityUnits(orbitalDistanceAU)
            );

            // Create planet
            GameObject planet = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            planet.transform.position = planetPosition;
            planet.name = $"Planet_{i + 1:D2}_{orbitalDistanceAU:F1}AU";
            planet.transform.SetParent(planetsContainer.transform);

            // Scale based on distance (inner planets smaller, outer planets larger)
            float planetRadius = Random.Range(3000f, 12000f) * (0.5f + normalizedPosition * 0.5f); // 3-12k km radius
            planet.transform.localScale = Vector3.one * SpaceCoordinates.KilometersToUnityUnits(planetRadius * 2f);

            // Configure planet entity
            Entity planetEntity = planet.AddComponent<Entity>();
            ConfigureEntityWithType(planetEntity, Entity.EntityType.Planet);
            SetEntityRadius(planetEntity, planetRadius / 1000f); // Convert to km
            SetEntityMass(planetEntity, Random.Range(3.3e23f, 1.9e27f)); // Earth to Jupiter mass range

            // Add visual distinction
            Renderer planetRenderer = planet.GetComponent<Renderer>();
            if (planetRenderer != null)
            {
                // Color based on distance (inner = rocky, outer = gas giant)
                if (orbitalDistanceAU < 2f)
                    planetRenderer.material.color = Random.ColorHSV(0f, 0.2f, 0.3f, 0.8f, 0.5f, 1f); // Rocky colors
                else
                    planetRenderer.material.color = Random.ColorHSV(0.5f, 0.8f, 0.4f, 1f, 0.6f, 1f); // Gas giant colors
            }


        }
    }
    
    /// <summary>
    /// Creates space stations distributed throughout the system
    /// </summary>
    void CreateSpaceStations()
    {
        Debug.Log($"=== CREATING SPACE STATIONS ===");
        Debug.Log($"createSpaceStations: {createSpaceStations}");
        Debug.Log($"numberOfStations: {numberOfStations}");
        Debug.Log($"stationPrefab: {(stationPrefab != null ? stationPrefab.name : "NULL")}");
        Debug.Log($"stationsContainer: {(stationsContainer != null ? "EXISTS" : "NULL")}");

        for (int i = 0; i < numberOfStations; i++)
        {
            // Random distance within station range
            float stationDistanceAU = Random.Range(stationMinDistanceAU, stationMaxDistanceAU);

            // Random position in 3D space
            Vector3 randomDirection = Random.onUnitSphere;
            randomDirection.y *= 0.3f; // Flatten slightly to simulate orbital plane preference
            randomDirection.Normalize();

            Vector3 stationPosition = randomDirection * SpaceCoordinates.AUToUnityUnits(stationDistanceAU);

            // Create station
            GameObject station;
            StationType stationType = (StationType)Random.Range(0, 4);

            // Use prefab if assigned, otherwise create primitive
            if (stationPrefab != null)
            {
                station = Instantiate(stationPrefab, stationPosition, Random.rotation);
                station.name = $"Station_{i + 1:D2}_{stationDistanceAU:F2}AU";
                Debug.Log($"Created station from prefab: {station.name}");
            }
            else
            {
                // Fallback to primitive cylinder
                station = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
                station.transform.position = stationPosition;
                station.transform.rotation = Random.rotation;
                station.name = $"Station_{i + 1:D2}_{stationDistanceAU:F2}AU";

                // Scale station based on type in scaled coordinates
                Vector3 stationScale = GetStationScaleScaled(stationType);
                station.transform.localScale = stationScale;

                Debug.Log($"Created station from primitive: {station.name} (no prefab assigned)");
            }

            station.transform.SetParent(stationsContainer.transform);

            // Configure station entity (add Entity component if not present)
            Entity stationEntity = station.GetComponent<Entity>();
            if (stationEntity == null)
            {
                stationEntity = station.AddComponent<Entity>();
            }
            ConfigureEntityWithType(stationEntity, Entity.EntityType.Station);
            SetEntityRadius(stationEntity, GetStationRadius(stationType));
            SetEntityMass(stationEntity, GetStationMass(stationType));

            // Add visual distinction based on type (only for primitives)
            if (stationPrefab == null)
            {
                Renderer stationRenderer = station.GetComponent<Renderer>();
                if (stationRenderer != null)
                {
                    stationRenderer.material.color = GetStationColor(stationType);
                }
            }

            // Update station name with type
            station.name = $"{GetStationTypeName(stationType)}_{i + 1:D2}_{stationDistanceAU:F2}AU";


        }

        Debug.Log($"=== SPACE STATION CREATION COMPLETE ===");
        Debug.Log($"Created {numberOfStations} space stations");
        Debug.Log($"Stations container has {stationsContainer.transform.childCount} children");
    }

    /// <summary>
    /// Station types for variety
    /// </summary>
    enum StationType
    {
        TradingPost,    // Small commercial station
        MiningStation,  // Medium industrial station
        MilitaryBase,   // Large defensive station
        ResearchLab     // Specialized science station
    }

    /// <summary>
    /// Gets the scale for different station types (Unity units - deprecated)
    /// </summary>
    Vector3 GetStationScale(StationType type)
    {
        switch (type)
        {
            case StationType.TradingPost:
                return new Vector3(200f, 50f, 200f); // Small
            case StationType.MiningStation:
                return new Vector3(400f, 100f, 400f); // Medium
            case StationType.MilitaryBase:
                return new Vector3(800f, 200f, 800f); // Large
            case StationType.ResearchLab:
                return new Vector3(300f, 150f, 300f); // Tall and narrow
            default:
                return new Vector3(300f, 75f, 300f);
        }
    }

    /// <summary>
    /// Gets the scale for different station types in grid-local coordinates
    /// </summary>
    Vector3 GetStationScaleScaled(StationType type)
    {
        // Grid-local scale factor: 150km grid = 10km Unity units
        float gridLocalScaleFactor = 10000f / (150f * 1000f); // Unity units per meter in grid

        switch (type)
        {
            case StationType.TradingPost:
                // 200m x 50m x 200m in grid-local Unity units
                return new Vector3(200f * gridLocalScaleFactor, 50f * gridLocalScaleFactor, 200f * gridLocalScaleFactor);
            case StationType.MiningStation:
                // 400m x 100m x 400m in grid-local Unity units
                return new Vector3(400f * gridLocalScaleFactor, 100f * gridLocalScaleFactor, 400f * gridLocalScaleFactor);
            case StationType.MilitaryBase:
                // 800m x 200m x 800m in grid-local Unity units
                return new Vector3(800f * gridLocalScaleFactor, 200f * gridLocalScaleFactor, 800f * gridLocalScaleFactor);
            case StationType.ResearchLab:
                // 300m x 150m x 300m in grid-local Unity units
                return new Vector3(300f * gridLocalScaleFactor, 150f * gridLocalScaleFactor, 300f * gridLocalScaleFactor);
            default:
                return new Vector3(200f * gridLocalScaleFactor, 50f * gridLocalScaleFactor, 200f * gridLocalScaleFactor);
        }
    }

    /// <summary>
    /// Gets the radius in km for different station types
    /// </summary>
    float GetStationRadius(StationType type)
    {
        switch (type)
        {
            case StationType.TradingPost: return 0.2f;   // 200m
            case StationType.MiningStation: return 0.4f; // 400m
            case StationType.MilitaryBase: return 0.8f;  // 800m
            case StationType.ResearchLab: return 0.3f;   // 300m
            default: return 0.3f;
        }
    }

    /// <summary>
    /// Gets the mass for different station types
    /// </summary>
    float GetStationMass(StationType type)
    {
        switch (type)
        {
            case StationType.TradingPost: return 100000f;    // 100 tons
            case StationType.MiningStation: return 500000f;  // 500 tons
            case StationType.MilitaryBase: return 2000000f;  // 2000 tons
            case StationType.ResearchLab: return 300000f;    // 300 tons
            default: return 300000f;
        }
    }

    /// <summary>
    /// Gets the color for different station types
    /// </summary>
    Color GetStationColor(StationType type)
    {
        switch (type)
        {
            case StationType.TradingPost: return Color.green;    // Commercial
            case StationType.MiningStation: return Color.yellow; // Industrial
            case StationType.MilitaryBase: return Color.red;     // Military
            case StationType.ResearchLab: return Color.cyan;     // Science
            default: return Color.gray;
        }
    }

    /// <summary>
    /// Gets the name for different station types
    /// </summary>
    string GetStationTypeName(StationType type)
    {
        switch (type)
        {
            case StationType.TradingPost: return "TradingPost";
            case StationType.MiningStation: return "MiningStation";
            case StationType.MilitaryBase: return "MilitaryBase";
            case StationType.ResearchLab: return "ResearchLab";
            default: return "Station";
        }
    }
    
    /// <summary>
    /// Creates asteroid belts at various distances
    /// </summary>
    void CreateAsteroidBelts()
    {
        for (int beltIndex = 0; beltIndex < numberOfBelts; beltIndex++)
        {
            // Calculate belt distance
            float beltDistanceAU = Random.Range(beltMinDistanceAU, beltMaxDistanceAU);

            // Create belt container
            GameObject beltContainer = new GameObject($"AsteroidBelt_{beltIndex + 1:D2}_{beltDistanceAU:F1}AU");
            beltContainer.transform.SetParent(asteroidsContainer.transform);

            // Create asteroids in the belt
            for (int i = 0; i < asteroidsPerBelt; i++)
            {
                // Random position within belt
                float angle = Random.Range(0f, 360f);
                float radiusVariation = Random.Range(0.8f, 1.2f); // ±20% radius variation
                float actualRadius = beltDistanceAU * radiusVariation;

                Vector3 asteroidPosition = new Vector3(
                    Mathf.Cos(angle * Mathf.Deg2Rad) * SpaceCoordinates.AUToUnityUnits(actualRadius),
                    Random.Range(-beltThicknessAU, beltThicknessAU) * SpaceCoordinates.AUToUnityUnits(1f),
                    Mathf.Sin(angle * Mathf.Deg2Rad) * SpaceCoordinates.AUToUnityUnits(actualRadius)
                );

                // Create asteroid
                GameObject asteroid;
                float asteroidRadius = Random.Range(10f, 500f);

                // Use prefab if assigned, otherwise create primitive
                if (asteroidPrefab != null)
                {
                    asteroid = Instantiate(asteroidPrefab, asteroidPosition, Random.rotation);
                    asteroid.name = $"Asteroid_B{beltIndex + 1}_{i + 1:D3}";
                }
                else
                {
                    // Fallback to primitive sphere
                    asteroid = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                    asteroid.transform.position = asteroidPosition;
                    asteroid.transform.rotation = Random.rotation;
                    asteroid.name = $"Asteroid_B{beltIndex + 1}_{i + 1:D3}";

                    // Random asteroid size (10m to 500m radius) in grid-local coordinates
                    float gridLocalScaleFactor = 10000f / (150f * 1000f); // Unity units per meter in grid
                    float asteroidScaleGridLocal = (asteroidRadius * 2f) * gridLocalScaleFactor; // Convert meters to grid-local units
                    asteroid.transform.localScale = Vector3.one * asteroidScaleGridLocal;
                }

                asteroid.transform.SetParent(beltContainer.transform);

                // Configure asteroid entity (add Entity component if not present)
                Entity asteroidEntity = asteroid.GetComponent<Entity>();
                if (asteroidEntity == null)
                {
                    asteroidEntity = asteroid.AddComponent<Entity>();
                }
                ConfigureEntityWithType(asteroidEntity, Entity.EntityType.Asteroid);
                SetEntityRadius(asteroidEntity, asteroidRadius / 1000f); // Convert to km
                SetEntityMass(asteroidEntity, CalculateAsteroidMass(asteroidRadius));

                // Add visual distinction (only for primitives)
                if (asteroidPrefab == null)
                {
                    Renderer asteroidRenderer = asteroid.GetComponent<Renderer>();
                    if (asteroidRenderer != null)
                    {
                        // Vary asteroid colors slightly
                        float hue = Random.Range(0.05f, 0.15f); // Brown-ish colors
                        asteroidRenderer.material.color = Color.HSVToRGB(hue, 0.6f, Random.Range(0.3f, 0.7f));
                    }
                }
            }


        }
    }

    /// <summary>
    /// Calculates realistic asteroid mass based on radius
    /// </summary>
    float CalculateAsteroidMass(float radiusMeters)
    {
        // Assume rocky composition with density ~2.5 g/cm³
        float volumeCubicMeters = (4f / 3f) * Mathf.PI * Mathf.Pow(radiusMeters, 3f);
        float densityKgPerCubicMeter = 2500f; // Rocky asteroid density
        return volumeCubicMeters * densityKgPerCubicMeter;
    }
    
    /// <summary>
    /// Creates jump gates near the system edge
    /// </summary>
    void CreateJumpGates()
    {
        for (int i = 0; i < numberOfGates; i++)
        {
            // Position gates near system edge in cardinal directions
            float angle = (360f / numberOfGates) * i + Random.Range(-15f, 15f); // Slight randomization

            Vector3 gatePosition = new Vector3(
                Mathf.Cos(angle * Mathf.Deg2Rad) * SpaceCoordinates.AUToUnityUnits(gateDistanceAU),
                Random.Range(-0.1f, 0.1f) * SpaceCoordinates.AUToUnityUnits(gateDistanceAU),
                Mathf.Sin(angle * Mathf.Deg2Rad) * SpaceCoordinates.AUToUnityUnits(gateDistanceAU)
            );

            // Create gate structure
            GameObject gate;

            // Use prefab if assigned, otherwise create primitive
            if (gatePrefab != null)
            {
                gate = Instantiate(gatePrefab, gatePosition, Quaternion.LookRotation(gatePosition.normalized));
                gate.name = $"JumpGate_{i + 1:D2}_{GetGateDestination(i)}";
                Debug.Log($"Created jump gate from prefab: {gate.name}");
            }
            else
            {
                // Fallback to primitive cylinder (torus-like)
                gate = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
                gate.transform.position = gatePosition;
                gate.transform.rotation = Quaternion.LookRotation(gatePosition.normalized); // Face outward
                gate.name = $"JumpGate_{i + 1:D2}_{GetGateDestination(i)}";

                // Scale to look like a jump gate in grid-local coordinates
                float gridLocalScaleFactor = 10000f / (150f * 1000f); // Unity units per meter in grid
                gate.transform.localScale = new Vector3(2000f * gridLocalScaleFactor, 100f * gridLocalScaleFactor, 2000f * gridLocalScaleFactor); // 2km x 100m x 2km ring
                Debug.Log($"Created jump gate from primitive: {gate.name} (no prefab assigned)");
            }

            gate.transform.SetParent(gatesContainer.transform);

            // Configure gate entity (add Entity component if not present)
            Entity gateEntity = gate.GetComponent<Entity>();
            if (gateEntity == null)
            {
                gateEntity = gate.AddComponent<Entity>();
            }
            ConfigureEntityWithType(gateEntity, Entity.EntityType.Gate);
            SetEntityRadius(gateEntity, 1f); // 1km radius
            SetEntityMass(gateEntity, 1000000f); // 1000 tons

            // Add visual distinction (only for primitives)
            if (gatePrefab == null)
            {
                Renderer gateRenderer = gate.GetComponent<Renderer>();
                if (gateRenderer != null)
                {
                    gateRenderer.material.color = Color.magenta;
                    gateRenderer.material.SetColor("_EmissionColor", Color.magenta * 0.5f);
                }
            }


        }
    }

    /// <summary>
    /// Gets a destination name for jump gates
    /// </summary>
    string GetGateDestination(int gateIndex)
    {
        string[] destinations = { "Alpha_Centauri", "Vega_System", "Sirius_Sector", "Proxima_Station", "Deep_Space", "Outer_Rim", "Core_Worlds", "Frontier" };
        return destinations[gateIndex % destinations.Length];
    }

    /// <summary>
    /// Creates navigation beacons throughout the system
    /// </summary>
    void CreateNavigationBeacons()
    {
        for (int i = 0; i < numberOfBeacons; i++)
        {
            // Random distance within beacon range
            float beaconDistanceAU = Random.Range(beaconMinDistanceAU, beaconMaxDistanceAU);

            // Random position
            Vector3 randomDirection = Random.onUnitSphere;
            randomDirection.y *= 0.2f; // Slightly flattened
            randomDirection.Normalize();

            Vector3 beaconPosition = randomDirection * SpaceCoordinates.AUToUnityUnits(beaconDistanceAU);

            // Create beacon
            GameObject beacon;

            // Use prefab if assigned, otherwise create primitive
            if (beaconPrefab != null)
            {
                beacon = Instantiate(beaconPrefab, beaconPosition, Random.rotation);
                beacon.name = $"NavBeacon_{i + 1:D2}_{GetBeaconType(i)}_{beaconDistanceAU:F2}AU";
                Debug.Log($"Created navigation beacon from prefab: {beacon.name}");
            }
            else
            {
                // Fallback to primitive capsule
                beacon = GameObject.CreatePrimitive(PrimitiveType.Capsule);
                beacon.transform.position = beaconPosition;
                beacon.transform.rotation = Random.rotation;
                beacon.name = $"NavBeacon_{i + 1:D2}_{GetBeaconType(i)}_{beaconDistanceAU:F2}AU";

                // Scale beacon in grid-local coordinates
                float gridLocalScaleFactor = 10000f / (150f * 1000f); // Unity units per meter in grid
                beacon.transform.localScale = new Vector3(20f * gridLocalScaleFactor, 50f * gridLocalScaleFactor, 20f * gridLocalScaleFactor); // 20m x 50m x 20m beacon
                Debug.Log($"Created navigation beacon from primitive: {beacon.name} (no prefab assigned)");
            }

            beacon.transform.SetParent(beaconsContainer.transform);

            // Configure beacon entity (add Entity component if not present)
            Entity beaconEntity = beacon.GetComponent<Entity>();
            if (beaconEntity == null)
            {
                beaconEntity = beacon.AddComponent<Entity>();
            }
            ConfigureEntityWithType(beaconEntity, Entity.EntityType.Beacon);
            SetEntityRadius(beaconEntity, 0.01f); // 10m radius
            SetEntityMass(beaconEntity, 1000f); // 1 ton

            // Add visual distinction based on type (only for primitives)
            if (beaconPrefab == null)
            {
                Renderer beaconRenderer = beacon.GetComponent<Renderer>();
                if (beaconRenderer != null)
                {
                    beaconRenderer.material.color = GetBeaconColor(i);
                    beaconRenderer.material.SetColor("_EmissionColor", GetBeaconColor(i) * 0.3f);
                }
            }


        }
    }

    /// <summary>
    /// Gets beacon type based on index
    /// </summary>
    string GetBeaconType(int beaconIndex)
    {
        string[] types = { "Navigation", "Communication", "Warning", "Mining", "Research", "Emergency", "Trade", "Military" };
        return types[beaconIndex % types.Length];
    }

    /// <summary>
    /// Gets beacon color based on index
    /// </summary>
    Color GetBeaconColor(int beaconIndex)
    {
        Color[] colors = { Color.blue, Color.green, Color.red, Color.yellow, Color.cyan, Color.white, Color.magenta, Color.gray };
        return colors[beaconIndex % colors.Length];
    }

    /// <summary>
    /// Configures an entity with a specific type
    /// </summary>
    void ConfigureEntityWithType(Entity entity, Entity.EntityType entityType)
    {
        var config = SpaceConstants.GetEntityConfig(entityType);
        var entityTypeReflection = typeof(Entity);

        // Set entity type
        var entityTypeField = entityTypeReflection.GetField("entityType", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (entityTypeField != null)
        {
            entityTypeField.SetValue(entity, entityType);
        }

        // Set radius and mass
        var radiusField = entityTypeReflection.GetField("radiusKm", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (radiusField != null)
        {
            radiusField.SetValue(entity, config.defaultRadius);
        }

        var massField = entityTypeReflection.GetField("massKg", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (massField != null)
        {
            massField.SetValue(entity, config.defaultMass);
        }

        // Set create own grid
        var createOwnGridField = entityTypeReflection.GetField("createOwnGrid", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (createOwnGridField != null)
        {
            createOwnGridField.SetValue(entity, config.createOwnGrid);
        }
    }

    /// <summary>
    /// Sets entity radius using reflection
    /// </summary>
    void SetEntityRadius(Entity entity, float radiusKm)
    {
        var entityType = typeof(Entity);
        var radiusField = entityType.GetField("radiusKm", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (radiusField != null)
        {
            radiusField.SetValue(entity, radiusKm);
        }
    }

    /// <summary>
    /// Sets entity mass using reflection
    /// </summary>
    void SetEntityMass(Entity entity, float massKg)
    {
        var entityType = typeof(Entity);
        var massField = entityType.GetField("massKg", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (massField != null)
        {
            massField.SetValue(entity, massKg);
        }
    }
    
    /// <summary>
    /// Finalizes the scene setup
    /// </summary>
    void FinalizeSetup()
    {
        // Ensure all systems are properly initialized
        if (SpaceGridSystem.Instance != null)
        {
            SpaceGridSystem.Instance.enabled = true;
        }
        
        // Set time scale to normal
        Time.timeScale = 1f;
        

    }
    
    /// <summary>
    /// Logs setup instructions for the user
    /// </summary>
    void LogSetupInstructions()
    {
        Debug.Log("=== EVE ONLINE-INSPIRED SPACE SYSTEM READY ===");
        Debug.Log("Prefab System Status:");
        Debug.Log($"- Star: {(starPrefab != null ? "Using prefab" : "Using primitive")}");
        Debug.Log($"- Stations: {(stationPrefab != null ? "Using prefab" : "Using primitive")}");
        Debug.Log($"- Asteroids: {(asteroidPrefab != null ? "Using prefab" : "Using primitive")}");
        Debug.Log($"- Gates: {(gatePrefab != null ? "Using prefab" : "Using primitive")}");
        Debug.Log($"- Beacons: {(beaconPrefab != null ? "Using prefab" : "Using primitive")}");
        Debug.Log("(Assign prefabs in SpaceSceneSetup inspector to use custom models)");
        Debug.Log("");
        Debug.Log("Ship Controls:");
        Debug.Log("- WASD/Arrow Keys: Move ship");
        Debug.Log("- X: Brake");
        Debug.Log("");
        Debug.Log("Camera Controls:");
        Debug.Log("- Left Click + Drag: Orbit camera");
        Debug.Log("- Mouse Wheel: Zoom in/out (100m to 200km)");
        Debug.Log("- Home: Reset zoom");
        Debug.Log("");
        Debug.Log("Warp System:");
        Debug.Log("- Space: Open warp menu");
        Debug.Log("- Alt + Space: Warp to nearest station");
        Debug.Log("- Up/Down Arrows: Navigate warp menu");
        Debug.Log("- Enter: Initiate warp");
        Debug.Log("- Esc: Cancel warp/close menu");
        Debug.Log("- Tab: Toggle advanced navigation UI");
        Debug.Log("");
        Debug.Log("EVE-Style UI:");
        Debug.Log("- F1: Toggle navigation dashboard");
        Debug.Log("- M: Toggle system map");
        Debug.Log("- Right-click in space: Context menu");
        Debug.Log("- Dashboard: Drag to move, click WARP buttons");
        Debug.Log("- Entity list: Click entity names for detailed info");
        Debug.Log("- System map: Hover entities, click for dropdown menu");
        Debug.Log("- Entity Info: Tabbed window with Description and Statistics");
        Debug.Log("");
        Debug.Log("Targeting & Commands:");
        Debug.Log("- Ctrl + Left Click: Target entity");
        Debug.Log("- Right Click: Context menu (Target, Approach, Orbit, Follow, Info, Warp)");
        Debug.Log("- Tab: Cycle primary target");
        Debug.Log("- A: Approach primary target (1km)");
        Debug.Log("- O: Orbit primary target (5km)");
        Debug.Log("- F: Follow primary target (1km)");
        Debug.Log("- S: Stop current command");
        Debug.Log("- Escape: Clear all targets");
        Debug.Log("- Target windows: Stackable UI with Hull/Shields and command buttons");
        Debug.Log("");
        Debug.Log("Ship Systems:");
        Debug.Log("- Capacitor: Energy system for thrust and warp (recharges over time)");
        Debug.Log("- Hull: Ship structure (orange indicator, bottom center)");
        Debug.Log("- Shields: Energy shields (green indicator, recharges after damage)");
        Debug.Log("- Status UI: EVE-style half-circle indicators at bottom center");
        Debug.Log("");
        Debug.Log("Debug Controls:");
        Debug.Log("- F1-F5: Debug toggles");
        Debug.Log("- F9: Toggle warp debug info");
        Debug.Log("- F10: Check all entity distances");
        Debug.Log("- F11: Check coordinate system state");
        Debug.Log("- F12: Fix player entity reference");
        Debug.Log("- F: Focus on space station");
        Debug.Log("- R: Return to player");
        Debug.Log("- G: Show grid info");
        Debug.Log("===============================================");
    }
}
