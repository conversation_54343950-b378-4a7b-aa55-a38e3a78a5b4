﻿
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     TextTransform Samples/Packages/com.unity.collections/Unity.Collections.Tests/FixedStringSizedTests.tt
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using NUnit.Framework;
using Unity.Collections;
using Unity.Collections.Tests;
using UnityEngine;

namespace Unity.Collections.Tests
{

internal class FixedStringSizedTests
{

    class ScriptableObjectFixedString32Bytes : UnityEngine.ScriptableObject
    {
        public FixedString32Bytes String;
    }

    [Test]
    public void FixedString32BytesSerializes()
    {
        var a = UnityEngine.ScriptableObject.CreateInstance<ScriptableObjectFixedString32Bytes>();
        a.String = "Hello World";
        var b = UnityEngine.Object.Instantiate(a);
        Assert.AreEqual(a.String, b.String);
    }

    [TestCase("ooooooooooooooooooooooooooooo", FormatError.None, TestName="FixedString32AtMaximumSizeWorks_Almost")]
    [TestCase("oooooooooooooooooooooooooooooo", FormatError.Overflow, TestName="FixedString32AtMaximumSizeWorks_Over")]
    public void FixedString32BytesAtMaximumSizeWorks(String a, FormatError expectedError)
    {
        FixedString32Bytes aa = default;
        aa.Junk();
        var error = aa.Append(a);
        Assert.AreEqual(expectedError, error);
        if (expectedError == FormatError.None)
            aa.AssertNullTerminated();
        else
            Assert.AreEqual(0, aa.Length);
    }

    [Test]
    public unsafe void FixedString32BytesToFixedList()
    {
        FixedString32Bytes a = default;
        a.Junk();
        a.Append("0123");
        ref var aList = ref a.AsFixedList();
        Assert.IsFalse(aList.IsEmpty);
        Assert.AreEqual(4, aList.Length);
        Assert.AreEqual(a.Capacity + 1, aList.Capacity);
        Assert.AreEqual('0', aList[0]);
        Assert.AreEqual('1', aList[1]);
        Assert.AreEqual('2', aList[2]);
        Assert.AreEqual('3', aList[3]);
        aList.Add((byte)'4');
        Assert.AreEqual("01234", a);
        // because we modified it as a FixedList, it will not be null terminated!
        Assert.AreNotEqual(0, a.GetUnsafePtr()[a.Length]);
    }

    [TestCase("red")]
    [TestCase("紅色", TestName="{m}(Chinese-Red)")]
    [TestCase("George Washington")]
    [TestCase("村上春樹", TestName="{m}(HarukiMurakami)")]
    public unsafe void FixedString32BytesEqualsStringNoGC(string a)
    {
        FixedString32Bytes b = a;
        GCAllocRecorder.ValidateNoGCAllocs(() =>
        {
            b.Equals(a);
        });
    }


    [TestCase("red")]
    [TestCase("紅色", TestName="{m}(Chinese-Red)")]
    [TestCase("George Washington")]
    [TestCase("村上春樹", TestName="{m}(HarukiMurakami)")]
    public void FixedString32BytesToFixedString64Works(String a)
    {
        var b = new FixedString32Bytes(a);
        var c = new FixedString64Bytes(b);
        String d = c.ToString();
        Assert.AreEqual(a, d);
    }

    [TestCase("red")]
    [TestCase("紅色", TestName="{m}(Chinese-Red)")]
    [TestCase("George Washington")]
    [TestCase("村上春樹", TestName="{m}(HarukiMurakami)")]
    public void FixedString32BytesToFixedString128Works(String a)
    {
        var b = new FixedString32Bytes(a);
        var c = new FixedString128Bytes(b);
        String d = c.ToString();
        Assert.AreEqual(a, d);
    }

    [TestCase("red")]
    [TestCase("紅色", TestName="{m}(Chinese-Red)")]
    [TestCase("George Washington")]
    [TestCase("村上春樹", TestName="{m}(HarukiMurakami)")]
    public void FixedString32BytesToFixedString512Works(String a)
    {
        var b = new FixedString32Bytes(a);
        var c = new FixedString512Bytes(b);
        String d = c.ToString();
        Assert.AreEqual(a, d);
    }

    [TestCase("red")]
    [TestCase("紅色", TestName="{m}(Chinese-Red)")]
    [TestCase("George Washington")]
    [TestCase("村上春樹", TestName="{m}(HarukiMurakami)")]
    public void FixedString32BytesToFixedString4096Works(String a)
    {
        var b = new FixedString32Bytes(a);
        var c = new FixedString4096Bytes(b);
        String d = c.ToString();
        Assert.AreEqual(a, d);
    }

    class ScriptableObjectFixedString64Bytes : UnityEngine.ScriptableObject
    {
        public FixedString64Bytes String;
    }

    [Test]
    public void FixedString64BytesSerializes()
    {
        var a = UnityEngine.ScriptableObject.CreateInstance<ScriptableObjectFixedString64Bytes>();
        a.String = "Hello World";
        var b = UnityEngine.Object.Instantiate(a);
        Assert.AreEqual(a.String, b.String);
    }

    [TestCase("ooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooo", FormatError.None, TestName="FixedString64AtMaximumSizeWorks_Almost")]
    [TestCase("oooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooo", FormatError.Overflow, TestName="FixedString64AtMaximumSizeWorks_Over")]
    public void FixedString64BytesAtMaximumSizeWorks(String a, FormatError expectedError)
    {
        FixedString64Bytes aa = default;
        aa.Junk();
        var error = aa.Append(a);
        Assert.AreEqual(expectedError, error);
        if (expectedError == FormatError.None)
            aa.AssertNullTerminated();
        else
            Assert.AreEqual(0, aa.Length);
    }

    [Test]
    public unsafe void FixedString64BytesToFixedList()
    {
        FixedString64Bytes a = default;
        a.Junk();
        a.Append("0123");
        ref var aList = ref a.AsFixedList();
        Assert.IsFalse(aList.IsEmpty);
        Assert.AreEqual(4, aList.Length);
        Assert.AreEqual(a.Capacity + 1, aList.Capacity);
        Assert.AreEqual('0', aList[0]);
        Assert.AreEqual('1', aList[1]);
        Assert.AreEqual('2', aList[2]);
        Assert.AreEqual('3', aList[3]);
        aList.Add((byte)'4');
        Assert.AreEqual("01234", a);
        // because we modified it as a FixedList, it will not be null terminated!
        Assert.AreNotEqual(0, a.GetUnsafePtr()[a.Length]);
    }

    [TestCase("red")]
    [TestCase("紅色", TestName="{m}(Chinese-Red)")]
    [TestCase("George Washington")]
    [TestCase("村上春樹", TestName="{m}(HarukiMurakami)")]
    public unsafe void FixedString64BytesEqualsStringNoGC(string a)
    {
        FixedString64Bytes b = a;
        GCAllocRecorder.ValidateNoGCAllocs(() =>
        {
            b.Equals(a);
        });
    }


    [TestCase("red")]
    [TestCase("紅色", TestName="{m}(Chinese-Red)")]
    [TestCase("George Washington")]
    [TestCase("村上春樹", TestName="{m}(HarukiMurakami)")]
    public void FixedString64BytesToFixedString32Works(String a)
    {
        var b = new FixedString64Bytes(a);
        var c = new FixedString32Bytes(b);
        String d = c.ToString();
        Assert.AreEqual(a, d);
    }

    [TestCase("red")]
    [TestCase("紅色", TestName="{m}(Chinese-Red)")]
    [TestCase("George Washington")]
    [TestCase("村上春樹", TestName="{m}(HarukiMurakami)")]
    public void FixedString64BytesToFixedString128Works(String a)
    {
        var b = new FixedString64Bytes(a);
        var c = new FixedString128Bytes(b);
        String d = c.ToString();
        Assert.AreEqual(a, d);
    }

    [TestCase("red")]
    [TestCase("紅色", TestName="{m}(Chinese-Red)")]
    [TestCase("George Washington")]
    [TestCase("村上春樹", TestName="{m}(HarukiMurakami)")]
    public void FixedString64BytesToFixedString512Works(String a)
    {
        var b = new FixedString64Bytes(a);
        var c = new FixedString512Bytes(b);
        String d = c.ToString();
        Assert.AreEqual(a, d);
    }

    [TestCase("red")]
    [TestCase("紅色", TestName="{m}(Chinese-Red)")]
    [TestCase("George Washington")]
    [TestCase("村上春樹", TestName="{m}(HarukiMurakami)")]
    public void FixedString64BytesToFixedString4096Works(String a)
    {
        var b = new FixedString64Bytes(a);
        var c = new FixedString4096Bytes(b);
        String d = c.ToString();
        Assert.AreEqual(a, d);
    }

    class ScriptableObjectFixedString128Bytes : UnityEngine.ScriptableObject
    {
        public FixedString128Bytes String;
    }

    [Test]
    public void FixedString128BytesSerializes()
    {
        var a = UnityEngine.ScriptableObject.CreateInstance<ScriptableObjectFixedString128Bytes>();
        a.String = "Hello World";
        var b = UnityEngine.Object.Instantiate(a);
        Assert.AreEqual(a.String, b.String);
    }

    [TestCase("ooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooo", FormatError.None, TestName="FixedString128AtMaximumSizeWorks_Almost")]
    [TestCase("oooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooo", FormatError.Overflow, TestName="FixedString128AtMaximumSizeWorks_Over")]
    public void FixedString128BytesAtMaximumSizeWorks(String a, FormatError expectedError)
    {
        FixedString128Bytes aa = default;
        aa.Junk();
        var error = aa.Append(a);
        Assert.AreEqual(expectedError, error);
        if (expectedError == FormatError.None)
            aa.AssertNullTerminated();
        else
            Assert.AreEqual(0, aa.Length);
    }

    [Test]
    public unsafe void FixedString128BytesToFixedList()
    {
        FixedString128Bytes a = default;
        a.Junk();
        a.Append("0123");
        ref var aList = ref a.AsFixedList();
        Assert.IsFalse(aList.IsEmpty);
        Assert.AreEqual(4, aList.Length);
        Assert.AreEqual(a.Capacity + 1, aList.Capacity);
        Assert.AreEqual('0', aList[0]);
        Assert.AreEqual('1', aList[1]);
        Assert.AreEqual('2', aList[2]);
        Assert.AreEqual('3', aList[3]);
        aList.Add((byte)'4');
        Assert.AreEqual("01234", a);
        // because we modified it as a FixedList, it will not be null terminated!
        Assert.AreNotEqual(0, a.GetUnsafePtr()[a.Length]);
    }

    [TestCase("red")]
    [TestCase("紅色", TestName="{m}(Chinese-Red)")]
    [TestCase("George Washington")]
    [TestCase("村上春樹", TestName="{m}(HarukiMurakami)")]
    public unsafe void FixedString128BytesEqualsStringNoGC(string a)
    {
        FixedString128Bytes b = a;
        GCAllocRecorder.ValidateNoGCAllocs(() =>
        {
            b.Equals(a);
        });
    }


    [TestCase("red")]
    [TestCase("紅色", TestName="{m}(Chinese-Red)")]
    [TestCase("George Washington")]
    [TestCase("村上春樹", TestName="{m}(HarukiMurakami)")]
    public void FixedString128BytesToFixedString32Works(String a)
    {
        var b = new FixedString128Bytes(a);
        var c = new FixedString32Bytes(b);
        String d = c.ToString();
        Assert.AreEqual(a, d);
    }

    [TestCase("red")]
    [TestCase("紅色", TestName="{m}(Chinese-Red)")]
    [TestCase("George Washington")]
    [TestCase("村上春樹", TestName="{m}(HarukiMurakami)")]
    public void FixedString128BytesToFixedString64Works(String a)
    {
        var b = new FixedString128Bytes(a);
        var c = new FixedString64Bytes(b);
        String d = c.ToString();
        Assert.AreEqual(a, d);
    }

    [TestCase("red")]
    [TestCase("紅色", TestName="{m}(Chinese-Red)")]
    [TestCase("George Washington")]
    [TestCase("村上春樹", TestName="{m}(HarukiMurakami)")]
    public void FixedString128BytesToFixedString512Works(String a)
    {
        var b = new FixedString128Bytes(a);
        var c = new FixedString512Bytes(b);
        String d = c.ToString();
        Assert.AreEqual(a, d);
    }

    [TestCase("red")]
    [TestCase("紅色", TestName="{m}(Chinese-Red)")]
    [TestCase("George Washington")]
    [TestCase("村上春樹", TestName="{m}(HarukiMurakami)")]
    public void FixedString128BytesToFixedString4096Works(String a)
    {
        var b = new FixedString128Bytes(a);
        var c = new FixedString4096Bytes(b);
        String d = c.ToString();
        Assert.AreEqual(a, d);
    }

    class ScriptableObjectFixedString512Bytes : UnityEngine.ScriptableObject
    {
        public FixedString512Bytes String;
    }

    [Test]
    public void FixedString512BytesSerializes()
    {
        var a = UnityEngine.ScriptableObject.CreateInstance<ScriptableObjectFixedString512Bytes>();
        a.String = "Hello World";
        var b = UnityEngine.Object.Instantiate(a);
        Assert.AreEqual(a.String, b.String);
    }

    [TestCase("ooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooo", FormatError.None, TestName="FixedString512AtMaximumSizeWorks_Almost")]
    [TestCase("oooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooo", FormatError.Overflow, TestName="FixedString512AtMaximumSizeWorks_Over")]
    public void FixedString512BytesAtMaximumSizeWorks(String a, FormatError expectedError)
    {
        FixedString512Bytes aa = default;
        aa.Junk();
        var error = aa.Append(a);
        Assert.AreEqual(expectedError, error);
        if (expectedError == FormatError.None)
            aa.AssertNullTerminated();
        else
            Assert.AreEqual(0, aa.Length);
    }

    [Test]
    public unsafe void FixedString512BytesToFixedList()
    {
        FixedString512Bytes a = default;
        a.Junk();
        a.Append("0123");
        ref var aList = ref a.AsFixedList();
        Assert.IsFalse(aList.IsEmpty);
        Assert.AreEqual(4, aList.Length);
        Assert.AreEqual(a.Capacity + 1, aList.Capacity);
        Assert.AreEqual('0', aList[0]);
        Assert.AreEqual('1', aList[1]);
        Assert.AreEqual('2', aList[2]);
        Assert.AreEqual('3', aList[3]);
        aList.Add((byte)'4');
        Assert.AreEqual("01234", a);
        // because we modified it as a FixedList, it will not be null terminated!
        Assert.AreNotEqual(0, a.GetUnsafePtr()[a.Length]);
    }

    [TestCase("red")]
    [TestCase("紅色", TestName="{m}(Chinese-Red)")]
    [TestCase("George Washington")]
    [TestCase("村上春樹", TestName="{m}(HarukiMurakami)")]
    public unsafe void FixedString512BytesEqualsStringNoGC(string a)
    {
        FixedString512Bytes b = a;
        GCAllocRecorder.ValidateNoGCAllocs(() =>
        {
            b.Equals(a);
        });
    }


    [TestCase("red")]
    [TestCase("紅色", TestName="{m}(Chinese-Red)")]
    [TestCase("George Washington")]
    [TestCase("村上春樹", TestName="{m}(HarukiMurakami)")]
    public void FixedString512BytesToFixedString32Works(String a)
    {
        var b = new FixedString512Bytes(a);
        var c = new FixedString32Bytes(b);
        String d = c.ToString();
        Assert.AreEqual(a, d);
    }

    [TestCase("red")]
    [TestCase("紅色", TestName="{m}(Chinese-Red)")]
    [TestCase("George Washington")]
    [TestCase("村上春樹", TestName="{m}(HarukiMurakami)")]
    public void FixedString512BytesToFixedString64Works(String a)
    {
        var b = new FixedString512Bytes(a);
        var c = new FixedString64Bytes(b);
        String d = c.ToString();
        Assert.AreEqual(a, d);
    }

    [TestCase("red")]
    [TestCase("紅色", TestName="{m}(Chinese-Red)")]
    [TestCase("George Washington")]
    [TestCase("村上春樹", TestName="{m}(HarukiMurakami)")]
    public void FixedString512BytesToFixedString128Works(String a)
    {
        var b = new FixedString512Bytes(a);
        var c = new FixedString128Bytes(b);
        String d = c.ToString();
        Assert.AreEqual(a, d);
    }

    [TestCase("red")]
    [TestCase("紅色", TestName="{m}(Chinese-Red)")]
    [TestCase("George Washington")]
    [TestCase("村上春樹", TestName="{m}(HarukiMurakami)")]
    public void FixedString512BytesToFixedString4096Works(String a)
    {
        var b = new FixedString512Bytes(a);
        var c = new FixedString4096Bytes(b);
        String d = c.ToString();
        Assert.AreEqual(a, d);
    }

    class ScriptableObjectFixedString4096Bytes : UnityEngine.ScriptableObject
    {
        public FixedString4096Bytes String;
    }

    [Test]
    public void FixedString4096BytesSerializes()
    {
        var a = UnityEngine.ScriptableObject.CreateInstance<ScriptableObjectFixedString4096Bytes>();
        a.String = "Hello World";
        var b = UnityEngine.Object.Instantiate(a);
        Assert.AreEqual(a.String, b.String);
    }

    [TestCase("ooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooo", FormatError.None, TestName="FixedString4096AtMaximumSizeWorks_Almost")]
    [TestCase("oooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooo", FormatError.Overflow, TestName="FixedString4096AtMaximumSizeWorks_Over")]
    public void FixedString4096BytesAtMaximumSizeWorks(String a, FormatError expectedError)
    {
        FixedString4096Bytes aa = default;
        aa.Junk();
        var error = aa.Append(a);
        Assert.AreEqual(expectedError, error);
        if (expectedError == FormatError.None)
            aa.AssertNullTerminated();
        else
            Assert.AreEqual(0, aa.Length);
    }

    [Test]
    public unsafe void FixedString4096BytesToFixedList()
    {
        FixedString4096Bytes a = default;
        a.Junk();
        a.Append("0123");
        ref var aList = ref a.AsFixedList();
        Assert.IsFalse(aList.IsEmpty);
        Assert.AreEqual(4, aList.Length);
        Assert.AreEqual(a.Capacity + 1, aList.Capacity);
        Assert.AreEqual('0', aList[0]);
        Assert.AreEqual('1', aList[1]);
        Assert.AreEqual('2', aList[2]);
        Assert.AreEqual('3', aList[3]);
        aList.Add((byte)'4');
        Assert.AreEqual("01234", a);
        // because we modified it as a FixedList, it will not be null terminated!
        Assert.AreNotEqual(0, a.GetUnsafePtr()[a.Length]);
    }

    [TestCase("red")]
    [TestCase("紅色", TestName="{m}(Chinese-Red)")]
    [TestCase("George Washington")]
    [TestCase("村上春樹", TestName="{m}(HarukiMurakami)")]
    public unsafe void FixedString4096BytesEqualsStringNoGC(string a)
    {
        FixedString4096Bytes b = a;
        GCAllocRecorder.ValidateNoGCAllocs(() =>
        {
            b.Equals(a);
        });
    }


    [TestCase("red")]
    [TestCase("紅色", TestName="{m}(Chinese-Red)")]
    [TestCase("George Washington")]
    [TestCase("村上春樹", TestName="{m}(HarukiMurakami)")]
    public void FixedString4096BytesToFixedString32Works(String a)
    {
        var b = new FixedString4096Bytes(a);
        var c = new FixedString32Bytes(b);
        String d = c.ToString();
        Assert.AreEqual(a, d);
    }

    [TestCase("red")]
    [TestCase("紅色", TestName="{m}(Chinese-Red)")]
    [TestCase("George Washington")]
    [TestCase("村上春樹", TestName="{m}(HarukiMurakami)")]
    public void FixedString4096BytesToFixedString64Works(String a)
    {
        var b = new FixedString4096Bytes(a);
        var c = new FixedString64Bytes(b);
        String d = c.ToString();
        Assert.AreEqual(a, d);
    }

    [TestCase("red")]
    [TestCase("紅色", TestName="{m}(Chinese-Red)")]
    [TestCase("George Washington")]
    [TestCase("村上春樹", TestName="{m}(HarukiMurakami)")]
    public void FixedString4096BytesToFixedString128Works(String a)
    {
        var b = new FixedString4096Bytes(a);
        var c = new FixedString128Bytes(b);
        String d = c.ToString();
        Assert.AreEqual(a, d);
    }

    [TestCase("red")]
    [TestCase("紅色", TestName="{m}(Chinese-Red)")]
    [TestCase("George Washington")]
    [TestCase("村上春樹", TestName="{m}(HarukiMurakami)")]
    public void FixedString4096BytesToFixedString512Works(String a)
    {
        var b = new FixedString4096Bytes(a);
        var c = new FixedString512Bytes(b);
        String d = c.ToString();
        Assert.AreEqual(a, d);
    }

}

}
