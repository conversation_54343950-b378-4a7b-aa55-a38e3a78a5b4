using UnityEngine;

/// <summary>
/// Right-click context menu for entities
/// </summary>
public class EntityContextMenu : MonoBehaviour
{
    [Header("Menu Settings")]
    [SerializeField] private float menuWidth = 150f;
    [SerializeField] private float itemHeight = 25f;
    [SerializeField] private LayerMask entityLayers = -1;
    
    // Menu state
    private bool isMenuVisible = false;
    private Vector2 menuPosition;
    private Entity targetEntity = null;
    private string[] menuItems = { "Target", "Approach (1km)", "Orbit (5km)", "Follow (1km)", "Show Info", "Warp To" };
    
    // UI Styles
    private GUIStyle menuStyle;
    private GUIStyle itemStyle;
    private GUIStyle hoverStyle;
    private bool stylesInitialized = false;
    
    void Update()
    {
        HandleContextMenuInput();
    }
    
    void HandleContextMenuInput()
    {
        // Right-click to open context menu
        if (Input.GetMouseButtonDown(1))
        {
            TryOpenContextMenu();
        }
        
        // Close menu on left click or escape
        if (Input.GetMouseButtonDown(0) || Input.GetKeyDown(KeyCode.Escape))
        {
            CloseContextMenu();
        }
    }
    
    void TryOpenContextMenu()
    {
        Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
        RaycastHit hit;
        
        if (Physics.Raycast(ray, out hit, Mathf.Infinity, entityLayers))
        {
            Entity entity = hit.collider.GetComponent<Entity>();
            if (entity != null && !IsPlayerEntity(entity))
            {
                OpenContextMenu(entity, Input.mousePosition);
            }
            else
            {
                CloseContextMenu();
            }
        }
        else
        {
            CloseContextMenu();
        }
    }
    
    void OpenContextMenu(Entity entity, Vector2 screenPosition)
    {
        targetEntity = entity;
        menuPosition = new Vector2(screenPosition.x, Screen.height - screenPosition.y); // Convert to GUI coordinates
        isMenuVisible = true;
        
        // Adjust position to keep menu on screen
        float menuHeight = menuItems.Length * itemHeight;
        if (menuPosition.x + menuWidth > Screen.width)
            menuPosition.x = Screen.width - menuWidth;
        if (menuPosition.y + menuHeight > Screen.height)
            menuPosition.y = Screen.height - menuHeight;
    }
    
    void CloseContextMenu()
    {
        isMenuVisible = false;
        targetEntity = null;
    }
    
    void OnGUI()
    {
        if (!isMenuVisible || targetEntity == null) return;
        
        InitializeStyles();
        DrawContextMenu();
    }
    
    void InitializeStyles()
    {
        if (stylesInitialized) return;
        
        // Menu background style
        menuStyle = new GUIStyle(GUI.skin.box);
        menuStyle.normal.background = CreateColorTexture(new Color(0.1f, 0.1f, 0.15f, 0.95f));
        menuStyle.border = new RectOffset(2, 2, 2, 2);
        
        // Menu item style
        itemStyle = new GUIStyle(GUI.skin.button);
        itemStyle.normal.background = CreateColorTexture(new Color(0.15f, 0.15f, 0.2f, 0.8f));
        itemStyle.normal.textColor = Color.white;
        itemStyle.fontSize = 12;
        itemStyle.alignment = TextAnchor.MiddleLeft;
        itemStyle.padding = new RectOffset(10, 10, 5, 5);
        
        // Hover style
        hoverStyle = new GUIStyle(itemStyle);
        hoverStyle.normal.background = CreateColorTexture(new Color(0.3f, 0.4f, 0.5f, 0.9f));
        hoverStyle.hover.background = CreateColorTexture(new Color(0.3f, 0.4f, 0.5f, 0.9f));
        
        stylesInitialized = true;
    }
    
    Texture2D CreateColorTexture(Color color)
    {
        Texture2D texture = new Texture2D(1, 1);
        texture.SetPixel(0, 0, color);
        texture.Apply();
        return texture;
    }
    
    void DrawContextMenu()
    {
        float menuHeight = menuItems.Length * itemHeight;
        Rect menuRect = new Rect(menuPosition.x, menuPosition.y, menuWidth, menuHeight);
        
        // Draw menu background
        GUI.Box(menuRect, "", menuStyle);
        
        // Draw menu items
        for (int i = 0; i < menuItems.Length; i++)
        {
            Rect itemRect = new Rect(menuPosition.x, menuPosition.y + (i * itemHeight), menuWidth, itemHeight);
            
            // Check if item is disabled
            bool isDisabled = IsMenuItemDisabled(i);
            GUI.enabled = !isDisabled;
            
            // Use hover style if mouse is over item
            GUIStyle currentStyle = itemRect.Contains(Event.current.mousePosition) ? hoverStyle : itemStyle;
            
            if (GUI.Button(itemRect, menuItems[i], currentStyle))
            {
                ExecuteMenuAction(i);
                CloseContextMenu();
            }
            
            GUI.enabled = true;
        }
    }
    
    bool IsMenuItemDisabled(int itemIndex)
    {
        switch (itemIndex)
        {
            case 0: // Target
                return TargetingSystem.Instance != null && TargetingSystem.Instance.IsTargeted(targetEntity);
            case 1: // Approach
            case 2: // Orbit
            case 3: // Follow
                return !IsInSameGrid(targetEntity);
            case 4: // Show Info
                return false;
            case 5: // Warp To
                return IsInSameGrid(targetEntity); // Can't warp to entities in same grid
            default:
                return false;
        }
    }
    
    void ExecuteMenuAction(int actionIndex)
    {
        if (targetEntity == null) return;
        
        switch (actionIndex)
        {
            case 0: // Target
                if (TargetingSystem.Instance != null)
                {
                    TargetingSystem.Instance.AddTarget(targetEntity);
                }
                break;
                
            case 1: // Approach (1km)
                var commandSystem1 = FindObjectOfType<ShipCommandSystem>();
                if (commandSystem1 != null)
                {
                    commandSystem1.StartApproachCommand(targetEntity, 1000f);
                }
                break;
                
            case 2: // Orbit (5km)
                var commandSystem2 = FindObjectOfType<ShipCommandSystem>();
                if (commandSystem2 != null)
                {
                    commandSystem2.StartOrbitCommand(targetEntity, 5000f);
                }
                break;
                
            case 3: // Follow (1km)
                var commandSystem3 = FindObjectOfType<ShipCommandSystem>();
                if (commandSystem3 != null)
                {
                    commandSystem3.StartFollowCommand(targetEntity, 1000f);
                }
                break;
                
            case 4: // Show Info
                var infoWindow = FindObjectOfType<EntityInfoWindow>();
                if (infoWindow != null)
                {
                    infoWindow.ShowEntityInfo(targetEntity);
                }
                break;
                
            case 5: // Warp To
                var shipController = FindObjectOfType<SpaceshipController>();
                if (shipController != null)
                {
                    shipController.WarpToEntity(targetEntity);
                }
                break;
        }
    }
    
    bool IsPlayerEntity(Entity entity)
    {
        return entity.GetComponent<SpaceshipController>() != null;
    }
    
    bool IsInSameGrid(Entity entity)
    {
        if (SpaceCoordinateManager.Instance == null || SpaceCoordinateManager.Instance.PlayerEntity == null)
            return false;
        
        return SpaceCoordinateManager.Instance.AreEntitiesInSameGrid(
            SpaceCoordinateManager.Instance.PlayerEntity, entity);
    }
}
