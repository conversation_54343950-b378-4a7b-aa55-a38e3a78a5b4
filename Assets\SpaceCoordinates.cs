using UnityEngine;

/// <summary>
/// Utility class for handling space coordinate conversions and calculations
/// Provides methods for converting between different coordinate systems used in the space game
/// </summary>
public static class SpaceCoordinates
{
    // Conversion constants
    public const float METERS_PER_KM = 1000f;
    public const float KM_PER_AU = 149597870.7f; // Astronomical Unit in kilometers
    public const float UNITY_UNITS_PER_AU = 1000000f; // How many Unity units represent 1 AU
    public const float UNITY_UNITS_PER_KM = UNITY_UNITS_PER_AU / KM_PER_AU;
    
    // Grid system constants
    public const float DEFAULT_GRID_RADIUS_KM = 150f;
    public const float MAX_GRID_RADIUS_KM = 500f;
    public const float MAX_RENDER_DISTANCE_KM = 50f;
    
    /// <summary>
    /// Converts kilometers to Unity units
    /// </summary>
    public static float KilometersToUnityUnits(float kilometers)
    {
        return kilometers * UNITY_UNITS_PER_KM;
    }
    
    /// <summary>
    /// Converts Unity units to kilometers
    /// </summary>
    public static float UnityUnitsToKilometers(float unityUnits)
    {
        return unityUnits / UNITY_UNITS_PER_KM;
    }
    
    /// <summary>
    /// Converts Astronomical Units to Unity units
    /// </summary>
    public static float AUToUnityUnits(float au)
    {
        return au * UNITY_UNITS_PER_AU;
    }
    
    /// <summary>
    /// Converts Unity units to Astronomical Units
    /// </summary>
    public static float UnityUnitsToAU(float unityUnits)
    {
        return unityUnits / UNITY_UNITS_PER_AU;
    }
    
    /// <summary>
    /// Converts meters to Unity units
    /// </summary>
    public static float MetersToUnityUnits(float meters)
    {
        return KilometersToUnityUnits(meters / METERS_PER_KM);
    }
    
    /// <summary>
    /// Converts Unity units to meters
    /// </summary>
    public static float UnityUnitsToMeters(float unityUnits)
    {
        return UnityUnitsToKilometers(unityUnits) * METERS_PER_KM;
    }
    
    /// <summary>
    /// Calculates the distance between two positions in kilometers
    /// </summary>
    public static float DistanceInKilometers(Vector3 pos1, Vector3 pos2)
    {
        float distanceInUnityUnits = Vector3.Distance(pos1, pos2);
        return UnityUnitsToKilometers(distanceInUnityUnits);
    }
    
    /// <summary>
    /// Calculates the distance between two positions in AU
    /// </summary>
    public static float DistanceInAU(Vector3 pos1, Vector3 pos2)
    {
        float distanceInUnityUnits = Vector3.Distance(pos1, pos2);
        return UnityUnitsToAU(distanceInUnityUnits);
    }
    
    /// <summary>
    /// Formats a distance value for display
    /// </summary>
    public static string FormatDistance(float distanceInUnityUnits)
    {
        float meters = UnityUnitsToMeters(distanceInUnityUnits);
        float kilometers = meters / METERS_PER_KM;
        float au = UnityUnitsToAU(distanceInUnityUnits);
        
        if (meters < 1000f)
            return $"{meters:F1} m";
        else if (kilometers < 1000f)
            return $"{kilometers:F2} km";
        else if (au < 1f)
            return $"{kilometers:F0} km";
        else
            return $"{au:F3} AU";
    }
    
    /// <summary>
    /// Formats a speed value for display
    /// </summary>
    public static string FormatSpeed(float speedInUnityUnitsPerSecond)
    {
        float metersPerSecond = UnityUnitsToMeters(speedInUnityUnitsPerSecond);
        float kilometersPerSecond = metersPerSecond / METERS_PER_KM;
        
        if (metersPerSecond < 1000f)
            return $"{metersPerSecond:F1} m/s";
        else if (kilometersPerSecond < 1000f)
            return $"{kilometersPerSecond:F2} km/s";
        else
            return $"{kilometersPerSecond:F0} km/s";
    }
    
    /// <summary>
    /// Calculates the appropriate grid radius for a given number of entities
    /// </summary>
    public static float CalculateOptimalGridRadius(int entityCount, float baseRadius = DEFAULT_GRID_RADIUS_KM)
    {
        // Scale grid size based on entity density
        float scaleFactor = Mathf.Sqrt(entityCount / 10f); // Base assumption: 10 entities per base grid
        float optimalRadius = baseRadius * Mathf.Max(1f, scaleFactor);
        
        return Mathf.Min(optimalRadius, MAX_GRID_RADIUS_KM);
    }
    
    /// <summary>
    /// Checks if a position is within the maximum render distance
    /// </summary>
    public static bool IsWithinRenderDistance(Vector3 position, Vector3 referencePosition)
    {
        float distanceKm = DistanceInKilometers(position, referencePosition);
        return distanceKm <= MAX_RENDER_DISTANCE_KM;
    }
    
    /// <summary>
    /// Calculates the Level of Detail based on distance
    /// </summary>
    public static int CalculateLOD(float distanceInUnityUnits)
    {
        float distanceKm = UnityUnitsToKilometers(distanceInUnityUnits);
        
        if (distanceKm <= 1f) return 0; // Full detail
        if (distanceKm <= 10f) return 1; // Medium detail
        if (distanceKm <= 50f) return 2; // Low detail
        return 3; // Very low detail or hidden
    }
    
    /// <summary>
    /// Calculates the scale factor for rendering at a given distance
    /// </summary>
    public static float CalculateScaleFactor(float distanceInUnityUnits)
    {
        float distanceKm = UnityUnitsToKilometers(distanceInUnityUnits);
        
        if (distanceKm <= MAX_RENDER_DISTANCE_KM)
            return 1f;
        
        // Scale down objects beyond render distance
        return MAX_RENDER_DISTANCE_KM / distanceKm;
    }
    
    /// <summary>
    /// Converts a world position to grid coordinates
    /// </summary>
    public static Vector2Int WorldToGridCoordinates(Vector3 worldPosition, float gridSize = DEFAULT_GRID_RADIUS_KM)
    {
        float gridSizeInUnityUnits = KilometersToUnityUnits(gridSize * 2f); // Diameter
        
        int gridX = Mathf.FloorToInt(worldPosition.x / gridSizeInUnityUnits);
        int gridZ = Mathf.FloorToInt(worldPosition.z / gridSizeInUnityUnits);
        
        return new Vector2Int(gridX, gridZ);
    }
    
    /// <summary>
    /// Converts grid coordinates to world position (center of grid)
    /// </summary>
    public static Vector3 GridToWorldCoordinates(Vector2Int gridCoordinates, float gridSize = DEFAULT_GRID_RADIUS_KM)
    {
        float gridSizeInUnityUnits = KilometersToUnityUnits(gridSize * 2f); // Diameter
        
        float worldX = (gridCoordinates.x + 0.5f) * gridSizeInUnityUnits;
        float worldZ = (gridCoordinates.y + 0.5f) * gridSizeInUnityUnits;
        
        return new Vector3(worldX, 0f, worldZ);
    }
    
    /// <summary>
    /// Calculates the warp time between two positions
    /// </summary>
    public static float CalculateWarpTime(Vector3 startPos, Vector3 endPos, float warpSpeed = 10000f)
    {
        float distanceKm = DistanceInKilometers(startPos, endPos);
        float warpSpeedKmPerSecond = warpSpeed / METERS_PER_KM;
        
        return distanceKm / warpSpeedKmPerSecond;
    }
    
    /// <summary>
    /// Generates a random position within a spherical volume
    /// </summary>
    public static Vector3 RandomPositionInSphere(Vector3 center, float radiusKm)
    {
        Vector3 randomDirection = Random.insideUnitSphere;
        float radiusInUnityUnits = KilometersToUnityUnits(radiusKm);
        
        return center + randomDirection * radiusInUnityUnits;
    }
    
    /// <summary>
    /// Generates a random position within a disk (flattened sphere)
    /// </summary>
    public static Vector3 RandomPositionInDisk(Vector3 center, float radiusKm, float heightKm = 1f)
    {
        Vector2 randomCircle = Random.insideUnitCircle;
        float randomHeight = Random.Range(-heightKm / 2f, heightKm / 2f);
        
        float radiusInUnityUnits = KilometersToUnityUnits(radiusKm);
        float heightInUnityUnits = KilometersToUnityUnits(randomHeight);
        
        Vector3 offset = new Vector3(
            randomCircle.x * radiusInUnityUnits,
            heightInUnityUnits,
            randomCircle.y * radiusInUnityUnits
        );
        
        return center + offset;
    }
    
    /// <summary>
    /// Clamps a position to stay within the solar system bounds
    /// </summary>
    public static Vector3 ClampToSolarSystem(Vector3 position, float solarSystemRadiusAU = 18f)
    {
        float maxDistanceInUnityUnits = AUToUnityUnits(solarSystemRadiusAU);
        
        if (position.magnitude > maxDistanceInUnityUnits)
        {
            return position.normalized * maxDistanceInUnityUnits;
        }
        
        return position;
    }
}
