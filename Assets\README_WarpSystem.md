# EVE Online-Inspired Warp System

The enhanced warp system provides EVE Online-style navigation with multiple destination types, distance calculations in AU/km, and comprehensive ship positioning information.

## 🚀 Features

### Warp Destination Types
- **🏭 Stations** - Major structures for docking and services
- **🪐 Planets** - Celestial bodies with gravity wells
- **⭐ Stars** - System centers with massive gravity wells
- **🚪 Gates** - Jump gates for inter-system travel
- **📡 Beacons** - Navigation markers and communication relays
- **🪨 Asteroids** - Large asteroid mining sites (>100m radius)

### Distance Display
- **Meters** - For close objects (<1km): "500 m"
- **Kilometers** - For medium distances (1km-1000km): "15.5 km"
- **Astronomical Units** - For large distances (>0.001 AU): "2.456 AU"

### Ship Position Tracking
- **Universal Coordinates** - True position in the solar system
- **Grid Information** - Current spatial grid and entity count
- **Real-time Updates** - Live distance calculations to all destinations

## 🎮 Controls

### Basic Warp Controls
- **Space** - Open warp destination menu
- **↑↓ / W/S** - Navigate through destinations
- **Enter** - Initiate warp to selected destination
- **Esc** - Cancel warp or close menu
- **Alt + Space** - Quick warp to nearest station

### Advanced Navigation UI
- **Tab** - Toggle advanced navigation computer
- **Mouse Click** - Click "WARP" buttons in advanced UI
- **Scroll** - Navigate through destination list

### Warp Process
1. **Selection** - Choose destination from menu
2. **Alignment** - Ship aligns to target (3 seconds)
3. **Warp** - High-speed travel to destination
4. **Exit** - Automatic exit at safe distance from target

## 📊 UI Components

### Basic Warp Menu
- Destination list with type icons
- Distance in appropriate units (m/km/AU)
- Availability status (in range/too close)
- Navigation instructions

### Advanced Navigation Computer (Tab)
- **Ship Status** - Position, speed, grid information
- **Destination List** - Scrollable list with detailed info
- **Warp Time** - Estimated travel time to each destination
- **Type Colors** - Color-coded destination types
- **Quick Warp** - One-click warp buttons

### Status Overlays
- **Speed Display** - Current velocity or warp status
- **Position Display** - Distance from system origin
- **Warp Status** - Alignment progress or warp destination
- **Progress Bar** - Visual alignment indicator

## 🛠 Technical Implementation

### Warp Destination Detection
```csharp
// Automatically finds all valid warp destinations
Entity[] allEntities = FindObjectsOfType<Entity>();
foreach (Entity entity in allEntities)
{
    if (IsValidWarpDestination(entity))
    {
        availableDestinations.Add(new WarpDestination(entity));
    }
}
```

### Distance Calculations
```csharp
// Real-time distance updates
float distanceInUnityUnits = Vector3.Distance(Vector3.zero, entity.transform.position);
distanceKm = SpaceCoordinates.UnityUnitsToKilometers(distanceInUnityUnits);
distanceAU = SpaceCoordinates.UnityUnitsToAU(distanceInUnityUnits);
```

### Warp Safety Distances
- **Stations**: 5km exit distance
- **Planets**: 2x planet radius
- **Stars**: 5x star radius (safe distance)
- **Gates**: 2km exit distance
- **Beacons**: 1km exit distance
- **Asteroids**: 500m exit distance

## 🎯 Warp Phases

### 1. Alignment Phase
- **Duration**: 3 seconds (configurable)
- **Visual**: Ship turns yellow, rotates toward target
- **Cancellable**: Press Esc to abort
- **Progress**: Visual progress bar and percentage

### 2. Warp Phase
- **Speed**: 10 km/s (configurable)
- **Visual**: Ship turns cyan, high-speed movement
- **Automatic**: No player input required
- **Safety**: Auto-exit at calculated safe distance

### 3. Exit Phase
- **Position**: Safe distance from destination
- **Status**: All momentum stopped
- **Ready**: Immediately ready for normal flight

## 📋 Configuration Options

### SpaceshipController Settings
```csharp
[Header("Warp Settings")]
public float warpSpeed = 10000f;        // 10km/s warp speed
public float warpAlignTime = 3f;        // 3 second alignment
public float warpMinDistance = 1000f;   // 1km minimum warp distance
```

### WarpUI Settings
```csharp
[Header("UI Configuration")]
public bool enableAdvancedUI = true;           // Enable Tab UI
public int maxDisplayedDestinations = 10;      // Limit menu size
public bool showWarpTime = true;               // Show travel time
public bool showDistanceInAU = true;           // Show AU distances
```

## 🔧 Integration

### Adding to Existing Ships
```csharp
// Add to any ship GameObject
SpaceshipController controller = ship.AddComponent<SpaceshipController>();
WarpUI warpUI = ship.AddComponent<WarpUI>();
Entity entity = ship.AddComponent<Entity>();
entity.isPlayer = true; // For player ships
```

### Creating Warp Destinations
```csharp
// Any Entity with appropriate type becomes a warp destination
Entity station = stationObject.AddComponent<Entity>();
station.entityType = Entity.EntityType.Station;
station.radiusKm = 2.0f; // 2km radius station
```

## 🎨 Visual Feedback

### Ship Colors
- **White** - Normal flight
- **Yellow** - Aligning for warp
- **Cyan** - Warping

### Gizmos (Editor)
- **Green Ray** - Current velocity vector
- **Yellow Sphere** - Warp target during alignment
- **Cyan Sphere** - Warp target during warp
- **Blue/Red Cubes** - Available destinations (in range/out of range)

## 🚀 Usage Examples

### Quick Station Warp
1. Press **Alt + Space** for instant warp to nearest station
2. Ship automatically aligns and warps
3. Exits 5km from station

### Manual Destination Selection
1. Press **Space** to open warp menu
2. Use **↑↓** to select destination
3. Press **Enter** to initiate warp
4. Watch alignment progress
5. Automatic warp and exit

### Advanced Navigation
1. Press **Tab** to open navigation computer
2. View detailed destination information
3. Click **WARP** button for instant warp
4. Monitor ship status and position

## 🎯 Ready to Use!

The warp system is fully integrated and ready for use. It automatically detects all valid destinations, calculates distances in appropriate units, and provides comprehensive navigation information just like EVE Online's system.