using UnityEngine;

/// <summary>
/// EVE Online-inspired camera controller that handles scaling and zoom levels
/// Works with the SpaceGridSystem to provide appropriate zoom and scaling
/// </summary>
public class SpaceCameraController : MonoBehaviour
{
    [Header("Zoom Configuration")]
    [SerializeField] private float defaultZoom = 1000f; // 1km default
    [SerializeField] private float minZoom = 100f; // 100m minimum
    [SerializeField] private float maxZoom = 200000f; // 200km maximum
    [SerializeField] private float zoomSpeed = 2f;
    [SerializeField] private float zoomSmoothTime = 0.2f;
    
    [Header("Camera Scaling")]
    [SerializeField] private float nearClipPlane = 0.1f;
    [SerializeField] private float farClipPlane = 1000000f; // 1000km
    [SerializeField] private bool useLogarithmicScaling = true;
    
    [Header("UI Scaling")]
    [SerializeField] private float uiScaleMultiplier = 1f;
    [SerializeField] private bool scaleUIWithZoom = true;
    
    // Camera state
    private Camera mainCamera;
    private float currentZoom;
    private float targetZoom;
    private float zoomVelocity;
    
    // Scaling factors
    private float currentScaleFactor = 1f;
    private float baseFieldOfView;
    
    // References
    private OrbitCamera orbitCamera;
    private Transform playerTransform;
    
    void Start()
    {
        Initialize();
    }
    
    void Update()
    {
        HandleZoomInput();
        UpdateCameraScaling();
        UpdateZoom();
    }
    
    /// <summary>
    /// Initializes the camera controller
    /// </summary>
    void Initialize()
    {
        mainCamera = GetComponent<Camera>();
        if (mainCamera == null)
        {
            Debug.LogError("SpaceCameraController: No Camera component found!");
            return;
        }
        
        orbitCamera = GetComponent<OrbitCamera>();
        baseFieldOfView = mainCamera.fieldOfView;
        
        // Set initial zoom
        currentZoom = targetZoom = defaultZoom;
        
        // Find player
        if (SpaceGridSystem.Instance != null && SpaceGridSystem.Instance.PlayerGrid != null)
        {
            var playerEntity = SpaceGridSystem.Instance.PlayerGrid.GetEntities().Find(e => e.IsPlayer);
            if (playerEntity != null)
            {
                playerTransform = playerEntity.transform;
            }
        }
        
        // Configure camera for space rendering
        ConfigureCameraForSpace();
    }
    
    /// <summary>
    /// Configures camera settings optimized for space rendering
    /// </summary>
    void ConfigureCameraForSpace()
    {
        mainCamera.nearClipPlane = nearClipPlane;
        mainCamera.farClipPlane = farClipPlane;
        
        // Enable HDR for better space visuals
        mainCamera.allowHDR = true;
        
        // Set background to space
        mainCamera.clearFlags = CameraClearFlags.Skybox;
    }
    
    /// <summary>
    /// Handles zoom input from mouse wheel and keyboard
    /// </summary>
    void HandleZoomInput()
    {
        float scrollInput = Input.GetAxis("Mouse ScrollWheel");
        
        if (Mathf.Abs(scrollInput) > 0.01f)
        {
            // Logarithmic zoom scaling for better feel across large ranges
            float zoomMultiplier = useLogarithmicScaling ? 
                Mathf.Pow(1.1f, scrollInput * zoomSpeed * 10f) : 
                (1f + scrollInput * zoomSpeed);
            
            targetZoom *= zoomMultiplier;
            targetZoom = Mathf.Clamp(targetZoom, minZoom, maxZoom);
        }
        
        // Keyboard zoom controls
        if (Input.GetKey(KeyCode.Plus) || Input.GetKey(KeyCode.KeypadPlus))
        {
            targetZoom *= 0.99f;
            targetZoom = Mathf.Max(targetZoom, minZoom);
        }
        if (Input.GetKey(KeyCode.Minus) || Input.GetKey(KeyCode.KeypadMinus))
        {
            targetZoom *= 1.01f;
            targetZoom = Mathf.Min(targetZoom, maxZoom);
        }
        
        // Reset to default zoom
        if (Input.GetKeyDown(KeyCode.Home))
        {
            targetZoom = defaultZoom;
        }
    }
    
    /// <summary>
    /// Updates camera scaling based on current zoom level
    /// </summary>
    void UpdateCameraScaling()
    {
        // Calculate scale factor based on zoom level
        float normalizedZoom = Mathf.InverseLerp(minZoom, maxZoom, currentZoom);
        
        if (useLogarithmicScaling)
        {
            // Logarithmic scaling for better distribution across zoom range
            currentScaleFactor = Mathf.Pow(10f, Mathf.Lerp(-1f, 3f, normalizedZoom));
        }
        else
        {
            // Linear scaling
            currentScaleFactor = Mathf.Lerp(0.1f, 1000f, normalizedZoom);
        }
        
        // Apply scaling to camera properties
        ApplyCameraScaling();
    }
    
    /// <summary>
    /// Applies scaling to camera properties
    /// </summary>
    void ApplyCameraScaling()
    {
        // Adjust field of view based on zoom (optional)
        float fovMultiplier = Mathf.Lerp(1.2f, 0.8f, Mathf.InverseLerp(minZoom, maxZoom, currentZoom));
        mainCamera.fieldOfView = baseFieldOfView * fovMultiplier;
        
        // Adjust near/far clip planes based on scale
        mainCamera.nearClipPlane = nearClipPlane * currentScaleFactor;
        mainCamera.farClipPlane = farClipPlane * currentScaleFactor;
        
        // Update orbit camera distance if available
        if (orbitCamera != null)
        {
            orbitCamera.SetDistance(currentZoom);
        }
    }
    
    /// <summary>
    /// Smoothly updates the current zoom towards the target
    /// </summary>
    void UpdateZoom()
    {
        currentZoom = Mathf.SmoothDamp(currentZoom, targetZoom, ref zoomVelocity, zoomSmoothTime);
        
        // Update UI scaling if enabled
        if (scaleUIWithZoom)
        {
            UpdateUIScaling();
        }
    }
    
    /// <summary>
    /// Updates UI scaling based on zoom level
    /// </summary>
    void UpdateUIScaling()
    {
        float uiScale = Mathf.Lerp(0.5f, 2f, Mathf.InverseLerp(minZoom, maxZoom, currentZoom)) * uiScaleMultiplier;
        
        // Apply to Canvas scalers (you would need to implement this based on your UI setup)
        Canvas[] canvases = FindObjectsOfType<Canvas>();
        foreach (var canvas in canvases)
        {
            if (canvas.renderMode == RenderMode.ScreenSpaceOverlay)
            {
                canvas.scaleFactor = uiScale;
            }
        }
    }
    
    /// <summary>
    /// Gets the current zoom level as a formatted string
    /// </summary>
    public string GetZoomDisplayString()
    {
        if (currentZoom < 1000f)
            return $"{currentZoom:F0}m";
        else if (currentZoom < 1000000f)
            return $"{currentZoom / 1000f:F1}km";
        else
            return $"{currentZoom / 1000000f:F2}Mm";
    }
    
    /// <summary>
    /// Sets the zoom level directly
    /// </summary>
    public void SetZoom(float zoom)
    {
        targetZoom = Mathf.Clamp(zoom, minZoom, maxZoom);
    }
    
    /// <summary>
    /// Gets the current effective scale factor for rendering
    /// </summary>
    public float GetCurrentScaleFactor()
    {
        return currentScaleFactor;
    }
    
    /// <summary>
    /// Focuses the camera on a specific entity
    /// </summary>
    public void FocusOnEntity(Entity entity)
    {
        if (entity == null || orbitCamera == null) return;
        
        orbitCamera.SetTarget(entity.transform);
        
        // Set appropriate zoom based on entity size
        float entityRadius = entity.RadiusKm * 1000f; // Convert to meters
        float optimalZoom = entityRadius * 5f; // 5x entity radius
        SetZoom(Mathf.Clamp(optimalZoom, minZoom, maxZoom));
    }
    
    /// <summary>
    /// Resets camera to default settings
    /// </summary>
    public void ResetCamera()
    {
        targetZoom = defaultZoom;
        if (orbitCamera != null)
        {
            orbitCamera.ResetToDefault();
        }
    }
    
    void OnGUI()
    {
        // Display zoom level in top-right corner
        GUI.Label(new Rect(Screen.width - 150, 10, 140, 20), 
                  $"Zoom: {GetZoomDisplayString()}", 
                  new GUIStyle(GUI.skin.label) { alignment = TextAnchor.MiddleRight });
        
        // Display scale factor for debugging
        if (Application.isEditor)
        {
            GUI.Label(new Rect(Screen.width - 150, 30, 140, 20), 
                      $"Scale: {currentScaleFactor:F2}x", 
                      new GUIStyle(GUI.skin.label) { alignment = TextAnchor.MiddleRight });
        }
    }
    
    // Properties
    public float CurrentZoom => currentZoom;
    public float TargetZoom => targetZoom;
    public float MinZoom => minZoom;
    public float MaxZoom => maxZoom;
    public float CurrentScaleFactor => currentScaleFactor;
}
