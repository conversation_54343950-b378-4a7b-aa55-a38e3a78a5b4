<#/*THIS IS A T4 FILE - see t4_text_templating.md for what it is and how to run codegen*/#>
<#@ template debug="True" #>
<#@ output extension=".gen.cs" encoding="utf-8" #>
<#@ assembly name="System.Core" #>
<#@ import namespace="System.Globalization" #>

//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     TextTransform Samples/Packages/com.unity.collections/Unity.Collections.Tests.Playmode/FixedListPlayModeTests.tt
//
//     These tests require UNITY_DOTS_DEBUG to be enabled.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.Collections;
using NUnit.Framework;
using UnityEngine.TestTools;
using System;
using Unity.Collections;

[TestFixture]
internal class FixedListPlaymodeTests
{
    [UnityTest]
#if UNITY_EDITOR // standalone only
        [Ignore("Test is for standalone builds only")]
#endif
    public IEnumerator FixedListPlaymode_AreCollectionsChecksEnabled()
    {
#if ENABLE_UNITY_COLLECTIONS_CHECKS
        Assert.IsTrue(false, "Collections checks should not be enabled in standalone playmode tests");
#endif
#if !UNITY_DOTS_DEBUG
        Assert.IsTrue(false, "UNITY_DOTS_DEBUG should be defined in standalone playmode tests");
#endif
        yield break;
    }
<#
    {
        var TYPES = new string[] {"byte", "int", "float"};
        var TYPESIZES = new int[] {1, 4, 4};
        var SIZES = new int[] {32, 64, 128};
        for (var type = 0; type < 3; ++type)
        {
            for (var size = 0; size < 3; ++size)
            {
                var BYTES = SIZES[size];
                var TYPE = TYPES[type];
                var TYPESIZE = TYPESIZES[type];
                var MAXLENGTH = BYTES - 2;
                var TYPENAME = String.Format("FixedList{0}Bytes", BYTES);
                var TYPENAME_IDENTIFIER = String.Format("FixedList{0}{1}Playmode_", BYTES, new CultureInfo("en-US").TextInfo.ToTitleCase(TYPE));
                var EXPECTEDCAPACITY = (BYTES - 2) / TYPESIZE;
                var BUFFERTYPE = String.Format("FixedBytes{0}", MAXLENGTH);
#>

    [UnityTest]
    public IEnumerator <#=TYPENAME_IDENTIFIER #>GenericHasExpectedCapacity()
    {
        var list = new <#=TYPENAME #><<#=TYPE#>>();
        var expectedCapacity = list.Capacity;
        for(int i = 0; i < expectedCapacity; ++i)
            list.Add((<#=TYPE #>)i);
        Assert.Throws<IndexOutOfRangeException> (() => { list.Add((<#=TYPE #>)expectedCapacity); });

        yield break;
    }
<#
            }
            for (var size = 0; size < 3; ++size)
            {
                var BYTES = SIZES[size];
                var TYPE = TYPES[type];
                var TYPESIZE = TYPESIZES[type];
                var MAXLENGTH = BYTES - 2;
                var TYPENAME = String.Format("FixedList{0}Bytes<{1}>", BYTES, TYPE);
                var TYPENAME_IDENTIFIER = String.Format("FixedList{0}{1}Playmode_", BYTES, new CultureInfo("en-US").TextInfo.ToTitleCase(TYPE));
                var EXPECTEDCAPACITY = (BYTES - 2) / TYPESIZE;
#>

    [UnityTest]
    public IEnumerator <#=TYPENAME_IDENTIFIER #>HasExpectedCapacity()
    {
        var list = new <#=TYPENAME #>();
        var expectedCapacity = list.Capacity;
        for(int i = 0; i < expectedCapacity; ++i)
            list.Add((<#=TYPE #>)i);
        Assert.Throws<IndexOutOfRangeException> (() => { list.Add((<#=TYPE #>)expectedCapacity); });

        yield break;
    }
<#
                foreach (var OTHERBYTES in SIZES)
                {
                    if (OTHERBYTES != BYTES)
                    {
                        var OTHERCAPACITY = (OTHERBYTES - 2) / TYPESIZE;
                        var OTHERTYPENAME = String.Format("FixedList{0}Bytes<{1}>", OTHERBYTES, TYPE);
                        var OTHERTYPENAME_IDENTIFIER = String.Format("_FixedList{0}{1}", OTHERBYTES, new CultureInfo("en-US").TextInfo.ToTitleCase(TYPE));

                        if (EXPECTEDCAPACITY > OTHERCAPACITY)
                        {
#>

    [UnityTest]
    public IEnumerator <#=TYPENAME_IDENTIFIER #>To<#=OTHERTYPENAME_IDENTIFIER #>()
    {
        var a = new <#=TYPENAME #>();
        for(var i = 0; i < <#=EXPECTEDCAPACITY #>; ++i)
            a.Add((<#=TYPE #>)i);
<#                    
                            WriteLine(
                                "        Assert.Throws<IndexOutOfRangeException> (() => {{ var b = new {0}(a); }} );",
                                OTHERTYPENAME);
                        
#>
        yield break;
    }
<#
                        }
                    }
                }
            }
        }
    }
#>

}
