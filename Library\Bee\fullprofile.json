{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 16152, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 16152, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 16152, "tid": 969, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 16152, "tid": 969, "ts": 1753474567704186, "dur": 14, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 16152, "tid": 969, "ts": 1753474567704218, "dur": 8, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 16152, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 16152, "tid": 1, "ts": 1753474567408498, "dur": 6553, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 16152, "tid": 1, "ts": 1753474567415056, "dur": 212842, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 16152, "tid": 1, "ts": 1753474567627901, "dur": 52719, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 16152, "tid": 969, "ts": 1753474567704229, "dur": 818, "ph": "X", "name": "", "args": {}}, {"pid": 16152, "tid": 42949672960, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567408398, "dur": 34617, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567443018, "dur": 259936, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567443028, "dur": 70, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567443285, "dur": 309, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567443597, "dur": 217, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567443867, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567443871, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567443914, "dur": 61, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567443978, "dur": 4495, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567448478, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567448487, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567448542, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567448545, "dur": 21, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567448569, "dur": 2, "ph": "X", "name": "ProcessMessages 26", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567448573, "dur": 55, "ph": "X", "name": "ReadAsync 26", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567448633, "dur": 2, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567448636, "dur": 32, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567448673, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567448676, "dur": 34, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567448712, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567448716, "dur": 26, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567448747, "dur": 91, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567448843, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567448846, "dur": 49, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567448898, "dur": 3, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567448902, "dur": 33, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567448937, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567448939, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567448978, "dur": 1, "ph": "X", "name": "ProcessMessages 126", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567448980, "dur": 45, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567449027, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567449036, "dur": 23, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567449074, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567449077, "dur": 51, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567449134, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567449137, "dur": 112, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567449252, "dur": 1, "ph": "X", "name": "ProcessMessages 166", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567449255, "dur": 52, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567449312, "dur": 1, "ph": "X", "name": "ProcessMessages 241", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567449314, "dur": 106, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567449424, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567449426, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567449481, "dur": 2, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567449485, "dur": 43, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567449530, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567449532, "dur": 38, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567449573, "dur": 1, "ph": "X", "name": "ProcessMessages 161", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567449579, "dur": 102, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567449782, "dur": 2, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567449788, "dur": 235, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567450027, "dur": 6, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567450035, "dur": 61, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567450098, "dur": 3, "ph": "X", "name": "ProcessMessages 2082", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567450103, "dur": 35, "ph": "X", "name": "ReadAsync 2082", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567450250, "dur": 2, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567450260, "dur": 71, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567450334, "dur": 2, "ph": "X", "name": "ProcessMessages 1641", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567450339, "dur": 82, "ph": "X", "name": "ReadAsync 1641", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567450429, "dur": 2, "ph": "X", "name": "ProcessMessages 751", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567450441, "dur": 110, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567450554, "dur": 2, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567450619, "dur": 213, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567451110, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567451113, "dur": 112, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567451233, "dur": 6, "ph": "X", "name": "ProcessMessages 4910", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567451241, "dur": 48, "ph": "X", "name": "ReadAsync 4910", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567451296, "dur": 2, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567451299, "dur": 46, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567451390, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567451394, "dur": 45, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567451442, "dur": 1, "ph": "X", "name": "ProcessMessages 207", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567451445, "dur": 42, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567451491, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567451493, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567451619, "dur": 2, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567451659, "dur": 62, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567451725, "dur": 2, "ph": "X", "name": "ProcessMessages 1873", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567451734, "dur": 65, "ph": "X", "name": "ReadAsync 1873", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567451802, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567451805, "dur": 126, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567451933, "dur": 2, "ph": "X", "name": "ProcessMessages 1412", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567451937, "dur": 39, "ph": "X", "name": "ReadAsync 1412", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567451980, "dur": 1, "ph": "X", "name": "ProcessMessages 165", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567451983, "dur": 49, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567452037, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567452080, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567452083, "dur": 64, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567452150, "dur": 1, "ph": "X", "name": "ProcessMessages 104", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567452152, "dur": 36, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567452190, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567452202, "dur": 41, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567452255, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567452265, "dur": 62, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567452329, "dur": 1, "ph": "X", "name": "ProcessMessages 740", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567452332, "dur": 41, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567452380, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567452424, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567452426, "dur": 60, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567452490, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567452493, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567452548, "dur": 2, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567452551, "dur": 47, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567452601, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567452604, "dur": 42, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567452656, "dur": 3, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567452674, "dur": 52, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567452766, "dur": 3, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567452775, "dur": 85, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567452862, "dur": 7, "ph": "X", "name": "ProcessMessages 803", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567452872, "dur": 55, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567452930, "dur": 38, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567452971, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567452974, "dur": 44, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567453022, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567453025, "dur": 46, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567453074, "dur": 2, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567453080, "dur": 56, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567453139, "dur": 2, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567453142, "dur": 51, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567453199, "dur": 6, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567453207, "dur": 46, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567453258, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567453261, "dur": 31, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567453293, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567453357, "dur": 122, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567453485, "dur": 3, "ph": "X", "name": "ProcessMessages 934", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567453489, "dur": 133, "ph": "X", "name": "ReadAsync 934", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567453828, "dur": 3, "ph": "X", "name": "ProcessMessages 1046", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567453834, "dur": 90, "ph": "X", "name": "ReadAsync 1046", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567453933, "dur": 5, "ph": "X", "name": "ProcessMessages 3492", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567453940, "dur": 58, "ph": "X", "name": "ReadAsync 3492", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567454001, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567454003, "dur": 53, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567454060, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567454063, "dur": 33, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567454099, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567454101, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567454145, "dur": 4, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567454150, "dur": 23, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567454175, "dur": 3, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567454180, "dur": 55, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567454245, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567454248, "dur": 154, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567454411, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567454414, "dur": 133, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567454551, "dur": 2, "ph": "X", "name": "ProcessMessages 1222", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567454554, "dur": 55, "ph": "X", "name": "ReadAsync 1222", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567454616, "dur": 2, "ph": "X", "name": "ProcessMessages 995", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567454619, "dur": 165, "ph": "X", "name": "ReadAsync 995", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567454794, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567454797, "dur": 123, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567454923, "dur": 4, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567454930, "dur": 99, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567455034, "dur": 2, "ph": "X", "name": "ProcessMessages 1194", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567455174, "dur": 65, "ph": "X", "name": "ReadAsync 1194", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567455243, "dur": 3, "ph": "X", "name": "ProcessMessages 1678", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567455249, "dur": 187, "ph": "X", "name": "ReadAsync 1678", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567455587, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567455590, "dur": 66, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567455658, "dur": 3, "ph": "X", "name": "ProcessMessages 1940", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567455663, "dur": 58, "ph": "X", "name": "ReadAsync 1940", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567456029, "dur": 2, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567456033, "dur": 128, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567456176, "dur": 3, "ph": "X", "name": "ProcessMessages 1941", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567456180, "dur": 55, "ph": "X", "name": "ReadAsync 1941", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567456240, "dur": 3, "ph": "X", "name": "ProcessMessages 987", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567456412, "dur": 171, "ph": "X", "name": "ReadAsync 987", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567456590, "dur": 2, "ph": "X", "name": "ProcessMessages 2133", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567456594, "dur": 52, "ph": "X", "name": "ReadAsync 2133", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567456926, "dur": 2, "ph": "X", "name": "ProcessMessages 934", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567456930, "dur": 68, "ph": "X", "name": "ReadAsync 934", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567456999, "dur": 3, "ph": "X", "name": "ProcessMessages 2597", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567457004, "dur": 41, "ph": "X", "name": "ReadAsync 2597", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567457057, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567457059, "dur": 183, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567457248, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567457251, "dur": 144, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567457443, "dur": 3, "ph": "X", "name": "ProcessMessages 1317", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567457448, "dur": 128, "ph": "X", "name": "ReadAsync 1317", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567457580, "dur": 3, "ph": "X", "name": "ProcessMessages 794", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567457585, "dur": 551, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567458140, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567458144, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567458213, "dur": 3, "ph": "X", "name": "ProcessMessages 861", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567458218, "dur": 49, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567458275, "dur": 81, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567458394, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567458401, "dur": 31, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567458434, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567458437, "dur": 33, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567458473, "dur": 1, "ph": "X", "name": "ProcessMessages 122", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567458476, "dur": 28, "ph": "X", "name": "ReadAsync 122", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567458506, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567458508, "dur": 46, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567458566, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567458603, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567458609, "dur": 47, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567458661, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567458663, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567458709, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567458713, "dur": 46, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567458787, "dur": 1, "ph": "X", "name": "ProcessMessages 77", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567458793, "dur": 40, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567458835, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567458838, "dur": 35, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567458876, "dur": 1, "ph": "X", "name": "ProcessMessages 202", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567458878, "dur": 38, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567458922, "dur": 1, "ph": "X", "name": "ProcessMessages 69", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567458925, "dur": 55, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567458983, "dur": 1, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567458985, "dur": 38, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567459025, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567459028, "dur": 43, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567459074, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567459147, "dur": 60, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567459209, "dur": 2, "ph": "X", "name": "ProcessMessages 1275", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567459213, "dur": 41, "ph": "X", "name": "ReadAsync 1275", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567459349, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567459354, "dur": 144, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567459502, "dur": 3, "ph": "X", "name": "ProcessMessages 864", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567459512, "dur": 56, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567459572, "dur": 3, "ph": "X", "name": "ProcessMessages 1019", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567459577, "dur": 35, "ph": "X", "name": "ReadAsync 1019", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567459616, "dur": 1, "ph": "X", "name": "ProcessMessages 149", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567459619, "dur": 35, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567459657, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567459659, "dur": 35, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567459704, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567459707, "dur": 52, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567459761, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567459765, "dur": 44, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567459821, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567459825, "dur": 184, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567460021, "dur": 2, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567460025, "dur": 75, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567460135, "dur": 3, "ph": "X", "name": "ProcessMessages 1641", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567460141, "dur": 54, "ph": "X", "name": "ReadAsync 1641", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567460248, "dur": 2, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567460252, "dur": 55, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567460311, "dur": 2, "ph": "X", "name": "ProcessMessages 683", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567460315, "dur": 45, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567460364, "dur": 73, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567460528, "dur": 177, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567460708, "dur": 5, "ph": "X", "name": "ProcessMessages 3671", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567460714, "dur": 79, "ph": "X", "name": "ReadAsync 3671", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567460799, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567460807, "dur": 57, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567460867, "dur": 2, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567460871, "dur": 44, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567461007, "dur": 1, "ph": "X", "name": "ProcessMessages 127", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567461011, "dur": 319, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567461335, "dur": 7, "ph": "X", "name": "ProcessMessages 2300", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567461344, "dur": 113, "ph": "X", "name": "ReadAsync 2300", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567461495, "dur": 2, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567461498, "dur": 65, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567461692, "dur": 2, "ph": "X", "name": "ProcessMessages 1045", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567461696, "dur": 274, "ph": "X", "name": "ReadAsync 1045", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567461979, "dur": 2, "ph": "X", "name": "ProcessMessages 768", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567461982, "dur": 60, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567462276, "dur": 3, "ph": "X", "name": "ProcessMessages 1559", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567462282, "dur": 51, "ph": "X", "name": "ReadAsync 1559", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567462337, "dur": 2, "ph": "X", "name": "ProcessMessages 1662", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567462344, "dur": 53, "ph": "X", "name": "ReadAsync 1662", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567462400, "dur": 1, "ph": "X", "name": "ProcessMessages 78", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567462403, "dur": 31, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567462435, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567462439, "dur": 3546, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567466094, "dur": 22, "ph": "X", "name": "ProcessMessages 20537", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567466118, "dur": 74, "ph": "X", "name": "ReadAsync 20537", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567466196, "dur": 2, "ph": "X", "name": "ProcessMessages 1317", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567466200, "dur": 49, "ph": "X", "name": "ReadAsync 1317", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567466253, "dur": 1, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567466256, "dur": 48, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567466307, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567466313, "dur": 98, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567466417, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567466420, "dur": 63, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567466485, "dur": 36, "ph": "X", "name": "ProcessMessages 889", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567466524, "dur": 223, "ph": "X", "name": "ReadAsync 889", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567466859, "dur": 2, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567466864, "dur": 486, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567467484, "dur": 6, "ph": "X", "name": "ProcessMessages 2761", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567467494, "dur": 108, "ph": "X", "name": "ReadAsync 2761", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567467606, "dur": 7, "ph": "X", "name": "ProcessMessages 4689", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567467881, "dur": 172, "ph": "X", "name": "ReadAsync 4689", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567468058, "dur": 4, "ph": "X", "name": "ProcessMessages 3265", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567468063, "dur": 67, "ph": "X", "name": "ReadAsync 3265", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567468136, "dur": 2, "ph": "X", "name": "ProcessMessages 1286", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567468139, "dur": 50, "ph": "X", "name": "ReadAsync 1286", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567468356, "dur": 2, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567468361, "dur": 72, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567468443, "dur": 10, "ph": "X", "name": "ProcessMessages 1783", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567468601, "dur": 405, "ph": "X", "name": "ReadAsync 1783", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567469016, "dur": 4, "ph": "X", "name": "ProcessMessages 3026", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567469024, "dur": 90, "ph": "X", "name": "ReadAsync 3026", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567469118, "dur": 8, "ph": "X", "name": "ProcessMessages 2413", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567469130, "dur": 48, "ph": "X", "name": "ReadAsync 2413", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567469180, "dur": 3, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567469185, "dur": 52, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567469240, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567469244, "dur": 131, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567469384, "dur": 2, "ph": "X", "name": "ProcessMessages 1027", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567469387, "dur": 234, "ph": "X", "name": "ReadAsync 1027", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567469624, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567469636, "dur": 252, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567469892, "dur": 5, "ph": "X", "name": "ProcessMessages 2182", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567469902, "dur": 186, "ph": "X", "name": "ReadAsync 2182", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567470236, "dur": 4, "ph": "X", "name": "ProcessMessages 2340", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567470242, "dur": 91, "ph": "X", "name": "ReadAsync 2340", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567470344, "dur": 3, "ph": "X", "name": "ProcessMessages 1874", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567470669, "dur": 254, "ph": "X", "name": "ReadAsync 1874", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567470937, "dur": 6, "ph": "X", "name": "ProcessMessages 5495", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567470957, "dur": 439, "ph": "X", "name": "ReadAsync 5495", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567471403, "dur": 2, "ph": "X", "name": "ProcessMessages 1560", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567471407, "dur": 324, "ph": "X", "name": "ReadAsync 1560", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567471735, "dur": 8, "ph": "X", "name": "ProcessMessages 6195", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567471756, "dur": 101, "ph": "X", "name": "ReadAsync 6195", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567472034, "dur": 3, "ph": "X", "name": "ProcessMessages 2326", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567472092, "dur": 82, "ph": "X", "name": "ReadAsync 2326", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567472186, "dur": 4, "ph": "X", "name": "ProcessMessages 2747", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567472192, "dur": 43, "ph": "X", "name": "ReadAsync 2747", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567472243, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567472245, "dur": 34, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567472281, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567472296, "dur": 27, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567472387, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567472431, "dur": 231, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567472673, "dur": 3, "ph": "X", "name": "ProcessMessages 2025", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567472679, "dur": 73, "ph": "X", "name": "ReadAsync 2025", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567472756, "dur": 3, "ph": "X", "name": "ProcessMessages 2153", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567472761, "dur": 44, "ph": "X", "name": "ReadAsync 2153", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567472842, "dur": 8, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567472852, "dur": 52, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567472916, "dur": 11, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567472928, "dur": 405, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567473560, "dur": 119, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567473691, "dur": 237, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567474036, "dur": 21, "ph": "X", "name": "ProcessMessages 5919", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567474212, "dur": 103, "ph": "X", "name": "ReadAsync 5919", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567474446, "dur": 44, "ph": "X", "name": "ProcessMessages 2012", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567474524, "dur": 145, "ph": "X", "name": "ReadAsync 2012", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567474808, "dur": 2, "ph": "X", "name": "ProcessMessages 1291", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567474812, "dur": 93, "ph": "X", "name": "ReadAsync 1291", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567474909, "dur": 2, "ph": "X", "name": "ProcessMessages 1566", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567474976, "dur": 187, "ph": "X", "name": "ReadAsync 1566", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567475166, "dur": 2, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567475170, "dur": 86, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567475265, "dur": 2, "ph": "X", "name": "ProcessMessages 1108", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567475277, "dur": 63, "ph": "X", "name": "ReadAsync 1108", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567475343, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567475346, "dur": 45, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567475397, "dur": 2, "ph": "X", "name": "ProcessMessages 631", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567475434, "dur": 95, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567475613, "dur": 10, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567475754, "dur": 194, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567475953, "dur": 3, "ph": "X", "name": "ProcessMessages 2022", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567476026, "dur": 202, "ph": "X", "name": "ReadAsync 2022", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567476383, "dur": 1, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567476392, "dur": 72, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567476470, "dur": 2, "ph": "X", "name": "ProcessMessages 2479", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567476475, "dur": 105, "ph": "X", "name": "ReadAsync 2479", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567476584, "dur": 8, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567476594, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567476648, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567476651, "dur": 124, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567476779, "dur": 7, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567476794, "dur": 74, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567476884, "dur": 1, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567476890, "dur": 59, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567476953, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567476959, "dur": 80, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567477079, "dur": 2, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567477083, "dur": 45, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567477143, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567477146, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567477226, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567477229, "dur": 197, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567477439, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567477442, "dur": 112, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567477559, "dur": 2, "ph": "X", "name": "ProcessMessages 929", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567477566, "dur": 43, "ph": "X", "name": "ReadAsync 929", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567477622, "dur": 3, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567477627, "dur": 107, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567477765, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567477774, "dur": 138, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567477919, "dur": 4, "ph": "X", "name": "ProcessMessages 1177", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567477925, "dur": 37, "ph": "X", "name": "ReadAsync 1177", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567478080, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567478084, "dur": 144, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567478234, "dur": 2, "ph": "X", "name": "ProcessMessages 1386", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567478237, "dur": 148, "ph": "X", "name": "ReadAsync 1386", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567478395, "dur": 47, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567478446, "dur": 271, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567478720, "dur": 2, "ph": "X", "name": "ProcessMessages 1180", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567478724, "dur": 66, "ph": "X", "name": "ReadAsync 1180", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567478792, "dur": 1, "ph": "X", "name": "ProcessMessages 962", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567478795, "dur": 71, "ph": "X", "name": "ReadAsync 962", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567478872, "dur": 1, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567478874, "dur": 39, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567479035, "dur": 2, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567479039, "dur": 66, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567479232, "dur": 2, "ph": "X", "name": "ProcessMessages 717", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567479249, "dur": 165, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567479529, "dur": 4, "ph": "X", "name": "ProcessMessages 2818", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567479536, "dur": 135, "ph": "X", "name": "ReadAsync 2818", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567479679, "dur": 2, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567479691, "dur": 183, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567479879, "dur": 1, "ph": "X", "name": "ProcessMessages 799", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567479882, "dur": 314, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567480199, "dur": 2, "ph": "X", "name": "ProcessMessages 1239", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567480202, "dur": 85, "ph": "X", "name": "ReadAsync 1239", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567480295, "dur": 4, "ph": "X", "name": "ProcessMessages 2034", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567480302, "dur": 54, "ph": "X", "name": "ReadAsync 2034", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567480360, "dur": 10, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567480372, "dur": 57, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567480435, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567480444, "dur": 87, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567480541, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567480544, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567480641, "dur": 2, "ph": "X", "name": "ProcessMessages 740", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567480651, "dur": 60, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567480714, "dur": 2, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567480728, "dur": 138, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567480878, "dur": 2, "ph": "X", "name": "ProcessMessages 959", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567480881, "dur": 50, "ph": "X", "name": "ReadAsync 959", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567480940, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567480951, "dur": 41, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567481001, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567481005, "dur": 97, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567481107, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567481116, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567481190, "dur": 2, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567481194, "dur": 342, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567481606, "dur": 3, "ph": "X", "name": "ProcessMessages 1740", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567481612, "dur": 71, "ph": "X", "name": "ReadAsync 1740", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567481686, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567481689, "dur": 65, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567481759, "dur": 2, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567481770, "dur": 335, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567482111, "dur": 3, "ph": "X", "name": "ProcessMessages 2154", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567482463, "dur": 75, "ph": "X", "name": "ReadAsync 2154", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567482543, "dur": 15, "ph": "X", "name": "ProcessMessages 2017", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567482565, "dur": 112, "ph": "X", "name": "ReadAsync 2017", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567482686, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567482759, "dur": 52, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567482842, "dur": 9, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567482853, "dur": 68, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567483079, "dur": 3, "ph": "X", "name": "ProcessMessages 686", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567483087, "dur": 56, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567483149, "dur": 2, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567483195, "dur": 40, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567483237, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567483248, "dur": 55, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567483311, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567483314, "dur": 120, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567483442, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567483444, "dur": 51, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567483501, "dur": 1, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567483511, "dur": 61, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567483580, "dur": 2, "ph": "X", "name": "ProcessMessages 762", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567483584, "dur": 67, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567483655, "dur": 1, "ph": "X", "name": "ProcessMessages 139", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567483659, "dur": 90, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567483758, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567483760, "dur": 149, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567483921, "dur": 2, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567483924, "dur": 56, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567483989, "dur": 2, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567484008, "dur": 143, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567484158, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567484172, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567484362, "dur": 2, "ph": "X", "name": "ProcessMessages 970", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567484366, "dur": 65, "ph": "X", "name": "ReadAsync 970", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567484434, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567484445, "dur": 50, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567484508, "dur": 3, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567484527, "dur": 52, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567484589, "dur": 9, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567484601, "dur": 132, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567484856, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567484908, "dur": 115, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567485028, "dur": 13, "ph": "X", "name": "ProcessMessages 1763", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567485043, "dur": 245, "ph": "X", "name": "ReadAsync 1763", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567485384, "dur": 12, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567485547, "dur": 77, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567485634, "dur": 3, "ph": "X", "name": "ProcessMessages 2080", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567485640, "dur": 287, "ph": "X", "name": "ReadAsync 2080", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567485937, "dur": 2, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567486076, "dur": 136, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567486215, "dur": 121, "ph": "X", "name": "ProcessMessages 1648", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567486413, "dur": 86, "ph": "X", "name": "ReadAsync 1648", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567486502, "dur": 3, "ph": "X", "name": "ProcessMessages 1856", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567486507, "dur": 109, "ph": "X", "name": "ReadAsync 1856", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567486753, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567486755, "dur": 157, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567486922, "dur": 32, "ph": "X", "name": "ProcessMessages 975", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567486958, "dur": 178, "ph": "X", "name": "ReadAsync 975", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567487141, "dur": 14, "ph": "X", "name": "ProcessMessages 1768", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567487157, "dur": 38, "ph": "X", "name": "ReadAsync 1768", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567487241, "dur": 8, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567487251, "dur": 54, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567487321, "dur": 82, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567487406, "dur": 2, "ph": "X", "name": "ProcessMessages 776", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567487410, "dur": 187, "ph": "X", "name": "ReadAsync 776", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567487608, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567487610, "dur": 71, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567487801, "dur": 2, "ph": "X", "name": "ProcessMessages 1149", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567487804, "dur": 51, "ph": "X", "name": "ReadAsync 1149", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567487873, "dur": 6, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567487880, "dur": 39, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567487921, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567487923, "dur": 155, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567488090, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567488092, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567488157, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567488175, "dur": 35, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567488215, "dur": 3, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567488221, "dur": 111, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567488336, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567488338, "dur": 101, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567488442, "dur": 2, "ph": "X", "name": "ProcessMessages 1111", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567488450, "dur": 83, "ph": "X", "name": "ReadAsync 1111", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567488542, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567488544, "dur": 205, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567488752, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567488754, "dur": 108, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567488884, "dur": 10, "ph": "X", "name": "ProcessMessages 1847", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567488898, "dur": 101, "ph": "X", "name": "ReadAsync 1847", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567489006, "dur": 2, "ph": "X", "name": "ProcessMessages 765", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567489010, "dur": 156, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567489182, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567489185, "dur": 108, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567489298, "dur": 2, "ph": "X", "name": "ProcessMessages 1101", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567489302, "dur": 134, "ph": "X", "name": "ReadAsync 1101", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567489442, "dur": 60, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567489506, "dur": 57, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567489565, "dur": 2, "ph": "X", "name": "ProcessMessages 1312", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567489569, "dur": 79, "ph": "X", "name": "ReadAsync 1312", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567489664, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567489668, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567489734, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567489737, "dur": 92, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567489837, "dur": 8, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567489857, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567489962, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567489966, "dur": 36, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567490004, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567490007, "dur": 71, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567490094, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567490144, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567490147, "dur": 23, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567490180, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567490182, "dur": 138, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567490323, "dur": 8, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567490333, "dur": 53, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567490389, "dur": 3, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567490400, "dur": 49, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567490451, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567490454, "dur": 556, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567491113, "dur": 20, "ph": "X", "name": "ProcessMessages 1120", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567491139, "dur": 114, "ph": "X", "name": "ReadAsync 1120", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567491285, "dur": 3, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567491363, "dur": 95, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567491462, "dur": 14, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567491478, "dur": 189, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567491674, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567491678, "dur": 55, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567491744, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567491747, "dur": 45, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567491796, "dur": 16, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567491814, "dur": 57, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567491875, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567491877, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567491953, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567491956, "dur": 37, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567491996, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567491998, "dur": 31, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567492032, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567492042, "dur": 54, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567492099, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567492109, "dur": 57, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567492175, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567492179, "dur": 26, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567492214, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567492218, "dur": 19, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567492239, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567492240, "dur": 53, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567492305, "dur": 2, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567492308, "dur": 84, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567492394, "dur": 3, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567492407, "dur": 203, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567492618, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567492630, "dur": 207, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567492840, "dur": 6, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567492971, "dur": 123, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567493103, "dur": 11, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567493117, "dur": 92, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567493222, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567493266, "dur": 42, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567493337, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567493341, "dur": 30, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567493390, "dur": 85, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567493494, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567493496, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567493532, "dur": 317, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567493853, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567493856, "dur": 231, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567494106, "dur": 7, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567494118, "dur": 82, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567494203, "dur": 3, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567494215, "dur": 51, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567494553, "dur": 10, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567494565, "dur": 174, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567494799, "dur": 4, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567494811, "dur": 625, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567495543, "dur": 32, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567495585, "dur": 199, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567495860, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567495864, "dur": 658, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567496525, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567496528, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567496711, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567496714, "dur": 181, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567496932, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567496936, "dur": 94, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567497040, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567497043, "dur": 9034, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567506081, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567506617, "dur": 4389, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567512273, "dur": 1485, "ph": "X", "name": "ProcessMessages 1828", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567513765, "dur": 4398, "ph": "X", "name": "ReadAsync 1828", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567519948, "dur": 304, "ph": "X", "name": "ProcessMessages 996", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567521476, "dur": 993, "ph": "X", "name": "ReadAsync 996", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567522715, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567522719, "dur": 104599, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567627325, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567627332, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567627397, "dur": 39, "ph": "X", "name": "ProcessMessages 183", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567627441, "dur": 6407, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567633854, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567633857, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567633911, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567633915, "dur": 26, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567633943, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567633946, "dur": 1646, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567635595, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567635602, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567635638, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567635640, "dur": 109, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567635751, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567635753, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567635786, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567635814, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567635819, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567635930, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567635969, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567635971, "dur": 1008, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567636984, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567637017, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567637020, "dur": 790, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567637815, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567637817, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567637851, "dur": 130, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567637985, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567638020, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567638022, "dur": 317, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567638345, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567638376, "dur": 458, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567638838, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567638867, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567638869, "dur": 94, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567638968, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567638997, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567639000, "dur": 204, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567639208, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567639238, "dur": 291, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567639532, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567639563, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567639607, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567639635, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567639637, "dur": 657, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567640299, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567640329, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567640357, "dur": 38, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567640398, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567640432, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567640435, "dur": 124, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567640563, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567640592, "dur": 319, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567640915, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567640947, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567640987, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567641018, "dur": 761, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567641783, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567641812, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567641815, "dur": 499, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567642319, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567642349, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567642351, "dur": 260, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567642615, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567642634, "dur": 188, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567642826, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567642868, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567642870, "dur": 510, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567643386, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567643420, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567643422, "dur": 353, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567643779, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567643782, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567643804, "dur": 118, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567643924, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567643929, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567643953, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567643957, "dur": 370, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567644329, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567644333, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567644367, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567644370, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567644400, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567644402, "dur": 162, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567644570, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567644601, "dur": 275, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567644879, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567644908, "dur": 1004, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567645916, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567645919, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567645957, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567645959, "dur": 394, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567646358, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567646392, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567646394, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567646432, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567646434, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567646504, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567646508, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567646544, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567646546, "dur": 86, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567646636, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567646672, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567646675, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567646707, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567646709, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567646741, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567646743, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567646778, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567646783, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567646818, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567646821, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567646856, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567646858, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567646891, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567646894, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567646934, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567646936, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567646978, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567646980, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647018, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647020, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647052, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647055, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647090, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647092, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647127, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647130, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647169, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647171, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647202, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647204, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647235, "dur": 29, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647268, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647271, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647378, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647381, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647424, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647428, "dur": 84, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647516, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647552, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647554, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647590, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647592, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647623, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647625, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647667, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647670, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647710, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647713, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647749, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647753, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647785, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647789, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647820, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647822, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647856, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567647860, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567648020, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567648022, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567648063, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567648066, "dur": 196, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567648267, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567648308, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567648311, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567648354, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567648356, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567648390, "dur": 227, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567648648, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567648684, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567648687, "dur": 16, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567648705, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567648746, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567648775, "dur": 274, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567649052, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567649054, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567649085, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567649088, "dur": 28, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567649119, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567649122, "dur": 165, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567649291, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567649293, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567649335, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567649337, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567649362, "dur": 41456, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567690823, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567690826, "dur": 188, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567691018, "dur": 2, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 16152, "tid": 42949672960, "ts": 1753474567691021, "dur": 11924, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 16152, "tid": 969, "ts": 1753474567705051, "dur": 2158, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 16152, "tid": 38654705664, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 16152, "tid": 38654705664, "ts": 1753474567407992, "dur": 272652, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 16152, "tid": 38654705664, "ts": 1753474567680645, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 16152, "tid": 38654705664, "ts": 1753474567680647, "dur": 4477, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 16152, "tid": 969, "ts": 1753474567707212, "dur": 13, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 16152, "tid": 34359738368, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 16152, "tid": 34359738368, "ts": 1753474567400554, "dur": 302451, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 16152, "tid": 34359738368, "ts": 1753474567401163, "dur": 6768, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 16152, "tid": 34359738368, "ts": 1753474567703011, "dur": 187, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 16152, "tid": 34359738368, "ts": 1753474567703021, "dur": 19, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 16152, "tid": 34359738368, "ts": 1753474567703201, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 16152, "tid": 969, "ts": 1753474567707227, "dur": 11, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1753474567443704, "dur": 83, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753474567444600, "dur": 56, "ph": "X", "name": "EmitBuildStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753474567443893, "dur": 2759, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753474567446670, "dur": 1390, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753474567448254, "dur": 111, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1753474567448366, "dur": 843, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753474567449253, "dur": 115, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ScriptAssemblies"}}, {"pid": 12345, "tid": 0, "ts": 1753474567449414, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_5030B9807253E8C6.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567449538, "dur": 112, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_D01BB2FA989B17C0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567449656, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_F3F5DCB963727977.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567449713, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_74150E0F4EFAAEFA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567449798, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_9AF2FAAE839792C8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567449882, "dur": 151, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_0D91F1512EC5566E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567450039, "dur": 180, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_647AD1890553C18A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567450325, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_7C06E08EC41CCAE2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567450414, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_A4ABE13006FC4FB7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567450875, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_96AE3EE2094E73B8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567451052, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_7D870C78E23D6B98.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567451153, "dur": 144, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_8BB5EB99A3DFCB51.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567451432, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_2E4560AD94EBD233.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567451947, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_E25C6FF6026C9DB4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567452124, "dur": 92, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_1186D56C5D6DA8DB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567452221, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_3D77701C9DDCB673.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567452483, "dur": 122, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_971662CBFF7A1C60.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567452704, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_31DA4AB87363EC55.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567452858, "dur": 101, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_427536A3014E122B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567452966, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_E5B3CE3140DA7472.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567453101, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_FB30C9CBD6C6FA04.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567453197, "dur": 103, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_11144021F988EE9B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567453306, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InsightsModule.dll_2B8BC9F317969ABE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567453497, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_E993CB39F79CD18B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567453651, "dur": 101, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_7483FEB008B1F9DA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567453851, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_9338625C7A9A1EA3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567453945, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_34BEE49F185E204B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567454672, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_5AFAAFF0D7C8F432.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567454830, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_96D7F32DA72365F9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567454964, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_A783396C963C64EE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567455223, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_25A93F8CA0E058C0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567455379, "dur": 220, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_B4C8E134AA0ACCA9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567455759, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567455998, "dur": 117, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753474567456405, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753474567456863, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753474567457247, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_F30DFA20C0DD1968.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567457747, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_257AEB342BE77856.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567457823, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753474567457888, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753474567458070, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753474567458272, "dur": 486, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753474567458953, "dur": 128, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567459089, "dur": 125, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753474567459294, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753474567459404, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753474567459485, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753474567459631, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753474567459783, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753474567460338, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753474567460853, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753474567460970, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753474567461454, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753474567461532, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753474567461955, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753474567462322, "dur": 105, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753474567463111, "dur": 106, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567464808, "dur": 1989, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753474567466939, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753474567466997, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753474567467163, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Utilities.ref.dll_0524057423981A9D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567467642, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753474567468311, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753474567468719, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753474567469182, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.HighDefinition.Editor.ref.dll_7A494C01255A034B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567469524, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753474567469843, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753474567470131, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753474567470800, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_9A20CFD48BC11F43.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567471107, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753474567471576, "dur": 102, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753474567472275, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753474567472606, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753474567474894, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753474567475087, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1753474567475622, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753474567476684, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753474567477954, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1753474567478300, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753474567478587, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753474567478649, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/908057632503650206.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753474567479287, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1753474567481054, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.dll"}}, {"pid": 12345, "tid": 0, "ts": 1753474567481625, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753474567481941, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1753474567482439, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753474567482859, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1753474567483908, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.CodeGen.dll"}}, {"pid": 12345, "tid": 0, "ts": 1753474567484342, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753474567485264, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753474567485794, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753474567485914, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10411718816959651794.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753474567486371, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753474567489592, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753474567490033, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753474567449240, "dur": 41215, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753474567490474, "dur": 196574, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753474567687050, "dur": 80, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753474567687130, "dur": 79, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753474567687277, "dur": 161, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753474567687640, "dur": 91, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753474567687845, "dur": 1969, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1753474567450821, "dur": 39687, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567490795, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_11144021F988EE9B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753474567491098, "dur": 179, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_716F702ACE218263.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753474567491337, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567491576, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_F752D5DB4BCF0EF1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753474567491851, "dur": 693, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753474567492546, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_33352EA77CF2B9C9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753474567492818, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753474567493245, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1753474567493488, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1753474567493851, "dur": 514, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753474567494455, "dur": 1036, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753474567495493, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567496012, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567496283, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567496506, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567496707, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567496979, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567497236, "dur": 388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567497625, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567498087, "dur": 522, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@5b86b5eab164\\Editor\\Models\\Blocks\\Implementations\\Spawn\\VFXSpawnerPeriodicBurst.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753474567497871, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567498697, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567499339, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567499589, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567499819, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567500383, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567500606, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567500834, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567501060, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567501313, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567501835, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567502047, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567502312, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567502543, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567502757, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567502998, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567503642, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567504104, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567504471, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567504670, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567504875, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567505071, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567505323, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567505630, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567506025, "dur": 753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753474567506849, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753474567507032, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753474567507277, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753474567507513, "dur": 646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753474567508159, "dur": 374, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567508539, "dur": 677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753474567509289, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753474567509484, "dur": 620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753474567510105, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567510222, "dur": 122148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567632372, "dur": 2228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753474567634653, "dur": 2040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753474567636733, "dur": 2082, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753474567638816, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567639143, "dur": 1995, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.EditorCoroutines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753474567641174, "dur": 4145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753474567645366, "dur": 2147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753474567647544, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753474567647935, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567648213, "dur": 615, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753474567648830, "dur": 1296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753474567650126, "dur": 36909, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567451000, "dur": 39581, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567490590, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_DC72FC0FB73CB983.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753474567490829, "dur": 214, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_E2FE17D849825DD8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753474567491086, "dur": 255, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_D166F8A77BC70F6E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753474567491346, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_ECCC620FA53AB17A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753474567491815, "dur": 212, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_9905600612EB9529.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753474567492044, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567492142, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567492208, "dur": 533, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1753474567493134, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753474567493276, "dur": 471, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1753474567493850, "dur": 412, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753474567494264, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567494720, "dur": 629, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753474567495350, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567496065, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567496316, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567496636, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567496900, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567497196, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567498085, "dur": 552, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@5b86b5eab164\\Editor\\GraphView\\Views\\VFXCopyPasteCommon.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753474567498046, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567498811, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567499208, "dur": 767, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.high-definition@9746be8d99fb\\Runtime\\RenderPipeline\\HDRenderPipelineGlobalSettings.Migration.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753474567499975, "dur": 591, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.high-definition@9746be8d99fb\\Runtime\\RenderPipeline\\HDRenderPipelineGlobalSettings.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753474567499043, "dur": 1576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567500620, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567500850, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567501066, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567501275, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567501500, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567501805, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567502025, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567502273, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567502538, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567502773, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567503341, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567503573, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567503918, "dur": 387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567504306, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567504956, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567505190, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567505606, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567505779, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753474567505977, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567506043, "dur": 661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753474567506780, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753474567506970, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753474567507211, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753474567507406, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753474567507617, "dur": 532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753474567508186, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753474567508422, "dur": 832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753474567509336, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567509613, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567509903, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567510194, "dur": 122295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567632491, "dur": 2083, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753474567634637, "dur": 6647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753474567641285, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753474567641381, "dur": 2211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753474567643629, "dur": 5399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753474567649158, "dur": 37678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567451763, "dur": 39468, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567491295, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_403638298D38E353.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753474567491750, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753474567491955, "dur": 295, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753474567492256, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567492329, "dur": 716, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1753474567493140, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753474567493374, "dur": 451, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753474567494314, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567494431, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567494609, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17571664448659802584.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753474567494705, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17571664448659802584.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753474567494955, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6591615775254759062.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753474567495260, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567495481, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567496006, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567496291, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567496797, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567497216, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567497520, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567497867, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567498628, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567499315, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567499543, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567499764, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567500030, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567500273, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567500510, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567500739, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567501019, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567501282, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567501649, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567501909, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567502145, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567502432, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567502675, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567502898, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567503126, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567503349, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567503572, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567503835, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567504096, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567504385, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567504800, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567505003, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567505312, "dur": 392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567505937, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753474567506259, "dur": 448, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567506712, "dur": 1568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753474567508280, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567508398, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753474567508630, "dur": 484, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567509120, "dur": 576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753474567509768, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567509860, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753474567510189, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567510249, "dur": 656, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753474567510989, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753474567511187, "dur": 1296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753474567512485, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567512565, "dur": 119800, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567632367, "dur": 2228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753474567634596, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567634724, "dur": 8669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753474567643417, "dur": 1738, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753474567645158, "dur": 2114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753474567647335, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567647582, "dur": 516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753474567648099, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567648189, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567648244, "dur": 430, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753474567648699, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1753474567648785, "dur": 1217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753474567650003, "dur": 36934, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567450876, "dur": 39660, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567490546, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_B91E1059028FB791.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753474567490769, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_104D37BB3A3C07D6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753474567490853, "dur": 512, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_104D37BB3A3C07D6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753474567491368, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_1716B00C0B77E4A2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753474567491748, "dur": 204, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753474567491985, "dur": 940, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753474567492946, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Config.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753474567493047, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753474567493116, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753474567493398, "dur": 587, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753474567494216, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567494703, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12320440560580502919.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753474567494961, "dur": 232, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8863518860715653438.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753474567495194, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567495433, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567495939, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567496111, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567496359, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567496574, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567496810, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567497074, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567497343, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567497611, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567498087, "dur": 630, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@5b86b5eab164\\Editor\\Models\\Contexts\\IVFXSubRenderer.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753474567497865, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567498751, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567498976, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567499226, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567499466, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567499714, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567499934, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567500204, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567500408, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567500665, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567500880, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567501080, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567501292, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567501541, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567502027, "dur": 597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567502624, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567502888, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567503105, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567503318, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567503573, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567504071, "dur": 607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567504679, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567504887, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567505099, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567505603, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567505735, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753474567505901, "dur": 1863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753474567507842, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753474567508023, "dur": 1980, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753474567510004, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567510151, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753474567510343, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567510417, "dur": 1421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753474567511913, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753474567512105, "dur": 444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753474567512617, "dur": 119812, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567632431, "dur": 2163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753474567634595, "dur": 3125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567637731, "dur": 1991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753474567639770, "dur": 1989, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753474567641790, "dur": 2898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753474567644730, "dur": 2638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753474567647369, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567647537, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567647661, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567648078, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567648140, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567648235, "dur": 226, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753474567648772, "dur": 679, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753474567649451, "dur": 37182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567450851, "dur": 39637, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567490570, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567490768, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567490860, "dur": 244, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_FB30C9CBD6C6FA04.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753474567491291, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567491552, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_AB3A7F15E6DCE458.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753474567491734, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567491851, "dur": 193, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7F371AF1A9E7F3FC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753474567492080, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753474567492276, "dur": 3701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753474567496053, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567496283, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567496498, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567496701, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567496952, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567497229, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567497473, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567497715, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567498036, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567498697, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567498925, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567499209, "dur": 543, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.high-definition@9746be8d99fb\\Runtime\\Material\\LTCAreaLight\\BRDF\\BRDF_GGX.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753474567499156, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567499946, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567500290, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567500536, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567500753, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567500965, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567501193, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567501423, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567501759, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567502269, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567502496, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567502729, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567502979, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567503183, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567503409, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567503643, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567503898, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567504176, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567504719, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567504958, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567505281, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567505601, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567505737, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753474567505986, "dur": 729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753474567506820, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753474567507140, "dur": 1084, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753474567508269, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753474567508467, "dur": 901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753474567509369, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567509562, "dur": 796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1753474567510409, "dur": 225, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567511204, "dur": 116928, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1753474567632376, "dur": 2211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753474567634647, "dur": 1896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.HighDefinition.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753474567636543, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567636601, "dur": 1922, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753474567638524, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567638609, "dur": 2023, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753474567640633, "dur": 507, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753474567641146, "dur": 1924, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753474567643115, "dur": 1963, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.HighDefinition.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753474567645131, "dur": 2037, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753474567647211, "dur": 2177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753474567649470, "dur": 37164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567451202, "dur": 39404, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567490618, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_69C50D06B8086FC2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753474567490773, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_030207EE0D57EC00.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753474567490850, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_030207EE0D57EC00.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753474567491303, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_E34D938713DA7A29.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753474567491801, "dur": 207, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753474567492229, "dur": 659, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753474567492922, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753474567493080, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753474567493171, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753474567493400, "dur": 193, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1753474567493625, "dur": 1153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1753474567494922, "dur": 193, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753474567495230, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567495740, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567496302, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567496555, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567497091, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567497338, "dur": 1237, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@5b86b5eab164\\Editor\\Utils\\VFXSystemBorder.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753474567497337, "dur": 1547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567498884, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567499110, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567499518, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567499757, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567500032, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567500267, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567500471, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567500700, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567500929, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567501134, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567501363, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567501691, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567501911, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567502150, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567502382, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567502658, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567502924, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567503166, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567503398, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567503723, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567503971, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567504536, "dur": 626, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@978211393e28\\InputSystem\\Editor\\AssetEditor\\PropertiesViewBase.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753474567504308, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567505242, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567505770, "dur": 474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753474567506267, "dur": 946, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753474567507272, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753474567507475, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753474567507674, "dur": 545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753474567508264, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753474567508487, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567508554, "dur": 676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753474567509315, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567509444, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567509585, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567510029, "dur": 3110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567513141, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753474567513326, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753474567513781, "dur": 123699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567637482, "dur": 2475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753474567640015, "dur": 4052, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753474567644115, "dur": 2995, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753474567647111, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753474567647177, "dur": 2632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753474567649895, "dur": 37200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567451523, "dur": 39349, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567491095, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_BE9E0DC7233247F5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753474567491241, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567491619, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567492051, "dur": 559, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1753474567492613, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_A5FBD79E52C0F38C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753474567492830, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567492957, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753474567493203, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753474567493343, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753474567493482, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1753474567493572, "dur": 233, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1753474567494227, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567494716, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6209987337720162677.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753474567494953, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753474567495207, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567495420, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567495796, "dur": 977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567496774, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567497418, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567497675, "dur": 950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567498625, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567498849, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567499080, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567499341, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567499567, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567499785, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567500276, "dur": 1036, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@4b6566c21d8b\\Editor\\Drawing\\Inspector\\PropertyDrawers\\SampleTexture2DNodePropertyDrawer.cs"}}, {"pid": 12345, "tid": 7, "ts": 1753474567500042, "dur": 1270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567501313, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567501605, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567501841, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567502060, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567502299, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567502569, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567502808, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567503055, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567503277, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567503515, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567503779, "dur": 379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567504158, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567504833, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567505604, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567505732, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753474567505952, "dur": 1309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753474567507261, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567507351, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753474567507533, "dur": 603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753474567508137, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567508261, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753474567508558, "dur": 1183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753474567509742, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567509838, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753474567510007, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567510067, "dur": 798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753474567510866, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567510950, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753474567511139, "dur": 764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753474567511993, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753474567512191, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567512261, "dur": 1600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753474567513916, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753474567514029, "dur": 1014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753474567515084, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753474567515193, "dur": 914, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753474567516155, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753474567516263, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753474567516615, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753474567516772, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753474567517101, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753474567517406, "dur": 117227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567634636, "dur": 1713, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753474567636393, "dur": 10261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753474567646713, "dur": 2723, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753474567649484, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753474567649548, "dur": 37823, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753474567451653, "dur": 39257, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753474567490919, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_0D9ADDA1DA1B6B1A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753474567491323, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753474567491623, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753474567491785, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_CFA7FCDC45E2E59C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753474567491910, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753474567492054, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753474567492143, "dur": 5113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753474567497327, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753474567497713, "dur": 6245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753474567504048, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753474567504340, "dur": 1150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753474567505491, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753474567505744, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753474567506046, "dur": 665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753474567506711, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753474567506847, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753474567507045, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753474567507556, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753474567507637, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753474567508125, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753474567508216, "dur": 815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753474567509032, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753474567509130, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753474567509283, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753474567509487, "dur": 656, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753474567510187, "dur": 6471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753474567516659, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753474567516848, "dur": 120839, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753474567637689, "dur": 2584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753474567640351, "dur": 2188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753474567642626, "dur": 4472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753474567647173, "dur": 2870, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753474567650137, "dur": 36755, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567451717, "dur": 39203, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567491325, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_28E9AAF2A54F3EEB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753474567491394, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_AA9E78718350C9F7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753474567491576, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567491800, "dur": 177, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753474567492174, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567492346, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567492528, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_CE0AEB9F73972D45.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753474567492694, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753474567492947, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753474567493043, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753474567493494, "dur": 218, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1753474567493859, "dur": 741, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753474567494711, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11294775302662683298.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753474567494969, "dur": 295, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2781082588993088484.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753474567495265, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567495571, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567495770, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567496214, "dur": 846, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@198cdf337d13\\Editor\\ProjectGeneration\\TypeCacheHelper.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753474567495982, "dur": 1536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567497518, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567497957, "dur": 1162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567499120, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567499474, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567499684, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567499886, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567501015, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567501776, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567502006, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567502317, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567502549, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567502838, "dur": 740, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@c07dec6d7e4d\\Runtime\\Utilities\\HashFNV1A32.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753474567502793, "dur": 996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567503790, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567504149, "dur": 339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567504489, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567504849, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567505422, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567505599, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567505740, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753474567505983, "dur": 1035, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753474567507018, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567507091, "dur": 1033, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567508130, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753474567508352, "dur": 770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753474567509178, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567509328, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567509613, "dur": 295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567509908, "dur": 2090, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567512000, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753474567512219, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753474567512653, "dur": 123931, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567636585, "dur": 2022, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753474567638608, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567638793, "dur": 2253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753474567641115, "dur": 2049, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753474567643165, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567643440, "dur": 4692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753474567648210, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753474567648438, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567648735, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567649141, "dur": 32375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753474567684443, "dur": 201, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "D:/Editors/6000.2.0b11/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 9, "ts": 1753474567684645, "dur": 1850, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "D:/Editors/6000.2.0b11/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 9, "ts": 1753474567686496, "dur": 100, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "D:/Editors/6000.2.0b11/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 9, "ts": 1753474567681517, "dur": 5084, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567451086, "dur": 39504, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567490738, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InsightsModule.dll_2B8BC9F317969ABE.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753474567490809, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InsightsModule.dll_2B8BC9F317969ABE.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753474567490895, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_84FAAD7E911C1447.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753474567491075, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_3A27AF984AC2B60B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753474567491221, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_3A27AF984AC2B60B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753474567491389, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_69EBAEAD3ED54413.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753474567491790, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_81DD88F7728AE14E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753474567492188, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753474567492338, "dur": 673, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753474567493079, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753474567493417, "dur": 190, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1753474567493632, "dur": 270, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1753474567493990, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753474567494096, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567494723, "dur": 366, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753474567495225, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567495444, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567495845, "dur": 922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567496768, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567497093, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567497369, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567497613, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567498064, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567498855, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567499105, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567499368, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567499614, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567499834, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567500121, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567500358, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567500568, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567500796, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567501010, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567501229, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567501454, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567501878, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567502108, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567502362, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567502852, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567503082, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567503291, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567503525, "dur": 339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567503864, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567504137, "dur": 373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567504511, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567504756, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567504969, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567505180, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567505602, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567505770, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753474567505992, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753474567506314, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567506374, "dur": 641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753474567507015, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567507334, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753474567507565, "dur": 506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753474567508072, "dur": 463, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567508548, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753474567508760, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567509064, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753474567509555, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567509780, "dur": 547, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567510333, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753474567510683, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753474567511197, "dur": 121175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567632375, "dur": 2218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753474567634641, "dur": 1865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753474567636554, "dur": 2922, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753474567639482, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567639640, "dur": 2032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Performance.Profile-Analyzer.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753474567641718, "dur": 3904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753474567645680, "dur": 2574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753474567648277, "dur": 467, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753474567648782, "dur": 1079, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753474567649892, "dur": 36765, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567451247, "dur": 39541, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567490798, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_E5B3CE3140DA7472.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753474567491061, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_971662CBFF7A1C60.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753474567491170, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_7EC3243254800FCF.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753474567491357, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_E25C6FF6026C9DB4.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753474567491483, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567491754, "dur": 178, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753474567491988, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1753474567492098, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753474567492191, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1753474567492367, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567492552, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567492830, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567492950, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753474567493118, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753474567493195, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753474567493291, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1753474567493395, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1753474567493499, "dur": 263, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1753474567493784, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753474567493862, "dur": 815, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753474567494727, "dur": 375, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753474567495221, "dur": 1078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567496300, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567496530, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567496767, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567497041, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567497296, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567497647, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567497997, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567498737, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567498946, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567499199, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567499479, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567499679, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567499885, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567500163, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567500366, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567500572, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567500797, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567501012, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567501238, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567501451, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567501762, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567502000, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567502426, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567502650, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567502904, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567503133, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567503363, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567503609, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567503906, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567504174, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567504619, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567504808, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567505014, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567505227, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567506138, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753474567506386, "dur": 1282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753474567507704, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753474567508229, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567508305, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567508544, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753474567509279, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753474567509450, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567509571, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753474567510103, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567510172, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753474567510352, "dur": 358, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567510716, "dur": 1351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753474567512150, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753474567512334, "dur": 736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753474567513136, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753474567513305, "dur": 522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753474567513882, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753474567514050, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753474567514499, "dur": 120128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567634628, "dur": 4044, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753474567638673, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567638787, "dur": 2170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753474567640958, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567641136, "dur": 3957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753474567645094, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567645194, "dur": 2116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753474567647311, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567647718, "dur": 238, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753474567648210, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753474567648436, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567648647, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567648778, "dur": 707, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753474567649505, "dur": 37126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567451417, "dur": 39431, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567490905, "dur": 204, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_427536A3014E122B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753474567491186, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_D1BD6482569D1D1B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753474567491330, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567491891, "dur": 865, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1753474567492865, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567492962, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753474567493194, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753474567493397, "dur": 299, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753474567493841, "dur": 288, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753474567494390, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753474567494661, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3064155424177982801.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753474567494835, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17296387151066238333.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753474567494985, "dur": 479, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753474567495465, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567495817, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567496444, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567496657, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567496938, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567497625, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567498070, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567498943, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567499179, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567499540, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567499754, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567500020, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567500512, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567500971, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567501278, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567501708, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567502371, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567502601, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567502840, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567503076, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567503293, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567503542, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567503844, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567504195, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567504728, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567504942, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567505165, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567505433, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567505612, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567505939, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753474567506161, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567506260, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753474567506834, "dur": 523, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567507371, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567507482, "dur": 842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753474567508370, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753474567508595, "dur": 552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753474567509148, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567509343, "dur": 221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567509596, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567509843, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753474567510010, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567510070, "dur": 571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753474567510719, "dur": 121649, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567632370, "dur": 2224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753474567634595, "dur": 3174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567637779, "dur": 2420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753474567640200, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753474567640411, "dur": 4117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753474567644573, "dur": 5256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753474567649878, "dur": 37267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753474567696840, "dur": 3346, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 16152, "tid": 969, "ts": 1753474567707282, "dur": 10670, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 16152, "tid": 969, "ts": 1753474567718014, "dur": 1191, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 16152, "tid": 969, "ts": 1753474567704202, "dur": 15050, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}