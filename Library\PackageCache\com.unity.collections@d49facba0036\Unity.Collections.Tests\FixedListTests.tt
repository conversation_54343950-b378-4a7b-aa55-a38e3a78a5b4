<#/*THIS IS A T4 FILE - see t4_text_templating.md for what it is and how to run codegen*/#>
<#@ template debug="True" #>
<#@ output extension=".gen.cs" encoding="utf-8" #>
<#@ assembly name="System.Core" #>
<#@ import namespace="System.Globalization" #>

//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     TextTransform Samples/Packages/com.unity.collections/Unity.Collections.Tests/FixedListTests.tt
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Threading;
using NUnit.Framework;
using Unity.Collections;
using Unity.Collections.LowLevel.Unsafe;
using Unity.Collections.Tests;
using UnityEngine;

internal class FixedListTests : CollectionsTestFixture
{
    struct NonComparableStruct
    {
        public int a;
    }

    struct DescendingComparer<T> : IComparer<T> where T : IComparable<T>
    {
        public int Compare(T x, T y) => y.CompareTo(x);
    }

<#
    {
        var SIZES = new int[] {32, 64, 128};
        for (var size = 0; size < 3; ++size)
        {
            var BYTES = SIZES[size];
            var TYPENAME = String.Format("FixedList{0}Bytes", BYTES);
#>
    [Test]
    public void <#=TYPENAME #>DebugView()
    {
        var list = new <#=TYPENAME #><NonComparableStruct>();
        CollectionAssert.IsEmpty(new <#=TYPENAME #>DebugView<NonComparableStruct>(list).Items);

        var reference = new []
        {
            new NonComparableStruct{ a = 123 },
            new NonComparableStruct{ a = 234 },
            new NonComparableStruct{ a = 345 },
        };

        list.Add(reference[0]);
        list.Add(reference[1]);
        list.Add(reference[2]);

        CollectionAssert.AreEqual(reference, new <#=TYPENAME #>DebugView<NonComparableStruct>(list).Items);
    }

<#
        }
    }
    {
        var TYPES = new string[] {"byte", "int", "float"};
        var TYPESIZES = new int[] {1, 4, 4};
        var SIZES = new int[] {32, 64, 128};
        for (var type = 0; type < 3; ++type)
        {
            for (var size = 0; size < 3; ++size)
            {
                var BYTES = SIZES[size];
                var TYPE = TYPES[type];
                var TYPESIZE = TYPESIZES[type];
                var MAXLENGTH = BYTES - 2;
                var TYPENAME = String.Format("FixedList{0}Bytes", BYTES);
                var TYPENAME_IDENTIFIER = String.Format("FixedList{0}{1}_", BYTES, new CultureInfo("en-US").TextInfo.ToTitleCase(TYPE));
                var EXPECTEDCAPACITY = (BYTES - 2) / TYPESIZE;
                var BUFFERTYPE = String.Format("FixedBytes{0}Align8", BYTES);
#>

    public readonly unsafe struct <#=TYPENAME_IDENTIFIER #>Wrapper
    {
        readonly <#=TYPENAME #><<#=TYPE #>> _values;
        internal <#=TYPE #>* Values => (<#=TYPE #>*)_values.Buffer;
        public <#=TYPENAME_IDENTIFIER #>Wrapper(<#=TYPE #> x, <#=TYPE #> y)
        {
            _values = new <#=TYPENAME #><<#=TYPE #>>();
            _values.Add(x);
            _values.Add(y);
            _values.Sort();
        }
    }

    unsafe void <#=TYPENAME_IDENTIFIER #>ReadonlyWorksFunc1(in <#=TYPENAME_IDENTIFIER #>Wrapper list, <#=TYPE #>* originalValues)
    {
        <#=TYPE #>* values = list.Values;
        Assert.AreEqual(originalValues[0], values[0], "Values[0] mismatch in func1");
        Assert.AreEqual(originalValues[1], values[1], "Values[1] mismatch in func1");
        Assert.AreEqual((ulong)originalValues, (ulong)values, "Buffer pointer mismatch in func1");
    }

    [Test]
    public unsafe void <#=TYPENAME_IDENTIFIER #>ReadonlyWorks()
    {
        var list = new <#=TYPENAME_IDENTIFIER #>Wrapper(17, 23);
        <#=TYPE #>* values = list.Values;
        <#=TYPENAME_IDENTIFIER #>ReadonlyWorksFunc1(list, values);
    }

    [Test]
    public unsafe void <#=TYPENAME_IDENTIFIER #><#=BUFFERTYPE#>IsAlignedGeneric()
    {
        var list = new FixedList<<#=TYPE#>,<#=BUFFERTYPE#>>();

        Assert.IsTrue((((ulong)list.Buffer) & (sizeof(<#=TYPE#>) - 1)) == 0);
    }

    [Test]
    public void <#=TYPENAME_IDENTIFIER #><#=BUFFERTYPE#>ToNativeArrayWorksGeneric()
    {
        var list = new FixedList<<#=TYPE#>,<#=BUFFERTYPE#>>();

        for(var i = 0; i < <#=EXPECTEDCAPACITY#>; ++i)
            list.Add((<#=TYPE#>)(i * 123 + 234));

        using(var array = list.ToNativeArray(Allocator.Temp))
        {
            for(var i = 0; i < <#=EXPECTEDCAPACITY#>; ++i)
                Assert.AreEqual((<#=TYPE#>)(i * 123 + 234), array[i]);
        }
    }

    [Test]
    public void <#=TYPENAME_IDENTIFIER #>ToNativeArrayWorks()
    {
        var list = new <#=TYPENAME #><<#=TYPE#>>();

        for(var i = 0; i < <#=EXPECTEDCAPACITY#>; ++i)
            list.Add((<#=TYPE#>)(i * 123 + 234));

        using(var array = list.ToNativeArray(Allocator.Temp))
        {
            for(var i = 0; i < <#=EXPECTEDCAPACITY#>; ++i)
                Assert.AreEqual((<#=TYPE#>)(i * 123 + 234), array[i]);
        }
    }

    [Test]
    public void <#=TYPENAME_IDENTIFIER #>GenericHasExpectedLayout()
    {
        var actual = new <#=TYPENAME #><<#=TYPE#>>();
        for(var i = 0; i < <#=EXPECTEDCAPACITY #>; ++i)
          actual.Add((<#=TYPE #>)i);
        unsafe
        {
            var e = stackalloc byte[<#=BYTES #>];
            e[0] = (byte)((<#=EXPECTEDCAPACITY #> >> 0) & 0xFF);
            e[1] = (byte)((<#=EXPECTEDCAPACITY #> >> 8) & 0xFF);
            for(var i = 0; i < <#=EXPECTEDCAPACITY #>; ++i)
            {
              var s = (<#=TYPE #>)i;
              UnsafeUtility.MemCpy(e + 2 + FixedList.PaddingBytes<<#=TYPE#>>() + sizeof(<#=TYPE #>) * i, &s, sizeof(<#=TYPE #>));
            }
            Assert.AreEqual(0, UnsafeUtility.MemCmp(e, &actual.data, <#=BYTES #>));
        }
    }

    [Test]
    [TestRequiresDotsDebugOrCollectionChecks]
    public void <#=TYPENAME_IDENTIFIER #>GenericHasExpectedCapacity()
    {
        var list = new <#=TYPENAME #><<#=TYPE#>>();
        var expectedCapacity = list.Capacity;
        for(int i = 0; i < expectedCapacity; ++i)
            list.Add((<#=TYPE #>)i);

        Assert.Throws<IndexOutOfRangeException> (() => { list.Add((<#=TYPE #>)expectedCapacity); });
    }
<#
                if (EXPECTEDCAPACITY >= 5)
                {
#>
    [Test]
    public unsafe void <#=TYPENAME_IDENTIFIER #>GenericAdd()
    {
        var list = new <#=TYPENAME #><<#=TYPE#>>();
        var capacity = list.Capacity;
        for (var i = 0; i < capacity; ++i)
        {
            list.Add((<#=TYPE#>)i);
            Assert.AreEqual(i + 1, list.Length);
            Assert.AreEqual(i, list[i]);
        }

#if ENABLE_UNITY_COLLECTIONS_CHECKS || UNITY_DOTS_DEBUG
        Assert.Throws<IndexOutOfRangeException>(() => { list.Add(0); });
#endif
        Assert.AreEqual(capacity, list.Length); // Verify length didn't change
    }

    [Test]
    public unsafe void <#=TYPENAME_IDENTIFIER #>GenericAddRange()
    {
        var list = new <#=TYPENAME #><<#=TYPE#>>();
        var capacity = list.Capacity;
        var items = stackalloc <#=TYPE#>[capacity];
        for (var i = 0; i < capacity; ++i)
            items[i] = (<#=TYPE#>)i;

        var half = capacity / 2;
        list.AddRange(items, half);
        Assert.AreEqual(half, list.Length);
        for (var i = 0; i < half; ++i)
            Assert.AreEqual(i, list[i]);

#if ENABLE_UNITY_COLLECTIONS_CHECKS || UNITY_DOTS_DEBUG
        Assert.Throws<IndexOutOfRangeException>(() => { list.AddRange(items, capacity); });
#endif
        Assert.AreEqual(half, list.Length); // Verify length didn't change
    }

    [Test]
    public unsafe void <#=TYPENAME_IDENTIFIER #>GenericAddNoResize()
    {
        var list = new <#=TYPENAME #><<#=TYPE#>>();
        var capacity = list.Capacity;
        for (var i = 0; i < capacity; ++i)
        {
            list.AddNoResize((<#=TYPE#>)i);
            Assert.AreEqual(i + 1, list.Length);
            Assert.AreEqual(i, list[i]);
        }

#if ENABLE_UNITY_COLLECTIONS_CHECKS || UNITY_DOTS_DEBUG
        Assert.Throws<IndexOutOfRangeException>(() => { list.AddNoResize(0); });
#endif
        Assert.AreEqual(capacity, list.Length); // Verify length didn't change
    }

    [Test]
    public unsafe void <#=TYPENAME_IDENTIFIER #>GenericAddRangeNoResize()
    {
        var list = new <#=TYPENAME #><<#=TYPE#>>();
        var capacity = list.Capacity;
        var items = stackalloc <#=TYPE#>[capacity];
        for (var i = 0; i < capacity; ++i)
            items[i] = (<#=TYPE#>)i;

        var half = capacity / 2;
        list.AddRangeNoResize(items, half);
        Assert.AreEqual(half, list.Length);
        for (var i = 0; i < half; ++i)
            Assert.AreEqual(i, list[i]);

#if ENABLE_UNITY_COLLECTIONS_CHECKS || UNITY_DOTS_DEBUG
        Assert.Throws<IndexOutOfRangeException>(() => { list.AddRangeNoResize(items, capacity); });
#endif
        Assert.AreEqual(half, list.Length); // Verify length didn't change
    }

    [Test]
    public void <#=TYPENAME_IDENTIFIER #>GenericAddReplicate()
    {
        var list = new <#=TYPENAME #><<#=TYPE#>>();
        list.AddReplicate(42, 2);
        Assert.AreEqual(2, list.Length);
        foreach (var item in list)
            Assert.AreEqual(42, item);

        list.AddReplicate(42, 3);
        Assert.AreEqual(5, list.Length);
        foreach (var item in list)
            Assert.AreEqual(42, item);

#if ENABLE_UNITY_COLLECTIONS_CHECKS || UNITY_DOTS_DEBUG
        Assert.Throws<IndexOutOfRangeException>(() => { list.AddReplicate(42, 1000); });
#endif
        Assert.AreEqual(5, list.Length); // Verify length didn't change
    }

    [Test]
    public void <#=TYPENAME_IDENTIFIER #>GenericInsertRangeWithBeginEnd()
    {
        var list = new <#=TYPENAME #><<#=TYPE#>>() { 0, 3, 4 };
        list.InsertRangeWithBeginEnd(1,3);
        list[1] = 1;
        list[2] = 2;
        for(var i = 0; i < 5; ++i)
            Assert.AreEqual(i, list[i]);
    }

    [Test]
    public void <#=TYPENAME_IDENTIFIER #>GenericInsertRange()
    {
        var list = new <#=TYPENAME #><<#=TYPE#>>() { 0, 3, 4 };
        Assert.AreEqual(3, list.Length);

        list.InsertRange(1, 2);
        Assert.AreEqual(5, list.Length);
        list[1] = 1;
        list[2] = 2;

        Assert.DoesNotThrow(() => list.InsertRange(1, 0));
        Assert.AreEqual(5, list.Length);

        for(var i = 0; i < 5; ++i)
            Assert.AreEqual(i, list[i]);
    }

    [Test]
    public void <#=TYPENAME_IDENTIFIER #>GenericRemoveRange()
    {
        var list = new <#=TYPENAME #><<#=TYPE#>>() { 0, 3, 3, 1, 2 };
        list.RemoveRange(1, 2);
        for(var i = 0; i < 3; ++i)
            Assert.AreEqual(i, list[i]);
    }

    [Test]
    public void <#=TYPENAME_IDENTIFIER #>GenericInsert()
    {
        var list = new <#=TYPENAME #><<#=TYPE#>>() { 0, 3, 4 };
        list.Insert(1,1);
        list.Insert(2,2);
        for(var i = 0; i < 5; ++i)
            Assert.AreEqual(i, list[i]);
    }

    [Test]
    public void <#=TYPENAME_IDENTIFIER #>GenericRemoveAt()
    {
        var list = new <#=TYPENAME #><<#=TYPE#>>() { 0, 3, 3, 1, 2 };
        list.RemoveAt(1);
        list.RemoveAt(1);
        for(var i = 0; i < 3; ++i)
            Assert.AreEqual(i, list[i]);
    }

    [Test]
    public void <#=TYPENAME_IDENTIFIER #>GenericRemove()
    {
        var list = new <#=TYPENAME #><<#=TYPE#>>() { 0, 3, 3, 1, 2 };
        Assert.True(list.Remove((<#=TYPE#>)3));
        Assert.True(list.Remove((<#=TYPE#>)3));
        for(var i = 0; i < 3; ++i)
            Assert.AreEqual(i, list[i]);
    }

    [Test]
    public void <#=TYPENAME_IDENTIFIER #>GenericRemoveSwapBack()
    {
        var list = new <#=TYPENAME #><<#=TYPE#>>() { 0, 3, 3, 2, 1 };
        Assert.True(list.RemoveSwapBack((<#=TYPE#>)3));
        Assert.True(list.RemoveSwapBack((<#=TYPE#>)3));
        for(var i = 0; i < 3; ++i)
            Assert.AreEqual(i, list[i]);
    }

    [Test]
    public void <#=TYPENAME_IDENTIFIER #>GenericSort()
    {
        var list = new <#=TYPENAME #><<#=TYPE#>>();
        for(var i = 0; i < 5; ++i)
          list.Add((<#=TYPE #>)(4-i));
        list.Sort();
        for(var i = 0; i < 5; ++i)
            Assert.AreEqual(i, list[i]);
    }

    [Test]
    public void <#=TYPENAME_IDENTIFIER #>GenericSortCustomComparer()
    {
        var list = new <#=TYPENAME #><<#=TYPE#>>();
        for(var i = 0; i < 5; ++i)
          list.Add((<#=TYPE #>)(i));
        list.Sort(new DescendingComparer<<#=TYPE#>>());
        for(var i = 0; i < 5; ++i)
            Assert.AreEqual(4-i, list[i]);
    }

    [Test]
    public unsafe void <#=TYPENAME_IDENTIFIER #>IndexOf()
    {
        var list = new <#=TYPENAME #><<#=TYPE#>>() { 123, 178 };
        bool r0 = false, r1 = false, r2 = false;

        GCAllocRecorder.ValidateNoGCAllocs(() =>
        {
            r0 = -1 != list.IndexOf((<#=TYPE#>)145);
            r1 = list.Contains((<#=TYPE#>)123);
            r2 = list.Contains((<#=TYPE#>)178);
        });

        Assert.False(r0);
        Assert.True(r1);
        Assert.True(r2);
    }

<#
                }
            }
            for (var size = 0; size < 3; ++size)
            {
                var BYTES = SIZES[size];
                var TYPE = TYPES[type];
                var TYPESIZE = TYPESIZES[type];
                var MAXLENGTH = BYTES - 2;
                var TYPENAME = String.Format("FixedList{0}Bytes<{1}>", BYTES, TYPE);
                var TYPENAME_IDENTIFIER = String.Format("FixedList{0}{1}_", BYTES, new CultureInfo("en-US").TextInfo.ToTitleCase(TYPE));
                var EXPECTEDCAPACITY = (BYTES - 2) / TYPESIZE;
#>

    [Test]
    public void <#=TYPENAME_IDENTIFIER #>HasExpectedLayout()
    {
        var actual = new <#=TYPENAME #>();
        for(var i = 0; i < <#=EXPECTEDCAPACITY #>; ++i)
          actual.Add((<#=TYPE #>)i);
        unsafe
        {
            var e = stackalloc byte[<#=BYTES #>];
            e[0] = (byte)((<#=EXPECTEDCAPACITY #> >> 0) & 0xFF);
            e[1] = (byte)((<#=EXPECTEDCAPACITY #> >> 8) & 0xFF);
            for(var i = 0; i < <#=EXPECTEDCAPACITY #>; ++i)
            {
              var s = (<#=TYPE #>)i;
              UnsafeUtility.MemCpy(e + 2 + FixedList.PaddingBytes<<#=TYPE#>>() + sizeof(<#=TYPE #>) * i, &s, sizeof(<#=TYPE #>));
            }
            Assert.AreEqual(0, UnsafeUtility.MemCmp(e, &actual.data, <#=BYTES #>));
        }
    }

    [Test]
    [TestRequiresDotsDebugOrCollectionChecks]
    public void <#=TYPENAME_IDENTIFIER #>HasExpectedCapacity()
    {
        var list = new <#=TYPENAME #>();
        var expectedCapacity = list.Capacity;
        for(int i = 0; i < expectedCapacity; ++i)
            list.Add((<#=TYPE #>)i);

        Assert.Throws<IndexOutOfRangeException> (() => { list.Add((<#=TYPE #>)expectedCapacity); });
    }

    class ScriptableObject<#=TYPENAME_IDENTIFIER#> : UnityEngine.ScriptableObject
    {
        public <#=TYPENAME#> List;
    }

    [Test]
    public void <#=TYPENAME_IDENTIFIER #>Serializes()
    {
        var a = UnityEngine.ScriptableObject.CreateInstance<ScriptableObject<#=TYPENAME_IDENTIFIER#> >();
        for(int i = 0; i < a.List.Capacity; ++i)
            a.List.Add((<#=TYPE #>)i);
        var b = UnityEngine.Object.Instantiate(a);
        CollectionAssert.AreEqual(a.List, b.List);
    }

<#
                if (EXPECTEDCAPACITY >= 5)
                {
#>
    [Test]
    public unsafe void <#=TYPENAME_IDENTIFIER #>Add()
    {
        var list = new <#=TYPENAME #>();
        var capacity = list.Capacity;
        for (var i = 0; i < capacity; ++i)
        {
            list.Add((<#=TYPE#>)i);
            Assert.AreEqual(i + 1, list.Length);
            Assert.AreEqual(i, list[i]);
        }

#if ENABLE_UNITY_COLLECTIONS_CHECKS || UNITY_DOTS_DEBUG
        Assert.Throws<IndexOutOfRangeException>(() => { list.Add(0); });
#endif
        Assert.AreEqual(capacity, list.Length); // Verify length didn't change
    }

    [Test]
    public unsafe void <#=TYPENAME_IDENTIFIER #>AddRange()
    {
        var list = new <#=TYPENAME #>();
        var capacity = list.Capacity;
        var items = stackalloc <#=TYPE#>[capacity];
        for (var i = 0; i < capacity; ++i)
            items[i] = (<#=TYPE#>)i;

        var half = capacity / 2;
        list.AddRange(items, half);
        Assert.AreEqual(half, list.Length);
        for (var i = 0; i < half; ++i)
            Assert.AreEqual(i, list[i]);

#if ENABLE_UNITY_COLLECTIONS_CHECKS || UNITY_DOTS_DEBUG
        Assert.Throws<IndexOutOfRangeException>(() => { list.AddRange(items, capacity); });
#endif
        Assert.AreEqual(half, list.Length); // Verify length didn't change
    }

    [Test]
    public unsafe void <#=TYPENAME_IDENTIFIER #>AddNoResize()
    {
        var list = new <#=TYPENAME #>();
        var capacity = list.Capacity;
        for (var i = 0; i < capacity; ++i)
        {
            list.AddNoResize((<#=TYPE#>)i);
            Assert.AreEqual(i + 1, list.Length);
            Assert.AreEqual(i, list[i]);
        }

#if ENABLE_UNITY_COLLECTIONS_CHECKS || UNITY_DOTS_DEBUG
        Assert.Throws<IndexOutOfRangeException>(() => { list.AddNoResize(0); });
#endif
        Assert.AreEqual(capacity, list.Length); // Verify length didn't change
    }

    [Test]
    public unsafe void <#=TYPENAME_IDENTIFIER #>AddRangeNoResize()
    {
        var list = new <#=TYPENAME #>();
        var capacity = list.Capacity;
        var items = stackalloc <#=TYPE#>[capacity];
        for (var i = 0; i < capacity; ++i)
            items[i] = (<#=TYPE#>)i;

        var half = capacity / 2;
        list.AddRangeNoResize(items, half);
        Assert.AreEqual(half, list.Length);
        for (var i = 0; i < half; ++i)
            Assert.AreEqual(i, list[i]);

#if ENABLE_UNITY_COLLECTIONS_CHECKS || UNITY_DOTS_DEBUG
        Assert.Throws<IndexOutOfRangeException>(() => { list.AddRangeNoResize(items, capacity); });
#endif
        Assert.AreEqual(half, list.Length); // Verify length didn't change
    }

    [Test]
    public void <#=TYPENAME_IDENTIFIER #>AddReplicate()
    {
        var list = new <#=TYPENAME #>();
        list.AddReplicate(42, 2);
        Assert.AreEqual(2, list.Length);
        foreach (var item in list)
            Assert.AreEqual(42, item);

        list.AddReplicate(42, 3);
        Assert.AreEqual(5, list.Length);
        foreach (var item in list)
            Assert.AreEqual(42, item);

#if ENABLE_UNITY_COLLECTIONS_CHECKS || UNITY_DOTS_DEBUG
        Assert.Throws<IndexOutOfRangeException>(() => { list.AddReplicate(42, 1000); });
#endif
        Assert.AreEqual(5, list.Length); // Verify length didn't change
    }

    [Test]
    public void <#=TYPENAME_IDENTIFIER #>InsertRangeWithBeginEnd()
    {
        var list = new <#=TYPENAME #>() { 0, 3, 4 };
        list.InsertRangeWithBeginEnd(1,3);
        list[1] = 1;
        list[2] = 2;
        for(var i = 0; i < 5; ++i)
            Assert.AreEqual(i, list[i]);
    }

    [Test]
    public void <#=TYPENAME_IDENTIFIER #>InsertRange()
    {
        var list = new <#=TYPENAME #>() { 0, 3, 4 };
        Assert.AreEqual(3, list.Length);

        list.InsertRange(1, 2);
        Assert.AreEqual(5, list.Length);
        list[1] = 1;
        list[2] = 2;

        Assert.DoesNotThrow(() => list.InsertRange(1, 0));
        Assert.AreEqual(5, list.Length);

        for(var i = 0; i < 5; ++i)
            Assert.AreEqual(i, list[i]);
    }

    private static void Expected(ref <#=TYPENAME #> container, int expectedLength, int[] expected)
    {
        Assert.AreEqual(expectedLength == 0, container.IsEmpty);
        Assert.AreEqual(container.Length, expectedLength);
        for (var i = 0; i < container.Length; ++i)
        {
            Assert.AreEqual(expected[i], container[i]);
        }
    }

    [Test]
    public void <#=TYPENAME_IDENTIFIER #>RemoveAt()
    {
        var list = new <#=TYPENAME #>() { 0, 3, 3, 1, 2 };
        list.RemoveAt(1);
        list.RemoveAt(1);
        Expected(ref list, 3, new int[] { 0, 1, 2 });
    }

    [Test]
    public void <#=TYPENAME_IDENTIFIER #>Remove()
    {
        var list = new <#=TYPENAME #>() { 0, 3, 3, 1, 2 };
        Assert.True(list.Remove((<#=TYPE#>)3));
        Assert.True(list.Remove((<#=TYPE#>)3));
        Expected(ref list, 3, new int[] { 0, 1, 2 });
    }

    [Test]
    public void <#=TYPENAME_IDENTIFIER #>RemoveSwapBack()
    {
        var list = new <#=TYPENAME #>() { 0, 3, 3, 2, 1 };
        Assert.True(list.RemoveSwapBack((<#=TYPE#>)3));
        Assert.True(list.RemoveSwapBack((<#=TYPE#>)3));
        Expected(ref list, 3, new int[] { 0, 1, 2 });
    }

    [Test]
    public void <#=TYPENAME_IDENTIFIER #>RemoveRange()
    {
        var list = new <#=TYPENAME #>() { 0, 3, 3, 1, 2 };
        list.RemoveRange(1, 2);
        Expected(ref list, 3, new int[] { 0, 1, 2 });
    }

    [Test]
    public void <#=TYPENAME_IDENTIFIER #>RemoveAtSwapBack()
    {
        var list = new <#=TYPENAME #>() { 0, 3, 3, 1, 2 };
        list.RemoveAtSwapBack(1);
        list.RemoveAtSwapBack(1);
        Expected(ref list, 3, new int[] { 0, 1, 3 });
    }

    [Test]
    public void <#=TYPENAME_IDENTIFIER #>RemoveRangeSwapBack()
    {
        var list = new <#=TYPENAME #>() { 0, 3, 3, 1, 2 };
        list.RemoveRangeSwapBack(1, 2);
        Expected(ref list, 3, new int[] { 0, 1, 2 });
    }

    [Test]
    public void <#=TYPENAME_IDENTIFIER #>Insert()
    {
        var list = new <#=TYPENAME #>() { 0, 3, 4 };
        list.Insert(1,1);
        list.Insert(2,2);
        for(var i = 0; i < 5; ++i)
            Assert.AreEqual(i, list[i]);
    }

    [Test]
    public void <#=TYPENAME_IDENTIFIER #>Sort()
    {
        var list = new <#=TYPENAME #>();
        for(var i = 0; i < 5; ++i)
          list.Add((<#=TYPE #>)(4-i));
        list.Sort();
        for(var i = 0; i < 5; ++i)
            Assert.AreEqual(i, list[i]);
    }

<#
                }

                foreach (var OTHERBYTES in SIZES)
                {
                    if (OTHERBYTES != BYTES)
                    {
                        var OTHERCAPACITY = (OTHERBYTES - 2) / TYPESIZE;
                        var OTHERTYPENAME = String.Format("FixedList{0}Bytes<{1}>", OTHERBYTES, TYPE);
                        var OTHERTYPENAME_IDENTIFIER = String.Format("_FixedList{0}{1}", OTHERBYTES, new CultureInfo("en-US").TextInfo.ToTitleCase(TYPE));
#>
    [Test]
    public void <#=TYPENAME_IDENTIFIER #>To<#=OTHERTYPENAME_IDENTIFIER #>()
    {
        var a = new <#=TYPENAME #>();
        for(var i = 0; i < <#=EXPECTEDCAPACITY #>; ++i)
            a.Add((<#=TYPE #>)i);
<#
                        if (EXPECTEDCAPACITY <= OTHERCAPACITY)
                        {
                            WriteLine("        var b = new {0}(a);", OTHERTYPENAME);
                            WriteLine("        for(var i = 0; i < {0}; ++i)", EXPECTEDCAPACITY);
                            WriteLine("            Assert.AreEqual(({0})i, b[i]);", TYPE);
                        }
                        else
                            WriteLine(
                                "#if ENABLE_UNITY_COLLECTIONS_CHECKS || UNITY_DOTS_DEBUG\n        Assert.Throws<IndexOutOfRangeException> (() => {{ var b = new {0}(a); }} );\n#endif",
                                OTHERTYPENAME);
#>
    }
<#
                    }
                }
            }
        }
    }
#>

}
