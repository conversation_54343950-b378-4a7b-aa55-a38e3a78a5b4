{"exempts": {"PVP-22-1": {"errors": ["Unity.Collections.LowLevel.ILSupport/source~/Unity.Collections.LowLevel.ILSupport.CodeGen/Unity.Collections.LowLevel.ILSupport.CodeGen.asmdef", "Unity.Collections.LowLevel.ILSupport/source~/Unity.Collections.LowLevel.ILSupport.CodeGen/Unity.Collections.LowLevel.ILSupport.CodeGen.asmdef.meta"]}, "PVP-25-1": {"errors": ["Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"]}, "PVP-41-1": {"errors": ["CHANGELOG.md: line 3: Unreleased section is not allowed for public release"]}, "PVP-150-1": {"errors": ["Unity.Collections.AllocatorManager: void* Allocate(ref T, int, int, int): suspicious '///' triple-slash inside XmlDoc comment", "Unity.Collections.AllocatorManager: void* Allocate(ref T, int, int, int): text or XML content outside a top-level tag", "Unity.Collections.AllocatorManager.AllocatorHandle: bool Equals(object): <seealso> in block context; use <see> instead", "Unity.Collections.AllocatorManager.AllocatorHandle: bool Equals(AllocatorHandle): <seealso> in block context; use <see> instead", "Unity.Collections.AllocatorManager.AllocatorHandle: bool Equals(Allocator): <seealso> in block context; use <see> instead", "Unity.Collections.AllocatorManager.AllocatorHandle: bool op_Equality(AllocatorHandle, AllocatorHandle): <seealso> in block context; use <see> instead", "Unity.Collections.AllocatorManager.AllocatorHandle: bool op_Inequality(AllocatorHandle, AllocatorHandle): <seealso> in block context; use <see> instead", "Unity.Collections.AllocatorManager.AllocatorHandle: bool op_LessThan(AllocatorHandle, AllocatorHandle): <seealso> in block context; use <see> instead", "Unity.Collections.AllocatorManager.AllocatorHandle: bool op_GreaterThan(AllocatorHandle, AllocatorHandle): <seealso> in block context; use <see> instead", "Unity.Collections.AllocatorManager.AllocatorHandle: bool op_LessThanOrEqual(AllocatorHandle, AllocatorHandle): <seealso> in block context; use <see> instead", "Unity.Collections.AllocatorManager.AllocatorHandle: bool op_GreaterThanOrEqual(AllocatorHandle, AllocatorHandle): <seealso> in block context; use <see> instead", "Unity.Collections.AllocatorManager.AllocatorHandle: int CompareTo(AllocatorHandle): <seealso> in block context; use <see> instead", "Unity.Collections.AllocatorManager.Block: Alignment: unexpected <param name=\"value\">", "Unity.Collections.DataStreamReader: <seealso> in block context; use <see> instead", "Unity.Collections.DataStreamReader: mixed block and inline content in <remarks>; wrap inline content in <para>", "Unity.Collections.DataStreamWriter: <seealso> in block context; use <see> instead", "Unity.Collections.DataStreamWriter: mixed block and inline content in <remarks>; wrap inline content in <para>", "Unity.Collections.FixedString32Bytes: UTF8MaxLengthInBytes: unexpected <returns>; use <value> instead", "Unity.Collections.FixedString32Bytes: Length: unexpected <param name=\"value\">", "Unity.Collections.FixedString64Bytes: UTF8MaxLengthInBytes: unexpected <returns>; use <value> instead", "Unity.Collections.FixedString64Bytes: Length: unexpected <param name=\"value\">", "Unity.Collections.FixedString128Bytes: UTF8MaxLengthInBytes: unexpected <returns>; use <value> instead", "Unity.Collections.FixedString128Bytes: Length: unexpected <param name=\"value\">", "Unity.Collections.FixedString512Bytes: UTF8MaxLengthInBytes: unexpected <returns>; use <value> instead", "Unity.Collections.FixedString512Bytes: Length: unexpected <param name=\"value\">", "Unity.Collections.FixedString4096Bytes: UTF8MaxLengthInBytes: unexpected <returns>; use <value> instead", "Unity.Collections.FixedString4096Bytes: Length: unexpected <param name=\"value\">", "Unity.Collections.FixedStringMethods: multiple <summary> tags", "Unity.Collections.FixedStringMethods: FormatError AppendFormat(ref T, in U, in T0): text or XML content outside a top-level tag", "Unity.Collections.FixedStringMethods: FormatError AppendFormat(ref T, in U, in T0, in T1): text or XML content outside a top-level tag", "Unity.Collections.FixedStringMethods: FormatError AppendFormat(ref T, in U, in T0, in T1, in T2): text or XML content outside a top-level tag", "Unity.Collections.FixedStringMethods: FormatError AppendFormat(ref T, in U, in T0, in T1, in T2, in T3): text or XML content outside a top-level tag", "Unity.Collections.FixedStringMethods: FormatError AppendFormat(ref T, in U, in T0, in T1, in T2, in T3, in T4): text or XML content outside a top-level tag", "Unity.Collections.FixedStringMethods: FormatError AppendFormat(ref T, in U, in T0, in T1, in T2, in T3, in T4, in T5): text or XML content outside a top-level tag", "Unity.Collections.FixedStringMethods: FormatError AppendFormat(ref T, in U, in T0, in T1, in T2, in T3, in T4, in T5, in T6): text or XML content outside a top-level tag", "Unity.Collections.FixedStringMethods: FormatError AppendFormat(ref T, in U, in T0, in T1, in T2, in T3, in T4, in T5, in T6, in T7): text or XML content outside a top-level tag", "Unity.Collections.FixedStringMethods: FormatError AppendFormat(ref T, in U, in T0, in T1, in T2, in T3, in T4, in T5, in T6, in T7, in T8): text or XML content outside a top-level tag", "Unity.Collections.FixedStringMethods: FormatError AppendFormat(ref T, in U, in T0, in T1, in T2, in T3, in T4, in T5, in T6, in T7, in T8, in T9): text or XML content outside a top-level tag", "Unity.Collections.FixedStringMethods: ParseError Parse(ref T, ref int, ref float, char): text or XML content outside a top-level tag", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, int, int, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, int, int, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, int, int, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, int, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, int, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, float, int, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, float, int, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, float, int, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, int, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, int, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, string, int, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, string, int, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, string, int, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, int, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, int, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, int, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, int, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, int, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, int, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, int, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, int, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, int, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, int, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, int, float, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, int, float, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, int, float, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, float, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, float, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, float, float, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, float, float, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, float, float, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, float, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, float, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, string, float, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, string, float, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, string, float, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, float, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, float, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, float, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, float, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, float, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, float, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, float, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, float, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, float, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, float, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, int, string, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, int, string, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, int, string, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, string, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, string, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, float, string, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, float, string, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, float, string, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, string, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, string, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, string, string, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, string, string, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, string, string, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, string, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, string, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, string, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, string, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, string, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, string, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, string, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, string, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, string, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, string, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, int, T1, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, int, T1, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, int, T1, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, int, T1, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, int, T1, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, int, T1, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, T2, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, T2, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, float, T1, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, float, T1, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, float, T1, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, float, T1, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, float, T1, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, float, T1, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, T2, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, T2, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, string, T1, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, string, T1, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, string, T1, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, string, T1, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, string, T1, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, string, T1, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, T2, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, T2, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, T2, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, T2, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, T2, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, T2, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, T2, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, T2, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, T3, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, T3, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, int, int, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, int, int, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, int, int, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, int, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, int, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, float, int, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, float, int, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, float, int, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, int, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, int, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, string, int, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, string, int, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, string, int, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, int, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, int, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, int, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, int, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, int, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, int, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, int, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, int, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, int, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, int, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, int, float, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, int, float, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, int, float, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, float, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, float, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, float, float, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, float, float, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, float, float, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, float, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, float, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, string, float, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, string, float, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, string, float, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, float, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, float, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, float, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, float, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, float, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, float, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, float, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, float, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, float, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, float, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, int, string, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, int, string, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, int, string, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, string, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, string, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, float, string, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, float, string, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, float, string, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, string, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, string, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, string, string, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, string, string, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, string, string, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, string, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, string, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, string, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, string, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, string, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, string, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, string, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, string, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, string, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, string, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, int, T1, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, int, T1, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, int, T1, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, int, T1, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, int, T1, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, int, T1, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, T2, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, T2, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, float, T1, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, float, T1, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, float, T1, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, float, T1, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, float, T1, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, float, T1, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, T2, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, T2, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, string, T1, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, string, T1, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, string, T1, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, string, T1, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, string, T1, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, string, T1, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, T2, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, T2, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, T2, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, T2, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, T2, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, T2, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, T2, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, T2, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, T3, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, T3, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, int, int, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, int, int, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, int, int, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, int, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, int, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, float, int, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, float, int, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, float, int, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, int, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, int, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, string, int, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, string, int, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, string, int, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, int, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, int, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, int, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, int, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, int, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, int, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, int, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, int, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, int, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, int, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, int, float, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, int, float, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, int, float, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, float, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, float, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, float, float, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, float, float, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, float, float, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, float, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, float, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, string, float, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, string, float, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, string, float, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, float, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, float, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, float, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, float, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, float, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, float, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, float, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, float, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, float, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, float, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, int, string, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, int, string, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, int, string, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, string, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, string, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, float, string, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, float, string, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, float, string, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, string, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, string, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, string, string, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, string, string, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, string, string, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, string, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, string, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, string, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, string, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, string, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, string, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, string, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, string, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, string, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, string, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, int, T1, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, int, T1, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, int, T1, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, int, T1, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, int, T1, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, int, T1, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, T2, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, T2, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, float, T1, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, float, T1, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, float, T1, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, float, T1, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, float, T1, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, float, T1, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, T2, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, T2, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, string, T1, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, string, T1, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, string, T1, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, string, T1, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, string, T1, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, string, T1, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, T2, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, T2, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, T2, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, T2, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, T2, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, T2, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, T2, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, T2, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, T3, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, T3, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, int, int, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, int, int, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, int, int, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, int, int, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, int, int, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, int, int, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, int, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, int, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, float, int, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, float, int, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, float, int, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, float, int, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, float, int, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, float, int, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, int, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, int, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, string, int, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, string, int, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, string, int, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, string, int, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, string, int, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, string, int, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, int, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, int, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, int, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, int, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, int, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, int, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, int, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, int, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, int, T3): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, int, T3): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, int, float, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, int, float, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, int, float, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, int, float, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, int, float, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, int, float, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, float, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, float, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, float, float, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, float, float, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, float, float, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, float, float, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, float, float, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, float, float, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, float, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, float, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, string, float, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, string, float, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, string, float, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, string, float, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, string, float, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, string, float, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, float, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, float, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, float, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, float, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, float, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, float, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, float, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, float, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, float, T3): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, float, T3): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, int, string, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, int, string, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, int, string, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, int, string, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, int, string, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, int, string, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, string, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, string, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, float, string, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, float, string, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, float, string, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, float, string, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, float, string, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, float, string, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, string, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, string, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, string, string, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, string, string, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, string, string, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, string, string, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, string, string, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, string, string, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, string, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, string, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, string, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, string, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, string, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, string, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, string, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, string, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, string, T3): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, string, T3): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, int, T1, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, int, T1, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, int, T1, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, int, T1, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, int, T1, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, int, T1, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, T2, T3): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, int, T2, T3): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, float, T1, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, float, T1, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, float, T1, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, float, T1, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, float, T1, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, float, T1, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, T2, T3): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, float, T2, T3): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, string, T1, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, string, T1, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, string, T1, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, string, T1, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, string, T1, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, string, T1, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, T2, T3): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, string, T2, T3): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, T2, T3): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, int, T1, T2, T3): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, T2, T3): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, float, T1, T2, T3): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, T2, T3): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, string, T1, T2, T3): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, T3, T4): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString512Bytes Format(FixedString512Bytes, T1, T2, T3, T4): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, int, int, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, float, int, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, string, int, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, int, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, int, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, int, float, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, float, float, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, string, float, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, float, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, float, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, int, string, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, float, string, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, string, string, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, string, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, string, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, int, T1, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, int, T1, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, float, T1, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, float, T1, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, string, T1, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, string, T1, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, T2, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, T2, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, int, int, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, float, int, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, string, int, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, int, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, int, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, int, float, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, float, float, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, string, float, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, float, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, float, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, int, string, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, float, string, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, string, string, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, string, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, string, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, int, T1, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, int, T1, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, float, T1, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, float, T1, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, string, T1, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, string, T1, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, T2, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, T2, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, int, int, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, float, int, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, string, int, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, int, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, int, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, int, float, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, float, float, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, string, float, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, float, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, float, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, int, string, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, float, string, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, string, string, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, string, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, string, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, int, T1, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, int, T1, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, float, T1, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, float, T1, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, string, T1, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, string, T1, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, T2, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, T2, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, int, int, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, int, int, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, float, int, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, float, int, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, string, int, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, string, int, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, int, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, int, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, int, float, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, int, float, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, float, float, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, float, float, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, string, float, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, string, float, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, float, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, float, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, int, string, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, int, string, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, float, string, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, float, string, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, string, string, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, string, string, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, string, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, string, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, int, T1, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, int, T1, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, float, T1, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, float, T1, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, string, T1, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, string, T1, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, T2, T3): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, T2, T3): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, int, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, float, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, string, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, int): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, int, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, float, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, string, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, float): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, int, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, float, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, string, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, string): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, int, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, int, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, float, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, float, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, string, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, string, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, T2): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1, T2): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, int): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, float): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, string): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1): <seealso> in block context; use <see> instead", "Unity.Collections.FixedString: FixedString128Bytes Format(FixedString128Bytes, T1): non-standard tag <undoc>; use filter.yml rule instead", "Unity.Collections.BurstCompatibleAttribute: empty <summary> tag", "Unity.Jobs.JobParallelIndexListExtensions: JobHandle ScheduleAppend(T, NativeList<int>, int, int, JobHandle): empty <typeparam> tag", "Unity.Jobs.JobParallelIndexListExtensions: JobHandle ScheduleAppend(T, NativeList<int>, int, int, JobHandle): empty <param> tag", "Unity.Jobs.JobParallelIndexListExtensions: JobHandle ScheduleAppend(T, NativeList<int>, int, int, JobHandle): empty <returns> tag", "Unity.Jobs.JobParallelIndexListExtensions: JobHandle ScheduleFilter(T, NativeList<int>, int, JobHandle): empty <typeparam> tag", "Unity.Jobs.JobParallelIndexListExtensions: JobHandle ScheduleFilter(T, NativeList<int>, int, JobHandle): empty <param> tag", "Unity.Jobs.JobParallelIndexListExtensions: JobHandle ScheduleFilter(T, NativeList<int>, int, JobHandle): empty <returns> tag", "Unity.Jobs.IJobParallelForFilter: bool Execute(int): empty <summary> tag", "Unity.Jobs.IJobParallelForFilter: bool Execute(int): empty <param> tag", "Unity.Jobs.IJobParallelForFilter: bool Execute(int): empty <returns> tag", "Unity.Jobs.IJobParallelForBatchExtensions: void EarlyJobInit(): empty <typeparam> tag", "Unity.Jobs.IJobParallelForDeferExtensions: void EarlyJobInit(): empty <typeparam> tag", "Unity.Jobs.IJobParallelForDeferExtensions: JobHandle Schedule(T, int*, int, JobHandle): empty <returns> tag", "Unity.Jobs.IJobParallelForDeferExtensions: JobHandle Schedule(T, int*, int, JobHandle): multiple <returns> tags", "Unity.Jobs.IJobParallelForDeferExtensions: JobHandle ScheduleByRef(ref T, int*, int, JobHandle): empty <typeparam> tag", "Unity.Jobs.IJobParallelForDeferExtensions: JobHandle ScheduleByRef(ref T, int*, int, JobHandle): empty <returns> tag", "Unity.Jobs.IJobParallelForDeferExtensions: JobHandle ScheduleByRef(ref T, int*, int, JobHandle): multiple <returns> tags", "Unity.Collections.NativeArrayExtensions.NativeArrayStaticId<T>: empty <typeparam> tag", "Unity.Collections.NativeHashMap<TKey, TValue>: Count: unexpected <returns>; use <value> instead", "Unity.Collections.NativeHashMap<T<PERSON>ey, TValue>: Capacity: unexpected <param name=\"value\">", "Unity.Collections.NativeHashMap<TKey, TValue>.ReadOnly: Count: unexpected <returns>; use <value> instead", "Unity.Collections.NativeHashSet<T>: Count: unexpected <returns>; use <value> instead", "Unity.Collections.NativeHashSet<T>: Capacity: unexpected <param name=\"value\">", "Unity.Collections.NativeHashSet<T>.ReadOnly: Count: unexpected <returns>; use <value> instead", "Unity.Collections.INativeList<T>: Capacity: unexpected <param name=\"value\">", "Unity.Collections.NativeList<T>: Length: unexpected <param name=\"value>>\">", "Unity.Collections.NativeList<T>: Capacity: unexpected <param name=\"value\">", "Unity.Collections.NativeList<T>: NativeArray<T> AsDeferredJobArray(): mixed block and inline content in <example>; wrap inline content in <para>", "Unity.Collections.NativeParallelHashMap<TKey, TValue>: Capacity: unexpected <param name=\"value\">", "Unity.Collections.NativeParallelHashSet<T>: Capacity: unexpected <param name=\"value\">", "Unity.Collections.NativeParallelMultiHashMap<TKey, TValue>: Capacity: unexpected <param name=\"value\">", "Unity.Collections.NativeQueue<T>: Count: unexpected <returns>; use <value> instead", "Unity.Collections.NativeQueue<T>.ReadOnly: Count: unexpected <returns>; use <value> instead", "Unity.Collections.NativeReference<T>: Value: unexpected <param name=\"value\">", "Unity.Collections.NativeStream.Writer: void PatchMinMaxRange(int): empty <param> tag", "Unity.Jobs.RegisterGenericJobTypeAttribute: .ctor(Type): empty <param> tag", "Unity.Collections.StreamCompressionModel: int GetCompressedSizeInBits(uint): empty <returns> tag", "Unity.Collections.LowLevel.Unsafe.UnsafeHashMap<TKey, TValue>: Count: unexpected <returns>; use <value> instead", "Unity.Collections.LowLevel.Unsafe.UnsafeHashMap<TKey, TValue>: Capacity: unexpected <param name=\"value\">", "Unity.Collections.LowLevel.Unsafe.UnsafeHashMap<TKey, TValue>.ReadOnly: Count: unexpected <returns>; use <value> instead", "Unity.Collections.LowLevel.Unsafe.UnsafeHashSet<T>: Count: unexpected <returns>; use <value> instead", "Unity.Collections.LowLevel.Unsafe.UnsafeHashSet<T>: Capacity: unexpected <param name=\"value\">", "Unity.Collections.LowLevel.Unsafe.UnsafeHashSet<T>.ReadOnly: Count: unexpected <returns>; use <value> instead", "Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMap<TKey, TValue>: Capacity: unexpected <param name=\"value\">", "Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashSet<T>: Capacity: unexpected <param name=\"value\">", "Unity.Collections.LowLevel.Unsafe.UnsafeParallelMultiHashMap<TKey, TValue>: Capacity: unexpected <param name=\"value\">", "Unity.Collections.UnsafeQueue<T>: Count: unexpected <returns>; use <value> instead", "Unity.Collections.UnsafeQueue<T>.ReadOnly: Count: unexpected <returns>; use <value> instead", "Unity.Collections.LowLevel.Unsafe.UnsafeUtilityExtensions: T AsRef(in T): <remarks> in block context (only allowed in top-level context)", "Unity.Collections.xxHash3.StreamingState: void Reset(bool, ulong): empty <param> tag"]}, "PVP-151-1": {"errors": ["Unity.Collections.AllocatorManager.TryFunction: missing <param name=\"allocatorState\">", "Unity.Collections.AllocatorManager.TryFunction: missing <param name=\"block\">", "Unity.Collections.AllocatorManager.TryFunction: missing <returns>"]}}}