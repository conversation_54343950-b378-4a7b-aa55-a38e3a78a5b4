using UnityEngine;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// EVE Online-style UI dashboard for space navigation
/// </summary>
public class EVEStyleUI : MonoBehaviour
{
    [Header("UI Configuration")]
    [SerializeField] private bool showDashboard = true;
    [SerializeField] private KeyCode toggleDashboardKey = KeyCode.F1;
    [SerializeField] private bool updateEveryFrame = true;
    
    [Header("Dashboard Settings")]
    [SerializeField] private float dashboardWidth = 350f;
    [SerializeField] private float dashboardHeight = 500f;
    [SerializeField] private float dashboardMargin = 20f;
    
    [Header("Context Menu Settings")]
    [SerializeField] private float contextMenuWidth = 200f;
    [SerializeField] private float contextMenuItemHeight = 25f;
    
    // UI State
    private bool showContextMenu = false;
    private Vector2 contextMenuPosition;
    private float lastUIUpdate = 0f;
    private Vector2 dashboardScrollPosition = Vector2.zero;
    
    // Data
    private List<EntityInfo> entityList = new List<EntityInfo>();
    private SpaceshipController shipController;
    private Entity playerEntity;
    
    // UI Styles
    private GUIStyle dashboardStyle;
    private GUIStyle headerStyle;
    private GUIStyle itemStyle;
    private GUIStyle buttonStyle;
    private GUIStyle contextMenuStyle;
    private bool stylesInitialized = false;
    
    // Mouse interaction
    private Rect dashboardRect;
    private bool isDashboardDragging = false;
    private Vector2 dashboardDragOffset;
    private Vector2 dashboardPosition;
    
    [System.Serializable]
    public class EntityInfo
    {
        public Entity entity;
        public string name;
        public string type;
        public string size;
        public string distance;
        public float distanceValue;
        public Color typeColor;
        
        public EntityInfo(Entity ent)
        {
            entity = ent;
            name = ent.name;
            type = ent.Type.ToString();
            
            // Format size
            float radiusKm = ent.RadiusKm;
            if (radiusKm < 1f)
                size = $"{radiusKm * 1000f:F0}m";
            else
                size = $"{radiusKm:F1}km";
            
            // Calculate distance using coordinate manager
            if (SpaceCoordinateManager.Instance != null && SpaceCoordinateManager.Instance.PlayerEntity != null)
            {
                distanceValue = SpaceCoordinateManager.Instance.GetDistanceBetweenEntities(SpaceCoordinateManager.Instance.PlayerEntity, ent);
            }
            else
            {
                // Fallback to world position
                distanceValue = Vector3.Distance(Vector3.zero, ent.transform.position);
            }
            distance = SpaceCoordinates.FormatDistance(distanceValue);
            
            // Set type color
            typeColor = GetTypeColor(ent.Type);
        }
        
        private Color GetTypeColor(Entity.EntityType entityType)
        {
            switch (entityType)
            {
                case Entity.EntityType.Station: return new Color(0.2f, 0.8f, 0.2f); // Green
                case Entity.EntityType.Planet: return new Color(0.4f, 0.6f, 1f); // Blue
                case Entity.EntityType.Star: return new Color(1f, 1f, 0.4f); // Yellow
                case Entity.EntityType.Gate: return new Color(1f, 0.4f, 1f); // Magenta
                case Entity.EntityType.Beacon: return new Color(1f, 0.6f, 0.2f); // Orange
                case Entity.EntityType.Asteroid: return new Color(0.6f, 0.6f, 0.6f); // Gray
                default: return Color.white;
            }
        }
    }
    
    void Start()
    {
        shipController = GetComponent<SpaceshipController>();
        playerEntity = GetComponent<Entity>();
        
        // Position dashboard at top-right
        dashboardPosition = new Vector2(Screen.width - dashboardWidth - dashboardMargin, dashboardMargin);
        
        RefreshEntityList();
    }
    
    void Update()
    {
        HandleInput();

        if (updateEveryFrame)
        {
            RefreshEntityList();
        }
        else if (Time.time - lastUIUpdate > 0.2f)
        {
            RefreshEntityList();
            lastUIUpdate = Time.time;
        }
    }
    
    void HandleInput()
    {
        // Toggle dashboard
        if (Input.GetKeyDown(toggleDashboardKey))
        {
            showDashboard = !showDashboard;
        }
        
        // Right-click context menu
        if (Input.GetMouseButtonDown(1))
        {
            Vector2 mousePos = Input.mousePosition;
            mousePos.y = Screen.height - mousePos.y; // Convert to GUI coordinates
            
            // Check if clicking outside dashboard
            if (!dashboardRect.Contains(mousePos) || !showDashboard)
            {
                contextMenuPosition = mousePos;
                showContextMenu = true;
            }
        }
        
        // Close context menu on left click or escape
        if (Input.GetMouseButtonDown(0) || Input.GetKeyDown(KeyCode.Escape))
        {
            showContextMenu = false;
        }
    }
    
    void RefreshEntityList()
    {
        if (shipController == null) return;
        
        entityList.Clear();
        var destinations = shipController.GetAvailableDestinations();
        
        foreach (var dest in destinations)
        {
            if (dest.targetEntity != null)
            {
                entityList.Add(new EntityInfo(dest.targetEntity));
            }
        }
        
        // Sort by distance
        entityList = entityList.OrderBy(e => e.distanceValue).ToList();
    }
    
    void OnGUI()
    {
        InitializeStyles();
        
        if (showDashboard)
        {
            DrawDashboard();
        }
        
        if (showContextMenu)
        {
            DrawContextMenu();
        }
    }
    
    void InitializeStyles()
    {
        if (stylesInitialized) return;
        
        // Dashboard background style
        dashboardStyle = new GUIStyle(GUI.skin.box);
        dashboardStyle.normal.background = CreateColorTexture(new Color(0.1f, 0.1f, 0.15f, 0.95f));
        dashboardStyle.border = new RectOffset(2, 2, 2, 2);
        
        // Header style
        headerStyle = new GUIStyle(GUI.skin.label);
        headerStyle.fontSize = 14;
        headerStyle.fontStyle = FontStyle.Bold;
        headerStyle.normal.textColor = new Color(0.8f, 0.9f, 1f);
        headerStyle.alignment = TextAnchor.MiddleLeft;
        
        // Item style
        itemStyle = new GUIStyle(GUI.skin.label);
        itemStyle.fontSize = 11;
        itemStyle.normal.textColor = Color.white;
        itemStyle.alignment = TextAnchor.MiddleLeft;
        itemStyle.padding = new RectOffset(5, 5, 2, 2);
        
        // Button style
        buttonStyle = new GUIStyle(GUI.skin.button);
        buttonStyle.fontSize = 10;
        buttonStyle.normal.background = CreateColorTexture(new Color(0.2f, 0.3f, 0.4f, 0.8f));
        buttonStyle.hover.background = CreateColorTexture(new Color(0.3f, 0.4f, 0.5f, 0.9f));
        buttonStyle.normal.textColor = Color.white;
        
        // Context menu style
        contextMenuStyle = new GUIStyle(GUI.skin.box);
        contextMenuStyle.normal.background = CreateColorTexture(new Color(0.15f, 0.15f, 0.2f, 0.95f));
        contextMenuStyle.border = new RectOffset(1, 1, 1, 1);
        
        stylesInitialized = true;
    }
    
    Texture2D CreateColorTexture(Color color)
    {
        Texture2D texture = new Texture2D(1, 1);
        texture.SetPixel(0, 0, color);
        texture.Apply();
        return texture;
    }
    
    void DrawDashboard()
    {
        // Calculate dashboard rect
        dashboardRect = new Rect(dashboardPosition.x, dashboardPosition.y, dashboardWidth, dashboardHeight);
        
        // Handle dragging
        HandleDashboardDragging();
        
        // Draw dashboard background
        GUI.Box(dashboardRect, "", dashboardStyle);
        
        // Header
        Rect headerRect = new Rect(dashboardRect.x + 10, dashboardRect.y + 10, dashboardRect.width - 20, 25);
        GUI.Label(headerRect, "NAVIGATION OVERVIEW", headerStyle);
        
        // Close button
        Rect closeButtonRect = new Rect(dashboardRect.x + dashboardRect.width - 30, dashboardRect.y + 5, 20, 20);
        if (GUI.Button(closeButtonRect, "×", buttonStyle))
        {
            showDashboard = false;
        }
        
        // Entity count
        Rect countRect = new Rect(dashboardRect.x + 10, dashboardRect.y + 35, dashboardRect.width - 20, 20);
        GUI.Label(countRect, $"Entities: {entityList.Count}", itemStyle);
        
        // Scroll view for entities
        Rect scrollViewRect = new Rect(dashboardRect.x + 5, dashboardRect.y + 60, dashboardRect.width - 10, dashboardRect.height - 70);
        Rect scrollContentRect = new Rect(0, 0, dashboardRect.width - 25, entityList.Count * 60f);
        
        dashboardScrollPosition = GUI.BeginScrollView(scrollViewRect, dashboardScrollPosition, scrollContentRect);
        
        // Draw entity list
        for (int i = 0; i < entityList.Count; i++)
        {
            DrawEntityItem(entityList[i], i * 60f);
        }
        
        GUI.EndScrollView();
    }
    
    void DrawEntityItem(EntityInfo entityInfo, float yOffset)
    {
        float itemWidth = dashboardWidth - 30;
        float itemHeight = 55f;
        
        Rect itemRect = new Rect(5, yOffset + 5, itemWidth, itemHeight);
        
        // Item background (alternating colors)
        Color bgColor = (entityList.IndexOf(entityInfo) % 2 == 0) ? 
            new Color(0.15f, 0.15f, 0.2f, 0.5f) : 
            new Color(0.1f, 0.1f, 0.15f, 0.5f);
        
        GUI.DrawTexture(itemRect, CreateColorTexture(bgColor));
        
        // Entity name (clickable for info)
        Rect nameRect = new Rect(itemRect.x + 5, itemRect.y + 2, itemWidth - 80, 18);
        itemStyle.normal.textColor = Color.white;
        itemStyle.fontStyle = FontStyle.Bold;

        if (GUI.Button(nameRect, entityInfo.name, itemStyle))
        {
            // Show entity info window
            EntityInfoWindow infoWindow = GetComponent<EntityInfoWindow>();
            if (infoWindow == null)
            {
                infoWindow = gameObject.AddComponent<EntityInfoWindow>();
            }
            infoWindow.ShowEntityInfo(entityInfo.entity);
        }
        
        // Entity type
        Rect typeRect = new Rect(itemRect.x + 5, itemRect.y + 18, itemWidth - 80, 15);
        itemStyle.normal.textColor = entityInfo.typeColor;
        itemStyle.fontStyle = FontStyle.Normal;
        GUI.Label(typeRect, entityInfo.type, itemStyle);
        
        // Size and distance
        Rect sizeRect = new Rect(itemRect.x + 5, itemRect.y + 33, (itemWidth - 80) / 2, 15);
        itemStyle.normal.textColor = Color.gray;
        GUI.Label(sizeRect, $"Size: {entityInfo.size}", itemStyle);
        
        Rect distanceRect = new Rect(itemRect.x + 5 + (itemWidth - 80) / 2, itemRect.y + 33, (itemWidth - 80) / 2, 15);
        GUI.Label(distanceRect, entityInfo.distance, itemStyle);
        
        // Warp button with tooltip
        Rect warpButtonRect = new Rect(itemRect.x + itemWidth - 70, itemRect.y + 15, 60, 25);
        if (GUI.Button(warpButtonRect, "WARP", buttonStyle))
        {
            if (shipController != null && entityInfo.entity != null)
            {
                shipController.WarpToEntity(entityInfo.entity);
            }
        }

        // Show warp info on hover
        if (warpButtonRect.Contains(Event.current.mousePosition))
        {
            GUI.tooltip = $"Warp to 10km from {entityInfo.name}";
        }
    }
    
    void HandleDashboardDragging()
    {
        Event currentEvent = Event.current;
        Vector2 mousePos = currentEvent.mousePosition;
        
        if (currentEvent.type == EventType.MouseDown && currentEvent.button == 0)
        {
            if (dashboardRect.Contains(mousePos))
            {
                isDashboardDragging = true;
                dashboardDragOffset = mousePos - dashboardPosition;
            }
        }
        else if (currentEvent.type == EventType.MouseDrag && isDashboardDragging)
        {
            dashboardPosition = mousePos - dashboardDragOffset;
            
            // Clamp to screen bounds
            dashboardPosition.x = Mathf.Clamp(dashboardPosition.x, 0, Screen.width - dashboardWidth);
            dashboardPosition.y = Mathf.Clamp(dashboardPosition.y, 0, Screen.height - dashboardHeight);
        }
        else if (currentEvent.type == EventType.MouseUp)
        {
            isDashboardDragging = false;
        }
    }
    
    void DrawContextMenu()
    {
        string[] menuItems = { "Toggle Dashboard", "Warp to Nearest Station", "Toggle System Map", "Reset Camera", "Refresh Entities" };
        float menuHeight = menuItems.Length * contextMenuItemHeight;
        
        Rect menuRect = new Rect(contextMenuPosition.x, contextMenuPosition.y, contextMenuWidth, menuHeight);
        
        // Clamp to screen
        if (menuRect.x + menuRect.width > Screen.width)
            menuRect.x = Screen.width - menuRect.width;
        if (menuRect.y + menuRect.height > Screen.height)
            menuRect.y = Screen.height - menuRect.height;
        
        GUI.Box(menuRect, "", contextMenuStyle);
        
        for (int i = 0; i < menuItems.Length; i++)
        {
            Rect itemRect = new Rect(menuRect.x, menuRect.y + i * contextMenuItemHeight, contextMenuWidth, contextMenuItemHeight);
            
            if (GUI.Button(itemRect, menuItems[i], buttonStyle))
            {
                ExecuteContextMenuAction(i);
                showContextMenu = false;
            }
        }
    }
    
    void ExecuteContextMenuAction(int actionIndex)
    {
        switch (actionIndex)
        {
            case 0: // Toggle Dashboard
                showDashboard = !showDashboard;
                break;
            case 1: // Warp to Nearest Station
                if (shipController != null)
                {
                    var nearestStation = entityList
                        .Where(e => e.entity.Type == Entity.EntityType.Station)
                        .OrderBy(e => e.distanceValue)
                        .FirstOrDefault();

                    if (nearestStation != null)
                    {
                        shipController.WarpToEntity(nearestStation.entity);
                    }
                }
                break;
            case 2: // Toggle System Map
                SystemMap systemMap = GetComponent<SystemMap>();
                if (systemMap != null)
                {
                    systemMap.ToggleMap();
                }
                break;
            case 3: // Reset Camera
                Camera.main?.GetComponent<OrbitCamera>()?.ResetToDefault();
                break;
            case 4: // Refresh Entities
                RefreshEntityList();
                break;
        }
    }
    
    // Public methods
    public void ToggleDashboard()
    {
        showDashboard = !showDashboard;
    }
    
    public void ShowDashboard()
    {
        showDashboard = true;
    }
    
    public void HideDashboard()
    {
        showDashboard = false;
    }
}
