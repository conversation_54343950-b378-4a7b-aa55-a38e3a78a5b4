using UnityEngine;

/// <summary>
/// Demo script to set up a basic space scene with the EVE Online-inspired grid system
/// </summary>
public class SpaceGameDemo : MonoBehaviour
{
    [Header("Demo Configuration")]
    [SerializeField] private GameObject playerShipPrefab;
    [SerializeField] private GameObject asteroidPrefab;
    [SerializeField] private GameObject stationPrefab;
    [SerializeField] private int numberOfAsteroids = 50;
    [SerializeField] private float asteroidFieldRadius = 100000f; // 100km radius
    
    [Header("Camera Setup")]
    [SerializeField] private Camera mainCamera;
    [SerializeField] private bool setupCameraAutomatically = true;
    
    // Demo objects
    private GameObject playerShip;
    private GameObject[] asteroids;
    private GameObject spaceStation;
    
    void Start()
    {
        SetupDemo();
    }
    
    /// <summary>
    /// Sets up the demo scene
    /// </summary>
    void SetupDemo()
    {
        Debug.Log("Setting up Space Game Demo...");
        
        // Ensure we have a SpaceGridSystem
        if (SpaceGridSystem.Instance == null)
        {
            GameObject gridSystemGO = new GameObject("SpaceGridSystem");
            gridSystemGO.AddComponent<SpaceGridSystem>();
        }
        
        // Setup camera
        if (setupCameraAutomatically)
        {
            SetupCamera();
        }
        
        // Create player ship
        CreatePlayerShip();
        
        // Create space station
        CreateSpaceStation();
        
        // Create asteroid field
        CreateAsteroidField();
        
        Debug.Log("Space Game Demo setup complete!");
    }
    
    /// <summary>
    /// Sets up the camera with orbit controls and space scaling
    /// </summary>
    void SetupCamera()
    {
        if (mainCamera == null)
        {
            mainCamera = Camera.main;
            if (mainCamera == null)
            {
                Debug.LogError("No main camera found!");
                return;
            }
        }
        
        // Add orbit camera component
        OrbitCamera orbitCamera = mainCamera.GetComponent<OrbitCamera>();
        if (orbitCamera == null)
        {
            orbitCamera = mainCamera.gameObject.AddComponent<OrbitCamera>();
        }
        
        // Add space camera controller
        SpaceCameraController spaceCameraController = mainCamera.GetComponent<SpaceCameraController>();
        if (spaceCameraController == null)
        {
            spaceCameraController = mainCamera.gameObject.AddComponent<SpaceCameraController>();
        }
        
        // Position camera for good initial view
        mainCamera.transform.position = new Vector3(0, 500, -1000);
        mainCamera.transform.LookAt(Vector3.zero);
        
        Debug.Log("Camera setup complete with orbit controls and space scaling");
    }
    
    /// <summary>
    /// Creates the player ship
    /// </summary>
    void CreatePlayerShip()
    {
        Vector3 playerPosition = Vector3.zero;
        
        if (playerShipPrefab != null)
        {
            playerShip = Instantiate(playerShipPrefab, playerPosition, Quaternion.identity);
        }
        else
        {
            // Create a simple cube as player ship
            playerShip = GameObject.CreatePrimitive(PrimitiveType.Cube);
            playerShip.transform.localScale = Vector3.one * 50f; // 50m ship
        }
        
        playerShip.name = "PlayerShip";
        
        // Add Entity component
        Entity playerEntity = playerShip.GetComponent<Entity>();
        if (playerEntity == null)
        {
            playerEntity = playerShip.AddComponent<Entity>();
        }
        
        // Configure as player
        var entityType = typeof(Entity);
        var isPlayerField = entityType.GetField("isPlayer", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (isPlayerField != null)
        {
            isPlayerField.SetValue(playerEntity, true);
        }
        
        // Set up SpaceGridSystem reference
        if (SpaceGridSystem.Instance != null)
        {
            var playerTransformField = typeof(SpaceGridSystem).GetField("playerTransform", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (playerTransformField != null)
            {
                playerTransformField.SetValue(SpaceGridSystem.Instance, playerShip.transform);
            }
        }
        
        // Set up orbit camera target
        if (mainCamera != null)
        {
            OrbitCamera orbitCamera = mainCamera.GetComponent<OrbitCamera>();
            if (orbitCamera != null)
            {
                orbitCamera.SetTarget(playerShip.transform);
            }
        }
        
        Debug.Log("Player ship created at origin");
    }
    
    /// <summary>
    /// Creates a space station
    /// </summary>
    void CreateSpaceStation()
    {
        Vector3 stationPosition = new Vector3(50000f, 0, 0); // 50km from player
        
        if (stationPrefab != null)
        {
            spaceStation = Instantiate(stationPrefab, stationPosition, Quaternion.identity);
        }
        else
        {
            // Create a simple cylinder as space station
            spaceStation = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
            spaceStation.transform.localScale = new Vector3(500f, 100f, 500f); // Large station
        }
        
        spaceStation.name = "SpaceStation";
        
        // Add Entity component
        Entity stationEntity = spaceStation.GetComponent<Entity>();
        if (stationEntity == null)
        {
            stationEntity = spaceStation.AddComponent<Entity>();
        }
        
        // Configure station properties using reflection
        SetEntityProperties(stationEntity, 2.0f, 1000000f, Entity.EntityType.Station);
        
        Debug.Log($"Space station created at {stationPosition}");
    }
    
    /// <summary>
    /// Creates an asteroid field
    /// </summary>
    void CreateAsteroidField()
    {
        asteroids = new GameObject[numberOfAsteroids];
        
        for (int i = 0; i < numberOfAsteroids; i++)
        {
            // Random position within asteroid field
            Vector3 randomPosition = Random.insideUnitSphere * asteroidFieldRadius;
            randomPosition.y *= 0.1f; // Flatten the field
            
            GameObject asteroid;
            if (asteroidPrefab != null)
            {
                asteroid = Instantiate(asteroidPrefab, randomPosition, Random.rotation);
            }
            else
            {
                // Create a simple sphere as asteroid
                asteroid = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                asteroid.transform.position = randomPosition;
                asteroid.transform.rotation = Random.rotation;
                
                // Random size
                float size = Random.Range(10f, 100f);
                asteroid.transform.localScale = Vector3.one * size;
            }
            
            asteroid.name = $"Asteroid_{i:D3}";
            
            // Add Entity component
            Entity asteroidEntity = asteroid.GetComponent<Entity>();
            if (asteroidEntity == null)
            {
                asteroidEntity = asteroid.AddComponent<Entity>();
            }
            
            // Configure asteroid properties
            float radius = Random.Range(0.01f, 0.1f); // 10m to 100m radius
            float mass = Random.Range(1000f, 100000f);
            SetEntityProperties(asteroidEntity, radius, mass, Entity.EntityType.Asteroid);
            
            asteroids[i] = asteroid;
        }
        
        Debug.Log($"Created {numberOfAsteroids} asteroids in field");
    }
    
    /// <summary>
    /// Sets entity properties using reflection (since fields are private)
    /// </summary>
    void SetEntityProperties(Entity entity, float radiusKm, float massKg, Entity.EntityType entityType)
    {
        var entityTypeField = typeof(Entity);
        
        // Set radius
        var radiusField = entityTypeField.GetField("radiusKm", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (radiusField != null)
        {
            radiusField.SetValue(entity, radiusKm);
        }
        
        // Set mass
        var massField = entityTypeField.GetField("massKg", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (massField != null)
        {
            massField.SetValue(entity, massKg);
        }
        
        // Set entity type
        var typeField = entityTypeField.GetField("entityType", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (typeField != null)
        {
            typeField.SetValue(entity, entityType);
        }
    }
    
    void Update()
    {
        // Demo controls
        HandleDemoControls();
    }
    
    /// <summary>
    /// Handles demo-specific controls
    /// </summary>
    void HandleDemoControls()
    {
        // Press F to focus on space station
        if (Input.GetKeyDown(KeyCode.F))
        {
            FocusOnSpaceStation();
        }
        
        // Press R to return to player
        if (Input.GetKeyDown(KeyCode.R))
        {
            FocusOnPlayer();
        }
        
        // Press G to show grid info
        if (Input.GetKeyDown(KeyCode.G))
        {
            ShowGridInfo();
        }
    }
    
    /// <summary>
    /// Focuses camera on the space station
    /// </summary>
    void FocusOnSpaceStation()
    {
        if (spaceStation != null && mainCamera != null)
        {
            SpaceCameraController cameraController = mainCamera.GetComponent<SpaceCameraController>();
            Entity stationEntity = spaceStation.GetComponent<Entity>();
            
            if (cameraController != null && stationEntity != null)
            {
                cameraController.FocusOnEntity(stationEntity);
                Debug.Log("Focused on space station");
            }
        }
    }
    
    /// <summary>
    /// Focuses camera on the player
    /// </summary>
    void FocusOnPlayer()
    {
        if (playerShip != null && mainCamera != null)
        {
            OrbitCamera orbitCamera = mainCamera.GetComponent<OrbitCamera>();
            if (orbitCamera != null)
            {
                orbitCamera.SetTarget(playerShip.transform);
                orbitCamera.ResetToDefault();
                Debug.Log("Focused on player ship");
            }
        }
    }
    
    /// <summary>
    /// Shows grid system information
    /// </summary>
    void ShowGridInfo()
    {
        if (SpaceGridSystem.Instance != null)
        {
            var grids = SpaceGridSystem.Instance.ActiveGrids;
            Debug.Log($"Active Grids: {grids.Count}");
            
            foreach (var grid in grids.Values)
            {
                Debug.Log(grid.ToString());
            }
        }
    }
    
    void OnGUI()
    {
        // Demo instructions
        GUI.Label(new Rect(10, 10, 300, 20), "Space Game Demo Controls:");
        GUI.Label(new Rect(10, 30, 300, 20), "F - Focus on Space Station");
        GUI.Label(new Rect(10, 50, 300, 20), "R - Return to Player");
        GUI.Label(new Rect(10, 70, 300, 20), "G - Show Grid Info");
        GUI.Label(new Rect(10, 90, 300, 20), "Mouse Wheel - Zoom");
        GUI.Label(new Rect(10, 110, 300, 20), "Left Click + Drag - Orbit");
    }
}
