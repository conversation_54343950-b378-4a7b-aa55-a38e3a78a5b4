using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

/// <summary>
/// EVE-style inventory UI window with code-generated interface
/// </summary>
public class InventoryUI : MonoBehaviour
{
    [Header("UI Settings")]
    [SerializeField] private KeyCode inventoryKey = KeyCode.I;
    [SerializeField] private KeyCode equipmentKey = KeyCode.E;

    // UI state
    private Canvas inventoryCanvas;
    private GameObject inventoryWindow;
    private GameObject equipmentWindow;
    private Transform cargoContainer;
    private Transform equipmentSlotsContainer;
    private Text cargoSpaceText;
    private bool inventoryOpen = false;
    private bool equipmentOpen = false;
    

    // Internal state
    private InventorySystem playerInventory;
    private List<GameObject> cargoSlots = new List<GameObject>();
    private List<GameObject> equipmentSlots = new List<GameObject>();
    
    void Start()
    {
        InitializeInventoryUI();
        FindPlayerInventory();
    }

    void Update()
    {
        HandleInput();
        if (inventoryOpen && cargoSpaceText != null)
        {
            UpdateCargoSpaceDisplay();
        }
    }

    void InitializeInventoryUI()
    {
        // Create canvas for inventory windows
        GameObject canvasGO = new GameObject("InventoryCanvas");
        inventoryCanvas = canvasGO.AddComponent<Canvas>();
        inventoryCanvas.renderMode = RenderMode.ScreenSpaceOverlay;
        inventoryCanvas.sortingOrder = 50; // Below targeting UI

        CanvasScaler scaler = canvasGO.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        scaler.referenceResolution = new Vector2(1920, 1080);

        canvasGO.AddComponent<GraphicRaycaster>();

        // Create inventory window
        CreateInventoryWindow();

        // Create equipment window
        CreateEquipmentWindow();
    }
    
    void CreateInventoryWindow()
    {
        // Create inventory window
        inventoryWindow = new GameObject("InventoryWindow");
        inventoryWindow.transform.SetParent(inventoryCanvas.transform, false);

        RectTransform windowRect = inventoryWindow.AddComponent<RectTransform>();
        windowRect.sizeDelta = new Vector2(400f, 500f);
        windowRect.anchoredPosition = new Vector2(-300f, 0f);

        // Add background
        Image background = inventoryWindow.AddComponent<Image>();
        background.color = new Color(0.1f, 0.1f, 0.15f, 0.9f);

        // Add border
        Outline outline = inventoryWindow.AddComponent<Outline>();
        outline.effectColor = new Color(0.3f, 0.6f, 1f, 1f);
        outline.effectDistance = new Vector2(1, 1);

        // Make draggable
        inventoryWindow.AddComponent<DraggableWindow>();

        // Create title
        CreateWindowTitle(inventoryWindow, "Ship Cargo");

        // Create cargo space display
        CreateCargoSpaceDisplay(inventoryWindow);

        // Create cargo container
        CreateCargoContainer(inventoryWindow);

        inventoryWindow.SetActive(false);
    }

    void CreateEquipmentWindow()
    {
        // Create equipment window
        equipmentWindow = new GameObject("EquipmentWindow");
        equipmentWindow.transform.SetParent(inventoryCanvas.transform, false);

        RectTransform windowRect = equipmentWindow.AddComponent<RectTransform>();
        windowRect.sizeDelta = new Vector2(300f, 400f);
        windowRect.anchoredPosition = new Vector2(300f, 0f);

        // Add background
        Image background = equipmentWindow.AddComponent<Image>();
        background.color = new Color(0.1f, 0.1f, 0.15f, 0.9f);

        // Add border
        Outline outline = equipmentWindow.AddComponent<Outline>();
        outline.effectColor = new Color(0.3f, 0.6f, 1f, 1f);
        outline.effectDistance = new Vector2(1, 1);

        // Make draggable
        equipmentWindow.AddComponent<DraggableWindow>();

        // Create title
        CreateWindowTitle(equipmentWindow, "Ship Equipment");

        // Create equipment slots container
        CreateEquipmentContainer(equipmentWindow);

        equipmentWindow.SetActive(false);
    }

    void FindPlayerInventory()
    {
        // Find player ship inventory
        var playerEntity = SpaceCoordinateManager.Instance?.PlayerEntity;
        if (playerEntity != null)
        {
            playerInventory = playerEntity.GetComponent<InventorySystem>();
            if (playerInventory != null)
            {
                // Subscribe to inventory events
                playerInventory.OnInventoryChanged += RefreshInventoryDisplay;

                RefreshInventoryDisplay();
            }
        }
    }
    
    void CreateWindowTitle(GameObject window, string title)
    {
        GameObject titleBar = new GameObject("TitleBar");
        titleBar.transform.SetParent(window.transform, false);

        RectTransform titleRect = titleBar.AddComponent<RectTransform>();
        titleRect.anchorMin = new Vector2(0, 1);
        titleRect.anchorMax = new Vector2(1, 1);
        titleRect.sizeDelta = new Vector2(0, 30f);
        titleRect.anchoredPosition = new Vector2(0, -15f);

        // Title background
        Image titleBg = titleBar.AddComponent<Image>();
        titleBg.color = new Color(0.2f, 0.4f, 0.8f, 0.8f);

        // Title text
        GameObject titleText = new GameObject("TitleText");
        titleText.transform.SetParent(titleBar.transform, false);

        RectTransform textRect = titleText.AddComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.sizeDelta = Vector2.zero;
        textRect.anchoredPosition = Vector2.zero;
        textRect.offsetMin = new Vector2(10, 0);
        textRect.offsetMax = new Vector2(-10, 0);

        Text text = titleText.AddComponent<Text>();
        text.text = title;
        text.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        text.fontSize = 14;
        text.color = Color.white;
        text.alignment = TextAnchor.MiddleLeft;
        text.fontStyle = FontStyle.Bold;
    }

    void CreateCargoSpaceDisplay(GameObject window)
    {
        GameObject spaceDisplay = new GameObject("CargoSpaceDisplay");
        spaceDisplay.transform.SetParent(window.transform, false);

        RectTransform spaceRect = spaceDisplay.AddComponent<RectTransform>();
        spaceRect.anchorMin = new Vector2(0, 1);
        spaceRect.anchorMax = new Vector2(1, 1);
        spaceRect.sizeDelta = new Vector2(0, 25f);
        spaceRect.anchoredPosition = new Vector2(0, -45f);

        cargoSpaceText = spaceDisplay.AddComponent<Text>();
        cargoSpaceText.text = "Cargo: 0/500 m³";
        cargoSpaceText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        cargoSpaceText.fontSize = 12;
        cargoSpaceText.color = new Color(0.8f, 0.8f, 0.8f, 1f);
        cargoSpaceText.alignment = TextAnchor.MiddleCenter;
    }

    void CreateCargoContainer(GameObject window)
    {
        GameObject container = new GameObject("CargoContainer");
        container.transform.SetParent(window.transform, false);

        RectTransform containerRect = container.AddComponent<RectTransform>();
        containerRect.anchorMin = new Vector2(0, 0);
        containerRect.anchorMax = new Vector2(1, 1);
        containerRect.sizeDelta = Vector2.zero;
        containerRect.anchoredPosition = Vector2.zero;
        containerRect.offsetMin = new Vector2(10, 10);
        containerRect.offsetMax = new Vector2(-10, -70);

        // Add scroll view
        ScrollRect scrollRect = container.AddComponent<ScrollRect>();
        scrollRect.horizontal = false;
        scrollRect.vertical = true;

        // Create content area
        GameObject content = new GameObject("Content");
        content.transform.SetParent(container.transform, false);

        RectTransform contentRect = content.AddComponent<RectTransform>();
        contentRect.anchorMin = new Vector2(0, 1);
        contentRect.anchorMax = new Vector2(1, 1);
        contentRect.sizeDelta = new Vector2(0, 0);
        contentRect.anchoredPosition = Vector2.zero;

        VerticalLayoutGroup layout = content.AddComponent<VerticalLayoutGroup>();
        layout.spacing = 2f;
        layout.padding = new RectOffset(5, 5, 5, 5);

        ContentSizeFitter fitter = content.AddComponent<ContentSizeFitter>();
        fitter.verticalFit = ContentSizeFitter.FitMode.PreferredSize;

        scrollRect.content = contentRect;
        cargoContainer = content.transform;
    }

    void CreateEquipmentContainer(GameObject window)
    {
        GameObject container = new GameObject("EquipmentContainer");
        container.transform.SetParent(window.transform, false);

        RectTransform containerRect = container.AddComponent<RectTransform>();
        containerRect.anchorMin = new Vector2(0, 0);
        containerRect.anchorMax = new Vector2(1, 1);
        containerRect.sizeDelta = Vector2.zero;
        containerRect.anchoredPosition = Vector2.zero;
        containerRect.offsetMin = new Vector2(10, 10);
        containerRect.offsetMax = new Vector2(-10, -40);

        VerticalLayoutGroup layout = container.AddComponent<VerticalLayoutGroup>();
        layout.spacing = 5f;
        layout.padding = new RectOffset(5, 5, 5, 5);

        equipmentSlotsContainer = container.transform;

        // Create slot groups
        CreateSlotGroup("High Slots", 8);
        CreateSlotGroup("Mid Slots", 4);
        CreateSlotGroup("Low Slots", 4);
    }

    void HandleInput()
    {
        // Toggle inventory window
        if (Input.GetKeyDown(inventoryKey))
        {
            ToggleInventoryWindow();
        }

        // Toggle equipment window
        if (Input.GetKeyDown(equipmentKey))
        {
            ToggleEquipmentWindow();
        }

        // Close windows with Escape
        if (Input.GetKeyDown(KeyCode.Escape))
        {
            CloseAllWindows();
        }
    }
    
    void CreateSlotGroup(string groupName, int slotCount)
    {
        // Create group header
        GameObject header = new GameObject($"{groupName}Header");
        header.transform.SetParent(equipmentSlotsContainer, false);

        RectTransform headerRect = header.AddComponent<RectTransform>();
        headerRect.sizeDelta = new Vector2(0, 25f);

        Text headerText = header.AddComponent<Text>();
        headerText.text = groupName;
        headerText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        headerText.fontSize = 12;
        headerText.color = new Color(0.8f, 0.8f, 0.8f, 1f);
        headerText.alignment = TextAnchor.MiddleLeft;
        headerText.fontStyle = FontStyle.Bold;

        // Create slots
        for (int i = 0; i < slotCount; i++)
        {
            CreateEquipmentSlot($"{groupName} {i + 1}");
        }
    }

    void CreateEquipmentSlot(string slotName)
    {
        GameObject slot = new GameObject($"Slot_{slotName}");
        slot.transform.SetParent(equipmentSlotsContainer, false);

        RectTransform slotRect = slot.AddComponent<RectTransform>();
        slotRect.sizeDelta = new Vector2(0, 30f);

        // Slot background
        Image slotBg = slot.AddComponent<Image>();
        slotBg.color = new Color(0.2f, 0.2f, 0.25f, 0.8f);

        // Slot text
        Text slotText = slot.AddComponent<Text>();
        slotText.text = $"{slotName}: [Empty]";
        slotText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        slotText.fontSize = 10;
        slotText.color = new Color(0.7f, 0.7f, 0.7f, 1f);
        slotText.alignment = TextAnchor.MiddleLeft;

        // Add padding
        slotRect.offsetMin = new Vector2(5, 0);
        slotRect.offsetMax = new Vector2(-5, 0);

        equipmentSlots.Add(slot);
    }

    void ToggleInventoryWindow()
    {
        inventoryOpen = !inventoryOpen;
        if (inventoryWindow != null)
        {
            inventoryWindow.SetActive(inventoryOpen);
        }

        if (inventoryOpen)
        {
            RefreshInventoryDisplay();
        }
    }

    void ToggleEquipmentWindow()
    {
        equipmentOpen = !equipmentOpen;
        if (equipmentWindow != null)
        {
            equipmentWindow.SetActive(equipmentOpen);
        }
    }

    void CloseAllWindows()
    {
        inventoryOpen = false;
        equipmentOpen = false;

        if (inventoryWindow != null) inventoryWindow.SetActive(false);
        if (equipmentWindow != null) equipmentWindow.SetActive(false);
    }
    
    void RefreshInventoryDisplay()
    {
        if (playerInventory == null || cargoContainer == null) return;

        // Clear existing slots
        foreach (var slot in cargoSlots)
        {
            if (slot != null)
            {
                Destroy(slot);
            }
        }
        cargoSlots.Clear();

        // Create slots for each item stack
        var cargoItems = playerInventory.CargoItems;
        for (int i = 0; i < cargoItems.Count; i++)
        {
            CreateCargoSlot(cargoItems[i]);
        }
    }
    
    void CreateCargoSlot(InventoryStack stack)
    {
        if (cargoContainer == null) return;

        GameObject slot = new GameObject($"CargoSlot_{stack.item.itemName}");
        slot.transform.SetParent(cargoContainer, false);

        RectTransform slotRect = slot.AddComponent<RectTransform>();
        slotRect.sizeDelta = new Vector2(0, 25f);

        // Slot background
        Image slotBg = slot.AddComponent<Image>();
        slotBg.color = new Color(0.15f, 0.15f, 0.2f, 0.8f);

        // Item text
        Text itemText = slot.AddComponent<Text>();
        string displayText = stack.item.isStackable && stack.quantity > 1
            ? $"{stack.item.itemName} x{stack.quantity} ({stack.TotalVolume:F1}m³)"
            : $"{stack.item.itemName} ({stack.item.volumeM3:F1}m³)";
        itemText.text = displayText;
        itemText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        itemText.fontSize = 10;
        itemText.color = Color.white;
        itemText.alignment = TextAnchor.MiddleLeft;

        // Add padding
        slotRect.offsetMin = new Vector2(5, 0);
        slotRect.offsetMax = new Vector2(-5, 0);

        cargoSlots.Add(slot);
    }
    
    void UpdateCargoSpaceDisplay()
    {
        if (cargoSpaceText != null && playerInventory != null)
        {
            float used = playerInventory.UsedCargoSpace;
            float total = playerInventory.CargoCapacity;
            cargoSpaceText.text = $"Cargo: {used:F1}/{total:F1} m³";

            // Color code based on usage
            float usage = used / total;
            if (usage > 0.9f)
                cargoSpaceText.color = Color.red;
            else if (usage > 0.7f)
                cargoSpaceText.color = Color.yellow;
            else
                cargoSpaceText.color = new Color(0.8f, 0.8f, 0.8f, 1f);
        }
    }
}
    
    void UpdateCargoSpaceDisplay()
    {
        if (cargoSpaceText != null && playerInventory != null)
        {
            float used = playerInventory.UsedCargoSpace;
            float total = playerInventory.CargoCapacity;
            cargoSpaceText.text = $"Cargo: {used:F1}/{total:F1} m³";

            // Color code based on usage
            float usage = used / total;
            if (usage > 0.9f)
                cargoSpaceText.color = Color.red;
            else if (usage > 0.7f)
                cargoSpaceText.color = Color.yellow;
            else
                cargoSpaceText.color = new Color(0.8f, 0.8f, 0.8f, 1f);
        }
    }
}
