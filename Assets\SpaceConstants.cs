using UnityEngine;

/// <summary>
/// Constants and configuration values for the EVE Online-inspired space system
/// </summary>
public static class SpaceConstants
{
    // === DISTANCE CONSTANTS ===
    
    /// <summary>
    /// Meters per kilometer
    /// </summary>
    public const float METERS_PER_KM = 1000f;
    
    /// <summary>
    /// Kilometers per Astronomical Unit
    /// </summary>
    public const float KM_PER_AU = 149597870.7f;
    
    /// <summary>
    /// Unity units per Astronomical Unit (for scaling)
    /// </summary>
    public const float UNITY_UNITS_PER_AU = 1000000f;
    
    /// <summary>
    /// Unity units per kilometer
    /// </summary>
    public const float UNITY_UNITS_PER_KM = UNITY_UNITS_PER_AU / KM_PER_AU;
    
    // === GRID SYSTEM CONSTANTS ===
    
    /// <summary>
    /// Default grid radius in kilometers (300km diameter)
    /// </summary>
    public const float DEFAULT_GRID_RADIUS_KM = 150f;
    
    /// <summary>
    /// Maximum grid radius in kilometers (1000km diameter)
    /// </summary>
    public const float MAX_GRID_RADIUS_KM = 500f;
    
    /// <summary>
    /// Minimum grid radius in kilometers
    /// </summary>
    public const float MIN_GRID_RADIUS_KM = 50f;
    
    /// <summary>
    /// Grid merge threshold - grids merge when they overlap by this percentage
    /// </summary>
    public const float GRID_MERGE_THRESHOLD = 0.1f;
    
    /// <summary>
    /// Maximum number of grids that can be merged per frame
    /// </summary>
    public const int MAX_GRID_MERGES_PER_FRAME = 1;
    
    // === RENDERING CONSTANTS ===
    
    /// <summary>
    /// Maximum render distance in kilometers
    /// </summary>
    public const float MAX_RENDER_DISTANCE_KM = 50f;
    
    /// <summary>
    /// Distance at which objects start to scale down
    /// </summary>
    public const float SCALE_START_DISTANCE_KM = 25f;
    
    /// <summary>
    /// Minimum scale factor for distant objects
    /// </summary>
    public const float MIN_SCALE_FACTOR = 0.01f;
    
    // === LOD CONSTANTS ===
    
    /// <summary>
    /// Distance for LOD level 0 (full detail) in kilometers
    /// </summary>
    public const float LOD_0_DISTANCE_KM = 1f;
    
    /// <summary>
    /// Distance for LOD level 1 (medium detail) in kilometers
    /// </summary>
    public const float LOD_1_DISTANCE_KM = 10f;
    
    /// <summary>
    /// Distance for LOD level 2 (low detail) in kilometers
    /// </summary>
    public const float LOD_2_DISTANCE_KM = 50f;
    
    /// <summary>
    /// Distance for LOD level 3 (very low detail/hidden) in kilometers
    /// </summary>
    public const float LOD_3_DISTANCE_KM = 100f;
    
    // === CAMERA CONSTANTS ===
    
    /// <summary>
    /// Default camera zoom distance in meters
    /// </summary>
    public const float DEFAULT_ZOOM_DISTANCE_M = 1000f;
    
    /// <summary>
    /// Minimum camera zoom distance in meters
    /// </summary>
    public const float MIN_ZOOM_DISTANCE_M = 100f;
    
    /// <summary>
    /// Maximum camera zoom distance in meters
    /// </summary>
    public const float MAX_ZOOM_DISTANCE_M = 200000f;
    
    /// <summary>
    /// Camera zoom speed multiplier
    /// </summary>
    public const float ZOOM_SPEED_MULTIPLIER = 2f;
    
    /// <summary>
    /// Camera orbit speed multiplier
    /// </summary>
    public const float ORBIT_SPEED_MULTIPLIER = 2f;
    
    // === ENTITY CONSTANTS ===
    
    /// <summary>
    /// Default entity radius in kilometers
    /// </summary>
    public const float DEFAULT_ENTITY_RADIUS_KM = 0.5f;
    
    /// <summary>
    /// Default entity mass in kilograms
    /// </summary>
    public const float DEFAULT_ENTITY_MASS_KG = 1000f;
    
    /// <summary>
    /// Movement threshold for grid boundary checks (meters)
    /// </summary>
    public const float MOVEMENT_THRESHOLD_M = 10f;
    
    // === PHYSICS CONSTANTS ===
    
    /// <summary>
    /// Default thrust force for spaceships (Newtons)
    /// </summary>
    public const float DEFAULT_THRUST_FORCE_N = 1000f;
    
    /// <summary>
    /// Default maximum speed for spaceships (m/s)
    /// </summary>
    public const float DEFAULT_MAX_SPEED_MS = 100f;
    
    /// <summary>
    /// Default warp speed (m/s)
    /// </summary>
    public const float DEFAULT_WARP_SPEED_MS = 10000f;
    
    /// <summary>
    /// Space damping factor (simulates space friction)
    /// </summary>
    public const float SPACE_DAMPING_FACTOR = 0.98f;
    
    // === SOLAR SYSTEM CONSTANTS ===
    
    /// <summary>
    /// Default solar system radius in AU
    /// </summary>
    public const float SOLAR_SYSTEM_RADIUS_AU = 18f;
    
    /// <summary>
    /// Number of asteroid belts in a typical solar system
    /// </summary>
    public const int ASTEROID_BELTS_PER_SYSTEM = 3;
    
    /// <summary>
    /// Average asteroids per belt
    /// </summary>
    public const int ASTEROIDS_PER_BELT = 50;
    
    // === PERFORMANCE CONSTANTS ===
    
    /// <summary>
    /// Maximum entities per grid before performance warnings
    /// </summary>
    public const int MAX_ENTITIES_PER_GRID = 100;
    
    /// <summary>
    /// Update frequency for distant entities (seconds)
    /// </summary>
    public const float DISTANT_ENTITY_UPDATE_INTERVAL = 1f;
    
    /// <summary>
    /// Maximum number of entity updates per frame
    /// </summary>
    public const int MAX_ENTITY_UPDATES_PER_FRAME = 50;
    
    // === UI CONSTANTS ===
    
    /// <summary>
    /// UI scale multiplier for different zoom levels
    /// </summary>
    public const float UI_SCALE_MULTIPLIER = 1f;
    
    /// <summary>
    /// HUD update frequency (seconds)
    /// </summary>
    public const float HUD_UPDATE_INTERVAL = 0.1f;
    
    // === DEBUG CONSTANTS ===
    
    /// <summary>
    /// Whether to show debug gizmos
    /// </summary>
    public const bool SHOW_DEBUG_GIZMOS = true;
    
    /// <summary>
    /// Whether to log grid operations
    /// </summary>
    public const bool LOG_GRID_OPERATIONS = false;
    
    /// <summary>
    /// Whether to log entity movements
    /// </summary>
    public const bool LOG_ENTITY_MOVEMENTS = false;
    
    // === COLOR CONSTANTS ===
    
    /// <summary>
    /// Color for player grid gizmos
    /// </summary>
    public static readonly Color PLAYER_GRID_COLOR = Color.green;
    
    /// <summary>
    /// Color for regular grid gizmos
    /// </summary>
    public static readonly Color GRID_COLOR = Color.blue;
    
    /// <summary>
    /// Color for entity connection lines
    /// </summary>
    public static readonly Color ENTITY_CONNECTION_COLOR = Color.yellow;
    
    /// <summary>
    /// Color for warp target indicators
    /// </summary>
    public static readonly Color WARP_TARGET_COLOR = Color.cyan;
    
    /// <summary>
    /// Color for velocity vectors
    /// </summary>
    public static readonly Color VELOCITY_COLOR = Color.green;
    
    // === ENTITY TYPE CONFIGURATIONS ===
    
    /// <summary>
    /// Configuration for different entity types
    /// </summary>
    public static class EntityTypeConfig
    {
        public struct EntityConfig
        {
            public float defaultRadius;
            public float defaultMass;
            public bool createOwnGrid;
            public Color debugColor;
            
            public EntityConfig(float radius, float mass, bool ownGrid, Color color)
            {
                defaultRadius = radius;
                defaultMass = mass;
                createOwnGrid = ownGrid;
                debugColor = color;
            }
        }
        
        public static readonly EntityConfig Ship = new EntityConfig(0.05f, 1000f, false, Color.white);
        public static readonly EntityConfig Station = new EntityConfig(2f, 1000000f, true, Color.gray);
        public static readonly EntityConfig Asteroid = new EntityConfig(0.1f, 10000f, false, Color.brown);
        public static readonly EntityConfig Planet = new EntityConfig(50f, 1e24f, true, Color.blue);
        public static readonly EntityConfig Star = new EntityConfig(500f, 1e30f, true, Color.yellow);
        public static readonly EntityConfig Gate = new EntityConfig(1f, 100000f, true, Color.magenta);
        public static readonly EntityConfig Beacon = new EntityConfig(0.01f, 100f, false, Color.red);
    }
    
    // === UTILITY METHODS ===
    
    /// <summary>
    /// Gets the configuration for a specific entity type
    /// </summary>
    public static EntityTypeConfig.EntityConfig GetEntityConfig(Entity.EntityType entityType)
    {
        switch (entityType)
        {
            case Entity.EntityType.Ship: return EntityTypeConfig.Ship;
            case Entity.EntityType.Station: return EntityTypeConfig.Station;
            case Entity.EntityType.Asteroid: return EntityTypeConfig.Asteroid;
            case Entity.EntityType.Planet: return EntityTypeConfig.Planet;
            case Entity.EntityType.Star: return EntityTypeConfig.Star;
            case Entity.EntityType.Gate: return EntityTypeConfig.Gate;
            case Entity.EntityType.Beacon: return EntityTypeConfig.Beacon;
            default: return EntityTypeConfig.Ship;
        }
    }
    
    /// <summary>
    /// Checks if a distance is within the maximum render range
    /// </summary>
    public static bool IsWithinRenderDistance(float distanceKm)
    {
        return distanceKm <= MAX_RENDER_DISTANCE_KM;
    }
    
    /// <summary>
    /// Gets the LOD level for a given distance
    /// </summary>
    public static int GetLODLevel(float distanceKm)
    {
        if (distanceKm <= LOD_0_DISTANCE_KM) return 0;
        if (distanceKm <= LOD_1_DISTANCE_KM) return 1;
        if (distanceKm <= LOD_2_DISTANCE_KM) return 2;
        return 3;
    }
    
    /// <summary>
    /// Calculates the scale factor for a given distance
    /// </summary>
    public static float GetScaleFactor(float distanceKm)
    {
        if (distanceKm <= SCALE_START_DISTANCE_KM)
            return 1f;
        
        float scaleFactor = SCALE_START_DISTANCE_KM / distanceKm;
        return Mathf.Max(scaleFactor, MIN_SCALE_FACTOR);
    }
}
