using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Centralized coordinate system manager for precise space positioning
/// Handles conversion between Unity world coordinates and precise space coordinates
/// </summary>
public class SpaceCoordinateManager : MonoBehaviour
{
    private static SpaceCoordinateManager _instance;
    public static SpaceCoordinateManager Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = FindObjectOfType<SpaceCoordinateManager>();
                if (_instance == null)
                {
                    GameObject go = new GameObject("SpaceCoordinateManager");
                    _instance = go.AddComponent<SpaceCoordinateManager>();
                    DontDestroyOnLoad(go);
                }
            }
            return _instance;
        }
    }

    [Header("Coordinate System Settings")]
    [SerializeField] private bool enableDebugLogging = true;
    
    // Coordinate system constants
    public const float METERS_PER_GRID_UNIT = 1000000f; // 1.0 grid unit = 1,000,000 meters = 1000km
    public const float KM_PER_GRID_UNIT = 1000f; // 1.0 grid unit = 1000km
    public const float AU_PER_GRID_UNIT = 1000f / 149597870.7f; // 1.0 grid unit in AU
    
    // Player reference (treated as normal entity)
    private Transform playerTransform;
    private Entity playerEntity;
    
    // Entity tracking
    private Dictionary<Entity, Vector3> entityUniversalPositions = new Dictionary<Entity, Vector3>();
    
    void Awake()
    {
        if (_instance == null)
        {
            _instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else if (_instance != this)
        {
            Destroy(gameObject);
            return;
        }
        
        InitializeCoordinateSystem();

        // Register all existing entities
        RegisterAllExistingEntities();
    }
    
    void InitializeCoordinateSystem()
    {
        // Find player using multiple methods
        GameObject playerShip = GameObject.FindGameObjectWithTag("Player");
        if (playerShip == null)
        {
            playerShip = GameObject.Find("PlayerShip");
        }

        // Also try finding by SpaceshipController component
        if (playerShip == null)
        {
            SpaceshipController[] controllers = FindObjectsOfType<SpaceshipController>();
            if (controllers.Length > 0)
            {
                playerShip = controllers[0].gameObject;
                Debug.Log($"Found player via SpaceshipController: {playerShip.name}");
            }
        }

        if (playerShip != null)
        {
            playerTransform = playerShip.transform;
            playerEntity = playerShip.GetComponent<Entity>();

            if (playerEntity != null)
            {
                Debug.Log($"SpaceCoordinateManager: Player found - {playerShip.name} with Entity component");
            }
            else
            {
                Debug.LogWarning($"SpaceCoordinateManager: Player ship {playerShip.name} found but no Entity component!");
            }
        }
        else
        {
            Debug.LogWarning("SpaceCoordinateManager: Player not found with any method!");
        }
    }

    void RegisterAllExistingEntities()
    {
        // Find and register all existing entities in the scene
        Entity[] allEntities = FindObjectsOfType<Entity>();

        if (enableDebugLogging)
            Debug.Log($"SpaceCoordinateManager: Found {allEntities.Length} entities to register");

        foreach (var entity in allEntities)
        {
            if (entity != null)
            {
                RegisterEntity(entity);
            }
        }

        if (enableDebugLogging)
            Debug.Log($"SpaceCoordinateManager: Registered {entityUniversalPositions.Count} entities");

        // If player entity is still null, try to find it among registered entities
        if (playerEntity == null)
        {
            FindPlayerEntityAmongRegistered();
        }
    }

    void FindPlayerEntityAmongRegistered()
    {
        foreach (var entity in entityUniversalPositions.Keys)
        {
            if (entity != null && entity.name.Contains("Player"))
            {
                playerEntity = entity;
                playerTransform = entity.transform;
                Debug.Log($"Found player entity among registered: {entity.name}");
                return;
            }
        }

        // If still not found, check for SpaceshipController
        foreach (var entity in entityUniversalPositions.Keys)
        {
            if (entity != null && entity.GetComponent<SpaceshipController>() != null)
            {
                playerEntity = entity;
                playerTransform = entity.transform;
                Debug.Log($"Found player entity via SpaceshipController: {entity.name}");
                return;
            }
        }

        Debug.LogWarning("Could not find player entity among registered entities!");
    }
    
    /// <summary>
    /// Registers an entity with the coordinate system
    /// </summary>
    public void RegisterEntity(Entity entity)
    {
        if (entity == null) return;

        if (!entityUniversalPositions.ContainsKey(entity))
        {
            if (entity == playerEntity)
            {
                // Player starts at origin in universal coordinates
                entityUniversalPositions[entity] = Vector3.zero;
                entity.transform.position = Vector3.zero;

                Debug.Log($"Registered PLAYER entity {entity.name} at universal origin");
            }
            else
            {
                // Calculate initial universal position based on world position
                Vector3 worldPos = entity.transform.position;
                Vector3 universalPos = worldPos / METERS_PER_GRID_UNIT; // Direct conversion for initial setup
                entityUniversalPositions[entity] = universalPos;

                if (enableDebugLogging)
                    Debug.Log($"Registered entity {entity.name} at universal position {universalPos} (world: {worldPos})");
            }
        }
    }
    
    /// <summary>
    /// Unregisters an entity from the coordinate system
    /// </summary>
    public void UnregisterEntity(Entity entity)
    {
        if (entity != null && entityUniversalPositions.ContainsKey(entity))
        {
            entityUniversalPositions.Remove(entity);
        }
    }
    
    /// <summary>
    /// Converts world position to universal coordinates (relative to player)
    /// </summary>
    public Vector3 WorldToUniversal(Vector3 worldPosition)
    {
        if (playerEntity != null)
        {
            Vector3 playerUniversalPos = GetEntityUniversalPosition(playerEntity);
            Vector3 relativeUniversalPos = worldPosition / METERS_PER_GRID_UNIT;
            return playerUniversalPos + relativeUniversalPos;
        }

        // Fallback if no player
        return worldPosition / METERS_PER_GRID_UNIT;
    }

    /// <summary>
    /// Converts universal coordinates to world position (relative to player)
    /// </summary>
    public Vector3 UniversalToWorld(Vector3 universalPosition)
    {
        if (playerEntity != null)
        {
            Vector3 playerUniversalPos = GetEntityUniversalPosition(playerEntity);
            Vector3 relativeUniversalPos = universalPosition - playerUniversalPos;
            return relativeUniversalPos * METERS_PER_GRID_UNIT;
        }

        // Fallback if no player
        return universalPosition * METERS_PER_GRID_UNIT;
    }
    
    /// <summary>
    /// Gets the universal position of an entity
    /// </summary>
    public Vector3 GetEntityUniversalPosition(Entity entity)
    {
        if (entity == null)
        {
            Debug.LogWarning("SpaceCoordinateManager: Attempted to get position of null entity");
            return Vector3.zero;
        }

        // Auto-register entity if not already registered
        if (!entityUniversalPositions.ContainsKey(entity))
        {
            RegisterEntity(entity);
        }

        if (entityUniversalPositions.ContainsKey(entity))
        {
            return entityUniversalPositions[entity];
        }

        // Fallback: calculate from world position
        Vector3 fallbackPos = WorldToUniversal(entity.transform.position);
        if (enableDebugLogging)
            Debug.LogWarning($"Using fallback position for {entity.name}: {fallbackPos}");
        return fallbackPos;
    }
    
    /// <summary>
    /// Sets the universal position of an entity
    /// </summary>
    public void SetEntityUniversalPosition(Entity entity, Vector3 universalPosition)
    {
        if (entity == null)
        {
            Debug.LogWarning("SpaceCoordinateManager: Attempted to set position of null entity");
            return;
        }

        // Auto-register entity if not already registered
        if (!entityUniversalPositions.ContainsKey(entity))
        {
            RegisterEntity(entity);
        }

        // Update entity's universal position
        entityUniversalPositions[entity] = universalPosition;

        if (entity == playerEntity)
        {
            // Player stays at origin for rendering, but we move all other entities relative to player
            entity.transform.position = Vector3.zero;

            if (enableDebugLogging)
                Debug.Log($"Player moved to universal position {universalPosition}, updating all entity world positions");

            // Update all other entities' world positions relative to new player position
            UpdateAllEntityWorldPositions();

            if (enableDebugLogging)
                Debug.Log($"Updated {entityUniversalPositions.Count - 1} entity world positions");
        }
        else
        {
            // Update world position for rendering relative to player
            UpdateEntityWorldPosition(entity);
        }

        if (enableDebugLogging)
            Debug.Log($"Set {entity.name} universal position to {universalPosition}");
    }
    
    /// <summary>
    /// Updates an entity's world position relative to the player
    /// </summary>
    private void UpdateEntityWorldPosition(Entity entity)
    {
        if (entity == null || entity == playerEntity) return;

        Vector3 entityUniversalPos = GetEntityUniversalPosition(entity);
        Vector3 playerUniversalPos = GetEntityUniversalPosition(playerEntity);

        // Calculate relative position to player
        Vector3 relativeUniversalPos = entityUniversalPos - playerUniversalPos;

        // Convert to world position
        Vector3 oldWorldPos = entity.transform.position;
        Vector3 newWorldPos = relativeUniversalPos * METERS_PER_GRID_UNIT;
        entity.transform.position = newWorldPos;

        if (enableDebugLogging && Vector3.Distance(oldWorldPos, newWorldPos) > 1000f)
        {
            Debug.Log($"Updated {entity.name} world position from {oldWorldPos} to {newWorldPos} (moved {Vector3.Distance(oldWorldPos, newWorldPos):F0}m)");
        }
    }

    /// <summary>
    /// Updates all entities' world positions relative to the player
    /// </summary>
    private void UpdateAllEntityWorldPositions()
    {
        if (playerEntity == null) return;

        foreach (var entity in entityUniversalPositions.Keys)
        {
            if (entity != null && entity != playerEntity)
            {
                UpdateEntityWorldPosition(entity);
            }
        }
    }

    /// <summary>
    /// Moves the player in universal coordinates
    /// </summary>
    public void MovePlayer(Vector3 universalOffset)
    {
        if (playerEntity == null) return;

        if (enableDebugLogging)
            Debug.Log($"Moving player by universal offset: {universalOffset}");

        // Get current player universal position
        Vector3 currentPlayerPos = GetEntityUniversalPosition(playerEntity);

        // Calculate new player universal position
        Vector3 newPlayerPos = currentPlayerPos + universalOffset;

        // Update player's universal position (this will update all other entities' world positions)
        SetEntityUniversalPosition(playerEntity, newPlayerPos);
    }
    
    /// <summary>
    /// Gets the distance between two entities in meters
    /// </summary>
    public float GetDistanceBetweenEntities(Entity entity1, Entity entity2)
    {
        if (entity1 == null || entity2 == null)
        {
            Debug.LogWarning("SpaceCoordinateManager: Attempted to get distance between null entities");
            return 0f;
        }

        Vector3 pos1 = GetEntityUniversalPosition(entity1);
        Vector3 pos2 = GetEntityUniversalPosition(entity2);

        float universalDistance = Vector3.Distance(pos1, pos2);
        return universalDistance * METERS_PER_GRID_UNIT;
    }
    
    /// <summary>
    /// Warps player to the target's grid and positions them 10km away within that grid
    /// </summary>
    public void WarpToDistanceFromEntity(Entity target, float distanceMeters)
    {
        if (target == null)
        {
            Debug.LogWarning("SpaceCoordinateManager: Cannot warp to null target");
            return;
        }

        if (playerEntity == null)
        {
            Debug.LogWarning("SpaceCoordinateManager: No player entity found for warp");
            return;
        }

        if (target == playerEntity)
        {
            Debug.LogWarning("SpaceCoordinateManager: Cannot warp to self");
            return;
        }

        Vector3 playerPos = GetEntityUniversalPosition(playerEntity);
        Vector3 targetPos = GetEntityUniversalPosition(target);

        Debug.Log($"=== GRID-BASED WARP DEBUG START ===");
        Debug.Log($"Player universal position: {playerPos}");
        Debug.Log($"Target universal position: {targetPos}");
        Debug.Log($"Current distance: {Vector3.Distance(playerPos, targetPos) * METERS_PER_GRID_UNIT:F2}m");

        // Step 1: Move player to target's grid (same universal position as target)
        Vector3 targetGridPos = targetPos;
        Debug.Log($"Moving player to target's grid at: {targetGridPos}");

        // Step 2: Calculate 10km offset within the grid
        // 10km = 10,000m in grid-local coordinates
        float gridLocalDistance = distanceMeters; // 10,000 meters

        // Calculate direction from target to player (or use default if too close)
        Vector3 direction = (playerPos - targetPos);
        if (direction.magnitude < 0.001f)
        {
            // If too close, use a default direction
            direction = Vector3.forward;
            Debug.Log("Using default direction (too close to target)");
        }
        direction = direction.normalized;
        Debug.Log($"Warp direction: {direction}");

        // Step 3: Set player's universal position to target's grid
        SetEntityUniversalPosition(playerEntity, targetGridPos);
        Debug.Log($"Player moved to target's grid at universal position: {targetGridPos}");

        // Step 4: Apply 10km offset in world coordinates (within the grid)
        Vector3 gridLocalOffset = direction * gridLocalDistance;
        playerEntity.transform.position = gridLocalOffset;
        Debug.Log($"Applied grid-local offset: {gridLocalOffset}");
        Debug.Log($"Player final world position: {playerEntity.transform.position}");

        // Step 5: Update target's world position to be at grid center
        target.transform.position = Vector3.zero;
        Debug.Log($"Target positioned at grid center: {target.transform.position}");

        // Verify the final distance
        float finalDistance = Vector3.Distance(playerEntity.transform.position, target.transform.position);
        Debug.Log($"Final distance (world coordinates): {finalDistance:F2}m");
        Debug.Log($"=== GRID-BASED WARP DEBUG END ===");
    }

    /// <summary>
    /// Moves player and target to the same grid, with player positioned at specified distance
    /// </summary>
    public void WarpToGridWithDistance(Entity target, float distanceMeters)
    {
        if (target == null || playerEntity == null || target == playerEntity) return;

        Vector3 targetPos = GetEntityUniversalPosition(target);

        Debug.Log($"=== GRID WARP START ===");
        Debug.Log($"Target grid position: {targetPos}");
        Debug.Log($"Desired distance: {distanceMeters}m");

        // Move both entities to the same universal grid position
        SetEntityUniversalPosition(playerEntity, targetPos);
        SetEntityUniversalPosition(target, targetPos);

        // Position target at grid center (0,0,0 in world coordinates)
        target.transform.position = Vector3.zero;

        // Position player at specified distance from target
        Vector3 offset = Vector3.forward * distanceMeters; // 10km forward from target
        playerEntity.transform.position = offset;

        // Update grid system if it exists
        if (SpaceGridSystem.Instance != null)
        {
            // Ensure both entities are in the same grid
            SpaceGridSystem.Instance.UpdateEntityGrid(playerEntity);
            SpaceGridSystem.Instance.UpdateEntityGrid(target);
        }

        Debug.Log($"Target world position: {target.transform.position}");
        Debug.Log($"Player world position: {playerEntity.transform.position}");
        Debug.Log($"Actual distance: {Vector3.Distance(playerEntity.transform.position, target.transform.position):F2}m");
        Debug.Log($"=== GRID WARP END ===");
    }
    
    /// <summary>
    /// Gets formatted distance string
    /// </summary>
    public string FormatDistance(float meters)
    {
        return SpaceCoordinates.FormatDistance(meters);
    }
    
    /// <summary>
    /// Updates entity positions (call this when entities move)
    /// </summary>
    public void UpdateEntityPosition(Entity entity)
    {
        if (entity == null || entity == playerEntity) return;
        
        // Update universal position based on current world position
        Vector3 universalPos = WorldToUniversal(entity.transform.position);
        entityUniversalPositions[entity] = universalPos;
    }
    
    /// <summary>
    /// Gets debug information about the coordinate system
    /// </summary>
    public string GetDebugInfo()
    {
        string info = $"=== COORDINATE SYSTEM DEBUG ===\n";
        if (playerEntity != null)
        {
            Vector3 playerUniversalPos = GetEntityUniversalPosition(playerEntity);
            info += $"Player Universal Position: {playerUniversalPos}\n";
        }
        info += $"Registered Entities: {entityUniversalPositions.Count}\n";

        foreach (var kvp in entityUniversalPositions)
        {
            if (kvp.Key != null)
            {
                Vector3 worldPos = kvp.Key.transform.position;
                if (playerEntity != null && kvp.Key != playerEntity)
                {
                    float distance = GetDistanceBetweenEntities(playerEntity, kvp.Key);
                    info += $"{kvp.Key.name}: Universal {kvp.Value}, World {worldPos}, Distance {FormatDistance(distance)}\n";
                }
                else
                {
                    info += $"{kvp.Key.name}: Universal {kvp.Value}, World {worldPos}\n";
                }
            }
        }

        return info;
    }
    
    /// <summary>
    /// Manually sets the player entity (useful if auto-detection fails)
    /// </summary>
    public void SetPlayerEntity(Entity entity)
    {
        if (entity != null)
        {
            playerEntity = entity;
            playerTransform = entity.transform;
            Debug.Log($"Manually set player entity to: {entity.name}");

            // Ensure player is registered
            if (!entityUniversalPositions.ContainsKey(entity))
            {
                RegisterEntity(entity);
            }
        }
    }

    // Properties
    public Vector3 PlayerUniversalPosition => playerEntity != null ? GetEntityUniversalPosition(playerEntity) : Vector3.zero;
    public Transform PlayerTransform => playerTransform;
    public Entity PlayerEntity => playerEntity;
    public bool EnableDebugLogging { get => enableDebugLogging; set => enableDebugLogging = value; }
}
