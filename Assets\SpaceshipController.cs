using UnityEngine;

/// <summary>
/// Simple spaceship controller for testing the EVE Online-inspired space system
/// </summary>
public class SpaceshipController : MonoBehaviour
{
    [Header("Movement Settings")]
    [SerializeField] private float thrustForce = 1000f;
    [SerializeField] private float maxSpeed = 100f; // m/s
    [SerializeField] private float rotationSpeed = 90f; // degrees per second
    [SerializeField] private float dampingFactor = 0.98f;
    
    [Header("Warp Settings")]
    [SerializeField] private float warpSpeed = 10000f; // 10km/s
    [SerializeField] private bool canWarp = true;
    
    // Movement state
    private Vector3 velocity = Vector3.zero;
    private bool isWarping = false;
    private Vector3 warpTarget;
    
    // Components
    private Entity shipEntity;
    private Rigidbody shipRigidbody;
    
    void Start()
    {
        Initialize();
    }
    
    void Update()
    {
        HandleInput();
        UpdateMovement();
    }
    
    /// <summary>
    /// Initializes the spaceship controller
    /// </summary>
    void Initialize()
    {
        shipEntity = GetComponent<Entity>();
        shipRigidbody = GetComponent<Rigidbody>();
        
        // If no rigidbody, add one
        if (shipRigidbody == null)
        {
            shipRigidbody = gameObject.AddComponent<Rigidbody>();
            shipRigidbody.useGravity = false;
            shipRigidbody.drag = 0f;
            shipRigidbody.angularDrag = 5f;
        }
        
        Debug.Log("Spaceship controller initialized");
    }
    
    /// <summary>
    /// Handles player input
    /// </summary>
    void HandleInput()
    {
        if (isWarping) return;
        
        // Thrust controls
        float thrust = 0f;
        if (Input.GetKey(KeyCode.W) || Input.GetKey(KeyCode.UpArrow))
            thrust = 1f;
        else if (Input.GetKey(KeyCode.S) || Input.GetKey(KeyCode.DownArrow))
            thrust = -0.5f; // Reverse thrust is weaker
        
        // Rotation controls
        float rotation = 0f;
        if (Input.GetKey(KeyCode.A) || Input.GetKey(KeyCode.LeftArrow))
            rotation = 1f;
        else if (Input.GetKey(KeyCode.D) || Input.GetKey(KeyCode.RightArrow))
            rotation = -1f;
        
        // Apply thrust
        if (Mathf.Abs(thrust) > 0.1f)
        {
            Vector3 thrustDirection = transform.forward * thrust;
            velocity += thrustDirection * thrustForce * Time.deltaTime;
            
            // Clamp to max speed
            if (velocity.magnitude > maxSpeed)
            {
                velocity = velocity.normalized * maxSpeed;
            }
        }
        
        // Apply rotation
        if (Mathf.Abs(rotation) > 0.1f)
        {
            transform.Rotate(0, rotation * rotationSpeed * Time.deltaTime, 0);
        }
        
        // Warp controls
        if (canWarp && Input.GetKeyDown(KeyCode.Space))
        {
            InitiateWarp();
        }
        
        // Stop/brake
        if (Input.GetKey(KeyCode.X))
        {
            velocity *= 0.9f; // Quick brake
        }
    }
    
    /// <summary>
    /// Updates movement and applies physics
    /// </summary>
    void UpdateMovement()
    {
        if (isWarping)
        {
            UpdateWarpMovement();
            return;
        }
        
        // Apply damping (space friction simulation)
        velocity *= dampingFactor;
        
        // Apply velocity to position
        if (velocity.magnitude > 0.01f)
        {
            transform.position += velocity * Time.deltaTime;
        }
        
        // Update rigidbody if present
        if (shipRigidbody != null)
        {
            shipRigidbody.velocity = velocity;
        }
    }
    
    /// <summary>
    /// Initiates warp to a random location
    /// </summary>
    void InitiateWarp()
    {
        if (isWarping) return;
        
        // Generate random warp target
        Vector3 randomDirection = Random.onUnitSphere;
        randomDirection.y *= 0.1f; // Keep mostly horizontal
        float warpDistance = Random.Range(10000f, 100000f); // 10-100km
        
        warpTarget = transform.position + randomDirection * warpDistance;
        isWarping = true;
        
        Debug.Log($"Initiating warp to {warpTarget}, distance: {warpDistance / 1000f:F1}km");
        
        // Visual effect could be added here
        StartWarpEffect();
    }
    
    /// <summary>
    /// Updates warp movement
    /// </summary>
    void UpdateWarpMovement()
    {
        Vector3 directionToTarget = (warpTarget - transform.position).normalized;
        float distanceToTarget = Vector3.Distance(transform.position, warpTarget);
        
        // Move towards target at warp speed
        Vector3 warpVelocity = directionToTarget * warpSpeed;
        transform.position += warpVelocity * Time.deltaTime;
        
        // Check if we've reached the target
        if (distanceToTarget < 100f) // Within 100m
        {
            ExitWarp();
        }
        
        // Auto-exit warp after 10 seconds (safety)
        if (isWarping && Time.time > warpStartTime + 10f)
        {
            ExitWarp();
        }
    }
    
    private float warpStartTime;
    
    /// <summary>
    /// Starts warp visual effects
    /// </summary>
    void StartWarpEffect()
    {
        warpStartTime = Time.time;
        
        // Add visual effects here (particles, screen effects, etc.)
        // For now, just change the ship color
        Renderer shipRenderer = GetComponent<Renderer>();
        if (shipRenderer != null)
        {
            shipRenderer.material.color = Color.cyan;
        }
    }
    
    /// <summary>
    /// Exits warp mode
    /// </summary>
    void ExitWarp()
    {
        isWarping = false;
        velocity = Vector3.zero; // Stop all momentum
        
        Debug.Log("Exited warp");
        
        // Reset visual effects
        Renderer shipRenderer = GetComponent<Renderer>();
        if (shipRenderer != null)
        {
            shipRenderer.material.color = Color.white;
        }
    }
    
    /// <summary>
    /// Warps to a specific position
    /// </summary>
    public void WarpToPosition(Vector3 targetPosition)
    {
        if (!canWarp) return;
        
        warpTarget = targetPosition;
        isWarping = true;
        StartWarpEffect();
        
        Debug.Log($"Warping to specific position: {targetPosition}");
    }
    
    /// <summary>
    /// Sets the maximum speed
    /// </summary>
    public void SetMaxSpeed(float newMaxSpeed)
    {
        maxSpeed = newMaxSpeed;
    }
    
    /// <summary>
    /// Gets current speed in m/s
    /// </summary>
    public float GetCurrentSpeed()
    {
        return velocity.magnitude;
    }
    
    /// <summary>
    /// Gets current speed as a formatted string
    /// </summary>
    public string GetSpeedDisplayString()
    {
        float speed = GetCurrentSpeed();
        
        if (isWarping)
        {
            return $"WARP: {warpSpeed / 1000f:F1} km/s";
        }
        else if (speed < 1000f)
        {
            return $"{speed:F1} m/s";
        }
        else
        {
            return $"{speed / 1000f:F2} km/s";
        }
    }
    
    void OnGUI()
    {
        // Display ship status
        GUI.Label(new Rect(10, Screen.height - 80, 200, 20), $"Speed: {GetSpeedDisplayString()}");
        GUI.Label(new Rect(10, Screen.height - 60, 200, 20), $"Position: {transform.position.magnitude / 1000f:F1}km from origin");
        
        if (shipEntity != null)
        {
            GUI.Label(new Rect(10, Screen.height - 40, 200, 20), $"Grid: {shipEntity.CurrentGrid?.GridId ?? -1}");
        }
        
        // Controls
        GUI.Label(new Rect(10, Screen.height - 160, 200, 20), "Ship Controls:");
        GUI.Label(new Rect(10, Screen.height - 140, 200, 20), "WASD/Arrows - Move/Rotate");
        GUI.Label(new Rect(10, Screen.height - 120, 200, 20), "Space - Random Warp");
        GUI.Label(new Rect(10, Screen.height - 100, 200, 20), "X - Brake");
    }
    
    void OnDrawGizmosSelected()
    {
        // Draw velocity vector
        Gizmos.color = Color.green;
        Gizmos.DrawRay(transform.position, velocity.normalized * 100f);
        
        // Draw warp target if warping
        if (isWarping)
        {
            Gizmos.color = Color.cyan;
            Gizmos.DrawWireSphere(warpTarget, 50f);
            Gizmos.DrawLine(transform.position, warpTarget);
        }
    }
    
    // Properties
    public bool IsWarping => isWarping;
    public Vector3 Velocity => velocity;
    public float MaxSpeed => maxSpeed;
    public float ThrustForce => thrustForce;
}
