using UnityEngine;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Enhanced spaceship controller with EVE Online-inspired warp system
/// Supports warping to stations, planets, and other celestial objects
/// </summary>
public class SpaceshipController : MonoBehaviour
{
    [Header("Movement Settings")]
    [SerializeField] private float thrustForce = 1000f;
    [SerializeField] private float maxSpeed = 100f; // m/s
    [SerializeField] private float rotationSpeed = 90f; // degrees per second
    [SerializeField] private float dampingFactor = 0.98f;

    [Header("Warp Settings")]
    [SerializeField] private float warpSpeed = 10000f; // 10km/s
    [SerializeField] private bool canWarp = true;
    [SerializeField] private float warpAlignTime = 3f; // Time to align for warp
    [SerializeField] private float warpMinDistance = 1000f; // Minimum warp distance (1km)
    [SerializeField] private float maxWarpTime = 5f; // Maximum warp duration
    [SerializeField] private float warpAccelTime = 1f; // Acceleration time
    [SerializeField] private float warpDecelTime = 1f; // Deceleration time
    [SerializeField] private float gridInteractionDistance = 500000f; // 500km in meters
    
    [Header("Warp UI Settings")]
    [SerializeField] private bool showWarpUI = true;
    [SerializeField] private KeyCode warpMenuKey = KeyCode.Space;
    [SerializeField] private KeyCode cancelWarpKey = KeyCode.Escape;

    // Movement state
    private Vector3 velocity = Vector3.zero;
    private bool isWarping = false;
    private bool isAligning = false;
    private bool showWarpMenu = false;
    private Vector3 warpTarget;
    private Entity warpTargetEntity;
    private float warpAlignTimer = 0f;

    // Warp movement state
    private float warpTimer = 0f;
    private float totalWarpTime = 0f;
    private Vector3 warpStartPosition;
    private Vector3 warpDirection;
    private float warpDistance;
    private AnimationCurve warpCurve;

    // Warp destinations
    private List<WarpDestination> availableDestinations = new List<WarpDestination>();
    private int selectedDestinationIndex = 0;

    // Components
    private Entity shipEntity;
    private Rigidbody shipRigidbody;

    // Warp destination data structure
    [System.Serializable]
    public class WarpDestination
    {
        public Entity targetEntity;
        public string name;
        public Entity.EntityType type;
        public Vector3 position;
        public float distanceKm;
        public float distanceAU;
        public bool isInRange;

        public WarpDestination(Entity entity)
        {
            targetEntity = entity;
            name = entity.name;
            type = entity.Type;
            position = entity.GetUniversalPosition();

            // Calculate distance from player (assuming player is at origin)
            float distanceInUnityUnits = Vector3.Distance(Vector3.zero, entity.transform.position);
            distanceKm = SpaceCoordinates.UnityUnitsToKilometers(distanceInUnityUnits);
            distanceAU = SpaceCoordinates.UnityUnitsToAU(distanceInUnityUnits);
            isInRange = distanceKm >= 1f; // Minimum 1km for warp
        }

        public void UpdateDistance()
        {
            if (targetEntity != null)
            {
                position = targetEntity.GetUniversalPosition();
                float distanceInUnityUnits = Vector3.Distance(Vector3.zero, targetEntity.transform.position);
                distanceKm = SpaceCoordinates.UnityUnitsToKilometers(distanceInUnityUnits);
                distanceAU = SpaceCoordinates.UnityUnitsToAU(distanceInUnityUnits);
                isInRange = distanceKm >= 1f;
            }
        }

        public string GetDistanceString()
        {
            if (distanceAU >= 0.001f)
                return $"{distanceAU:F3} AU";
            else if (distanceKm >= 1f)
                return $"{distanceKm:F1} km";
            else
                return $"{distanceKm * 1000f:F0} m";
        }

        public string GetTypeIcon()
        {
            switch (type)
            {
                case Entity.EntityType.Station: return "🏭";
                case Entity.EntityType.Planet: return "🪐";
                case Entity.EntityType.Star: return "⭐";
                case Entity.EntityType.Asteroid: return "🪨";
                case Entity.EntityType.Gate: return "🚪";
                case Entity.EntityType.Beacon: return "📡";
                case Entity.EntityType.Ship: return "🚀";
                default: return "❓";
            }
        }

        public string GetWarpInfo()
        {
            return $"Warp to 10km from {name}";
        }
    }
    
    void Start()
    {
        Initialize();
    }

    void Update()
    {
        HandleInput();
        UpdateMovement();
        UpdateWarpDestinations();
        UpdateWarpAlignment();
    }
    
    /// <summary>
    /// Initializes the spaceship controller
    /// </summary>
    void Initialize()
    {
        shipEntity = GetComponent<Entity>();
        shipRigidbody = GetComponent<Rigidbody>();

        // If no rigidbody, add one
        if (shipRigidbody == null)
        {
            shipRigidbody = gameObject.AddComponent<Rigidbody>();
            shipRigidbody.useGravity = false;
            shipRigidbody.linearDamping = 0f;
            shipRigidbody.angularDamping = 5f;
        }

        // Initialize warp destinations
        RefreshWarpDestinations();

        // Initialize warp curve for smooth acceleration/deceleration
        InitializeWarpCurve();
    }

    /// <summary>
    /// Refreshes the list of available warp destinations
    /// </summary>
    void RefreshWarpDestinations()
    {
        availableDestinations.Clear();

        // Find all entities in the scene that can be warp destinations
        Entity[] allEntities = FindObjectsOfType<Entity>();

        foreach (Entity entity in allEntities)
        {
            // Skip self and ships (unless it's a special ship like a carrier)
            if (entity == shipEntity || entity.Type == Entity.EntityType.Ship)
                continue;

            // Add valid warp destinations
            if (IsValidWarpDestination(entity))
            {
                availableDestinations.Add(new WarpDestination(entity));
            }
        }

        // Sort by distance
        availableDestinations = availableDestinations.OrderBy(d => d.distanceKm).ToList();
    }

    /// <summary>
    /// Checks if an entity is a valid warp destination
    /// </summary>
    bool IsValidWarpDestination(Entity entity)
    {
        // Valid warp destinations
        switch (entity.Type)
        {
            case Entity.EntityType.Station:
            case Entity.EntityType.Planet:
            case Entity.EntityType.Star:
            case Entity.EntityType.Gate:
            case Entity.EntityType.Beacon:
                return true;
            case Entity.EntityType.Asteroid:
                // Only large asteroids are warp destinations
                return entity.RadiusKm >= 0.1f;
            default:
                return false;
        }
    }

    /// <summary>
    /// Initializes the warp acceleration/deceleration curve
    /// </summary>
    void InitializeWarpCurve()
    {
        warpCurve = new AnimationCurve();

        // Create smooth acceleration and deceleration curve
        float accelRatio = warpAccelTime / maxWarpTime;
        float decelRatio = warpDecelTime / maxWarpTime;
        float cruiseRatio = 1f - accelRatio - decelRatio;

        // Acceleration phase (0 to accelRatio)
        warpCurve.AddKey(0f, 0f);
        warpCurve.AddKey(accelRatio, accelRatio);

        // Cruise phase (accelRatio to 1-decelRatio)
        if (cruiseRatio > 0f)
        {
            warpCurve.AddKey(1f - decelRatio, 1f - decelRatio);
        }

        // Deceleration phase (1-decelRatio to 1)
        warpCurve.AddKey(1f, 1f);

        // Set tangent modes for smooth curves
        for (int i = 0; i < warpCurve.keys.Length; i++)
        {
            warpCurve.SmoothTangents(i, 0f);
        }
    }
    
    /// <summary>
    /// Handles player input
    /// </summary>
    void HandleInput()
    {
        // Handle warp menu input
        HandleWarpMenuInput();

        // Don't allow normal movement during warp or alignment
        if (isWarping || isAligning) return;

        // Thrust controls
        float thrust = 0f;
        if (Input.GetKey(KeyCode.W) || Input.GetKey(KeyCode.UpArrow))
            thrust = 1f;
        else if (Input.GetKey(KeyCode.S) || Input.GetKey(KeyCode.DownArrow))
            thrust = -0.5f; // Reverse thrust is weaker

        // Rotation controls
        float rotation = 0f;
        if (Input.GetKey(KeyCode.A) || Input.GetKey(KeyCode.LeftArrow))
            rotation = 1f;
        else if (Input.GetKey(KeyCode.D) || Input.GetKey(KeyCode.RightArrow))
            rotation = -1f;

        // Apply thrust
        if (Mathf.Abs(thrust) > 0.1f)
        {
            Vector3 thrustDirection = transform.forward * thrust;
            velocity += thrustDirection * thrustForce * Time.deltaTime;

            // Clamp to max speed
            if (velocity.magnitude > maxSpeed)
            {
                velocity = velocity.normalized * maxSpeed;
            }
        }

        // Apply rotation
        if (Mathf.Abs(rotation) > 0.1f)
        {
            transform.Rotate(0, rotation * rotationSpeed * Time.deltaTime, 0);
        }

        // Stop/brake
        if (Input.GetKey(KeyCode.X))
        {
            velocity *= 0.9f; // Quick brake
        }
    }

    /// <summary>
    /// Handles warp menu input
    /// </summary>
    void HandleWarpMenuInput()
    {
        // Toggle warp menu
        if (Input.GetKeyDown(warpMenuKey) && canWarp && !isWarping && !isAligning)
        {
            showWarpMenu = !showWarpMenu;
            if (showWarpMenu)
            {
                RefreshWarpDestinations();
                selectedDestinationIndex = 0;
            }
        }

        // Cancel warp menu or abort warp
        if (Input.GetKeyDown(cancelWarpKey))
        {
            if (showWarpMenu)
            {
                showWarpMenu = false;
            }
            else if (isAligning)
            {
                CancelWarp();
            }
        }

        // Navigate warp menu
        if (showWarpMenu && availableDestinations.Count > 0)
        {
            if (Input.GetKeyDown(KeyCode.UpArrow) || Input.GetKeyDown(KeyCode.W))
            {
                selectedDestinationIndex = (selectedDestinationIndex - 1 + availableDestinations.Count) % availableDestinations.Count;
            }
            else if (Input.GetKeyDown(KeyCode.DownArrow) || Input.GetKeyDown(KeyCode.S))
            {
                selectedDestinationIndex = (selectedDestinationIndex + 1) % availableDestinations.Count;
            }

            // Select destination
            if (Input.GetKeyDown(KeyCode.Return) || Input.GetKeyDown(KeyCode.KeypadEnter))
            {
                if (selectedDestinationIndex < availableDestinations.Count)
                {
                    InitiateWarpToDestination(availableDestinations[selectedDestinationIndex]);
                }
            }
        }

        // Quick warp to nearest station (Alt + Space)
        if (Input.GetKey(KeyCode.LeftAlt) && Input.GetKeyDown(warpMenuKey) && canWarp && !isWarping && !isAligning)
        {
            WarpToNearestStation();
        }
    }
    
    /// <summary>
    /// Updates movement and applies physics - player stays at origin, world moves
    /// </summary>
    void UpdateMovement()
    {
        if (isWarping)
        {
            UpdateWarpMovement();
            return;
        }

        // Don't move during alignment
        if (isAligning) return;

        // Apply damping (space friction simulation)
        velocity *= dampingFactor;

        // Apply velocity by moving the player
        if (velocity.magnitude > 0.01f)
        {
            Vector3 playerMovement = velocity * Time.deltaTime;

            if (SpaceCoordinateManager.Instance != null)
            {
                Vector3 universalMovement = playerMovement / SpaceCoordinateManager.METERS_PER_GRID_UNIT;
                SpaceCoordinateManager.Instance.MovePlayer(universalMovement);
            }
            else
            {
                // Fallback: move player directly
                transform.position += playerMovement;
            }
        }

        // Keep player at origin
        transform.position = Vector3.zero;

        // Update rigidbody if present
        if (shipRigidbody != null)
        {
            shipRigidbody.linearVelocity = Vector3.zero; // Player never moves
        }
    }

    /// <summary>
    /// Updates warp destinations distances
    /// </summary>
    void UpdateWarpDestinations()
    {
        if (availableDestinations.Count == 0) return;

        // Update distances every frame for accurate display
        foreach (var destination in availableDestinations)
        {
            destination.UpdateDistance();
        }

        // Re-sort by distance every few seconds
        if (Time.time % 2f < Time.deltaTime)
        {
            availableDestinations = availableDestinations.OrderBy(d => d.distanceKm).ToList();
        }
    }

    /// <summary>
    /// Updates warp alignment process
    /// </summary>
    void UpdateWarpAlignment()
    {
        if (!isAligning) return;

        warpAlignTimer += Time.deltaTime;

        // Rotate towards warp target
        if (warpTargetEntity != null)
        {
            Vector3 directionToTarget = (warpTargetEntity.transform.position - transform.position).normalized;
            Quaternion targetRotation = Quaternion.LookRotation(directionToTarget);
            transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, Time.deltaTime * 2f);
        }

        // Complete alignment and start warp
        if (warpAlignTimer >= warpAlignTime)
        {
            StartWarp();
        }
    }
    
    /// <summary>
    /// Initiates warp to a specific destination
    /// </summary>
    void InitiateWarpToDestination(WarpDestination destination)
    {
        if (isWarping || isAligning || destination.targetEntity == null) return;

        // Check minimum distance
        if (destination.distanceKm < warpMinDistance / 1000f)
        {
            return;
        }

        warpTargetEntity = destination.targetEntity;
        warpTarget = destination.targetEntity.transform.position;
        showWarpMenu = false;

        // Calculate warp parameters using centralized coordinate manager
        warpStartPosition = Vector3.zero; // Player position (always at origin)

        // Get distance using coordinate manager
        float currentDistance = 0f;
        if (SpaceCoordinateManager.Instance != null)
        {
            currentDistance = SpaceCoordinateManager.Instance.GetDistanceBetweenEntities(shipEntity, destination.targetEntity);
        }
        else
        {
            // Fallback to world position
            currentDistance = Vector3.Distance(Vector3.zero, destination.targetEntity.transform.position);
        }

        // Calculate safe exit distance (always 10km)
        float exitDistance = CalculateWarpExitDistance();

        // Calculate how far we need to move
        warpDistance = Mathf.Max(0f, currentDistance - exitDistance);

        // Calculate direction (from player to target)
        Vector3 targetWorldPos = destination.targetEntity.transform.position;
        warpDirection = targetWorldPos.normalized;

        // Clamp warp time to maximum
        totalWarpTime = Mathf.Min(maxWarpTime, warpDistance / warpSpeed);

        // INSTANT WARP FOR DEBUGGING - Skip alignment and warp phases
        Debug.Log($"Initiating INSTANT warp to {destination.name} - Distance: {SpaceCoordinates.FormatDistance(warpDistance)}");

        // Immediately execute grid-based warp
        if (SpaceCoordinateManager.Instance != null && destination.targetEntity != null)
        {
            SpaceCoordinateManager.Instance.WarpToGridWithDistance(destination.targetEntity, 10000f);
        }

        // Clear warp state
        isAligning = false;
        isWarping = false;
        warpTargetEntity = null;
        velocity = Vector3.zero;

        Debug.Log("INSTANT warp completed!");
    }

    /// <summary>
    /// Warps to the nearest station
    /// </summary>
    void WarpToNearestStation()
    {
        var nearestStation = availableDestinations
            .Where(d => d.type == Entity.EntityType.Station && d.isInRange)
            .OrderBy(d => d.distanceKm)
            .FirstOrDefault();

        if (nearestStation != null)
        {
            InitiateWarpToDestination(nearestStation);
        }
    }

    /// <summary>
    /// Starts the actual warp after alignment
    /// </summary>
    void StartWarp()
    {
        isAligning = false;
        isWarping = true;
        warpTimer = 0f;
        warpStartTime = Time.time;

        // Visual effect for warp start
        StartWarpEffect();
    }

    /// <summary>
    /// Cancels warp alignment
    /// </summary>
    void CancelWarp()
    {
        isAligning = false;
        isWarping = false;
        warpAlignTimer = 0f;
        warpTimer = 0f;
        warpTargetEntity = null;

        // Reset visual effects
        ResetWarpEffects();
    }
    
    /// <summary>
    /// Updates warp movement - player stays at origin, everything else moves
    /// </summary>
    void UpdateWarpMovement()
    {
        warpTimer += Time.deltaTime;
        float warpProgress = warpTimer / totalWarpTime;

        if (warpProgress >= 1f)
        {
            ExitWarp();
            return;
        }

        // Calculate smooth movement using curve
        float curveProgress = warpCurve.Evaluate(warpProgress);

        // Calculate how much we should move this frame
        float previousProgress = warpCurve.Evaluate(Mathf.Max(0f, (warpTimer - Time.deltaTime) / totalWarpTime));
        float deltaProgress = curveProgress - previousProgress;
        float deltaMovement = warpDistance * deltaProgress;

        // Move everything else by the inverse of our movement
        if (Mathf.Abs(deltaMovement) > 0.01f)
        {
            Vector3 worldOffset = -warpDirection * deltaMovement;
            MoveWorld(worldOffset);
        }

        // Keep player at origin
        transform.position = Vector3.zero;
    }

    /// <summary>
    /// Moves the player using the coordinate manager
    /// </summary>
    void MoveWorld(Vector3 offset)
    {
        if (SpaceCoordinateManager.Instance != null)
        {
            // Convert world offset to universal coordinates
            Vector3 universalOffset = offset / SpaceCoordinateManager.METERS_PER_GRID_UNIT;

            // Move the player using the coordinate manager
            SpaceCoordinateManager.Instance.MovePlayer(universalOffset);
        }
        else
        {
            // Fallback: move player directly
            transform.position += offset;
        }
    }



    /// <summary>
    /// Calculates the appropriate warp exit distance - always 10km for all targets
    /// </summary>
    float CalculateWarpExitDistance()
    {
        return 10000f; // Always land 10km from target
    }
    
    private float warpStartTime;

    /// <summary>
    /// Starts alignment visual effects
    /// </summary>
    void StartAlignmentEffect()
    {
        Renderer shipRenderer = GetComponent<Renderer>();
        if (shipRenderer != null)
        {
            shipRenderer.material.color = Color.yellow;
        }
    }

    /// <summary>
    /// Starts warp visual effects
    /// </summary>
    void StartWarpEffect()
    {
        warpStartTime = Time.time;

        // Add visual effects here (particles, screen effects, etc.)
        // For now, just change the ship color
        Renderer shipRenderer = GetComponent<Renderer>();
        if (shipRenderer != null)
        {
            shipRenderer.material.color = Color.cyan;
        }
    }

    /// <summary>
    /// Exits warp mode and ensures exact 10km distance from target
    /// </summary>
    void ExitWarp()
    {
        isWarping = false;
        isAligning = false;
        velocity = Vector3.zero; // Stop all momentum
        warpTimer = 0f;

        // Ensure player is at origin
        transform.position = Vector3.zero;

        // Final position adjustment using coordinate manager
        if (warpTargetEntity != null && SpaceCoordinateManager.Instance != null)
        {
            // Use coordinate manager to warp to exact distance
            SpaceCoordinateManager.Instance.WarpToDistanceFromEntity(warpTargetEntity, 10000f);

            // Verify final distance
            float finalDistance = SpaceCoordinateManager.Instance.GetDistanceBetweenEntities(shipEntity, warpTargetEntity);
            Debug.Log($"Warp completed to {warpTargetEntity.name}. Final distance: {SpaceCoordinates.FormatDistance(finalDistance)} (Target: 10km)");
        }

        // Reset visual effects
        ResetWarpEffects();

        // Refresh destinations after warp
        RefreshWarpDestinations();

        // Clear warp target
        warpTargetEntity = null;
    }

    /// <summary>
    /// Resets all warp visual effects
    /// </summary>
    void ResetWarpEffects()
    {
        Renderer shipRenderer = GetComponent<Renderer>();
        if (shipRenderer != null)
        {
            shipRenderer.material.color = Color.white;
        }
    }
    
    /// <summary>
    /// Warps to a specific position (legacy method)
    /// </summary>
    public void WarpToPosition(Vector3 targetPosition)
    {
        if (!canWarp) return;

        warpTarget = targetPosition;
        warpTargetEntity = null;

        // Calculate warp parameters - player is always at origin
        warpStartPosition = Vector3.zero; // Player position
        warpDirection = targetPosition.normalized;

        // Calculate distance to target and subtract 10km exit distance
        float distanceToTarget = targetPosition.magnitude;
        float exitDistance = 10000f; // Always 10km
        warpDistance = Mathf.Max(0f, distanceToTarget - exitDistance);

        totalWarpTime = Mathf.Min(maxWarpTime, warpDistance / warpSpeed);

        // INSTANT WARP FOR DEBUGGING
        Debug.Log($"Initiating INSTANT warp to coordinates - Distance: {SpaceCoordinates.FormatDistance(warpDistance)}");

        // Clear warp state immediately
        isAligning = false;
        isWarping = false;
        velocity = Vector3.zero;

        Debug.Log("INSTANT coordinate warp completed!");
    }

    /// <summary>
    /// Warps to a specific entity
    /// </summary>
    public void WarpToEntity(Entity targetEntity)
    {
        if (!canWarp || targetEntity == null) return;

        var destination = new WarpDestination(targetEntity);
        InitiateWarpToDestination(destination);
    }

    /// <summary>
    /// Sets the maximum speed
    /// </summary>
    public void SetMaxSpeed(float newMaxSpeed)
    {
        maxSpeed = newMaxSpeed;
    }

    /// <summary>
    /// Gets current speed in m/s
    /// </summary>
    public float GetCurrentSpeed()
    {
        if (isWarping)
            return warpSpeed;
        else
            return velocity.magnitude;
    }

    /// <summary>
    /// Gets current speed as a formatted string
    /// </summary>
    public string GetSpeedDisplayString()
    {
        if (isAligning)
        {
            return "ALIGNING...";
        }
        else if (isWarping)
        {
            return $"WARP: {SpaceCoordinates.FormatSpeed(warpSpeed)}";
        }
        else
        {
            return SpaceCoordinates.FormatSpeed(velocity.magnitude);
        }
    }

    /// <summary>
    /// Gets the current warp target information
    /// </summary>
    public string GetWarpTargetInfo()
    {
        if (warpTargetEntity != null)
        {
            float distance = warpTargetEntity.transform.position.magnitude;
            return $"{warpTargetEntity.name} ({SpaceCoordinates.FormatDistance(distance)}) - Landing at 10km";
        }
        else if (isWarping || isAligning)
        {
            float distance = Vector3.Distance(transform.position, warpTarget);
            return $"Coordinates ({SpaceCoordinates.FormatDistance(distance)}) - Landing at 10km";
        }
        return "None";
    }

    /// <summary>
    /// Gets the number of available warp destinations
    /// </summary>
    public int GetAvailableDestinationCount()
    {
        return availableDestinations.Count;
    }

    /// <summary>
    /// Gets all available warp destinations
    /// </summary>
    public List<WarpDestination> GetAvailableDestinations()
    {
        return new List<WarpDestination>(availableDestinations);
    }
    
    void OnGUI()
    {
        DrawShipStatus();
        DrawWarpMenu();
        DrawControls();
    }

    /// <summary>
    /// Draws ship status information
    /// </summary>
    void DrawShipStatus()
    {
        GUIStyle statusStyle = new GUIStyle(GUI.skin.label);
        statusStyle.fontSize = 12;
        statusStyle.normal.textColor = Color.white;

        float yPos = Screen.height - 120;

        // Ship status
        GUI.Label(new Rect(10, yPos, 300, 20), $"Speed: {GetSpeedDisplayString()}", statusStyle);
        yPos += 20;

        // Position in space
        Vector3 universalPos = shipEntity?.GetUniversalPosition() ?? transform.position;
        string positionStr = SpaceCoordinates.FormatDistance(universalPos.magnitude);
        GUI.Label(new Rect(10, yPos, 300, 20), $"Position: {positionStr} from origin", statusStyle);
        yPos += 20;

        // Grid information
        if (shipEntity != null)
        {
            GUI.Label(new Rect(10, yPos, 300, 20), $"Grid: {shipEntity.CurrentGrid?.GridId ?? -1}", statusStyle);
            yPos += 20;
        }

        // Warp status
        if (isAligning)
        {
            float alignProgress = (warpAlignTimer / warpAlignTime) * 100f;
            GUI.Label(new Rect(10, yPos, 300, 20), $"Aligning for warp: {alignProgress:F0}%", statusStyle);
        }
        else if (isWarping)
        {
            string targetName = warpTargetEntity?.name ?? "coordinates";
            GUI.Label(new Rect(10, yPos, 300, 20), $"Warping to: {targetName}", statusStyle);
        }
    }

    /// <summary>
    /// Draws the warp destination menu
    /// </summary>
    void DrawWarpMenu()
    {
        if (!showWarpMenu || !showWarpUI) return;

        // Menu background
        float menuWidth = 400f;
        float menuHeight = Mathf.Min(400f, availableDestinations.Count * 25f + 100f);
        float menuX = (Screen.width - menuWidth) / 2f;
        float menuY = (Screen.height - menuHeight) / 2f;

        GUI.Box(new Rect(menuX, menuY, menuWidth, menuHeight), "");

        // Menu title
        GUIStyle titleStyle = new GUIStyle(GUI.skin.label);
        titleStyle.fontSize = 16;
        titleStyle.fontStyle = FontStyle.Bold;
        titleStyle.alignment = TextAnchor.MiddleCenter;
        titleStyle.normal.textColor = Color.white;

        GUI.Label(new Rect(menuX, menuY + 10, menuWidth, 30), "WARP DESTINATIONS", titleStyle);

        // Instructions
        GUIStyle instructionStyle = new GUIStyle(GUI.skin.label);
        instructionStyle.fontSize = 10;
        instructionStyle.alignment = TextAnchor.MiddleCenter;
        instructionStyle.normal.textColor = Color.gray;

        GUI.Label(new Rect(menuX, menuY + 35, menuWidth, 20), "↑↓ Navigate | Enter: Warp | Esc: Cancel | Alt+Space: Nearest Station", instructionStyle);

        // Destination list
        GUIStyle itemStyle = new GUIStyle(GUI.skin.label);
        itemStyle.fontSize = 12;
        itemStyle.normal.textColor = Color.white;

        GUIStyle selectedStyle = new GUIStyle(itemStyle);
        selectedStyle.normal.textColor = Color.cyan;
        selectedStyle.fontStyle = FontStyle.Bold;

        float itemY = menuY + 60;
        for (int i = 0; i < availableDestinations.Count && i < 15; i++) // Limit to 15 items
        {
            var destination = availableDestinations[i];
            bool isSelected = i == selectedDestinationIndex;

            GUIStyle currentStyle = isSelected ? selectedStyle : itemStyle;

            string itemText = $"{destination.GetTypeIcon()} {destination.name} - {destination.GetDistanceString()}";
            if (!destination.isInRange)
            {
                itemText += " (Too close)";
                currentStyle.normal.textColor = Color.red;
            }

            GUI.Label(new Rect(menuX + 10, itemY, menuWidth - 20, 20), itemText, currentStyle);
            itemY += 22;
        }

        // Show if there are more destinations
        if (availableDestinations.Count > 15)
        {
            GUI.Label(new Rect(menuX + 10, itemY, menuWidth - 20, 20), $"... and {availableDestinations.Count - 15} more", instructionStyle);
        }
    }

    /// <summary>
    /// Draws control instructions
    /// </summary>
    void DrawControls()
    {
        GUIStyle controlStyle = new GUIStyle(GUI.skin.label);
        controlStyle.fontSize = 10;
        controlStyle.normal.textColor = Color.gray;

        float yPos = 10;

        GUI.Label(new Rect(10, yPos, 300, 16), "Ship Controls:", controlStyle);
        yPos += 16;
        GUI.Label(new Rect(10, yPos, 300, 16), "WASD/Arrows - Move/Rotate", controlStyle);
        yPos += 16;
        GUI.Label(new Rect(10, yPos, 300, 16), "Space - Warp Menu", controlStyle);
        yPos += 16;
        GUI.Label(new Rect(10, yPos, 300, 16), "Alt+Space - Warp to Nearest Station", controlStyle);
        yPos += 16;
        GUI.Label(new Rect(10, yPos, 300, 16), "X - Brake", controlStyle);
        yPos += 16;
        GUI.Label(new Rect(10, yPos, 300, 16), "Esc - Cancel Warp", controlStyle);
    }
    
    void OnDrawGizmosSelected()
    {
        // Draw velocity vector
        if (!isWarping && !isAligning)
        {
            Gizmos.color = Color.green;
            Gizmos.DrawRay(transform.position, velocity.normalized * 100f);
        }

        // Draw warp target if warping or aligning
        if (isWarping || isAligning)
        {
            Gizmos.color = isAligning ? Color.yellow : Color.cyan;
            Gizmos.DrawWireSphere(warpTarget, CalculateWarpExitDistance());
            Gizmos.DrawLine(transform.position, warpTarget);

            // Draw alignment progress
            if (isAligning)
            {
                Vector3 directionToTarget = (warpTarget - transform.position).normalized;
                Gizmos.color = Color.yellow;
                Gizmos.DrawRay(transform.position, directionToTarget * 500f);
            }
        }

        // Draw available warp destinations
        if (showWarpMenu)
        {
            foreach (var destination in availableDestinations)
            {
                if (destination.targetEntity != null)
                {
                    Gizmos.color = destination.isInRange ? Color.blue : Color.red;
                    Gizmos.DrawWireCube(destination.targetEntity.transform.position, Vector3.one * 100f);
                }
            }
        }
    }

    // Properties
    public bool IsWarping => isWarping;
    public bool IsAligning => isAligning;
    public bool ShowWarpMenu => showWarpMenu;
    public Vector3 Velocity => velocity;
    public float MaxSpeed => maxSpeed;
    public float ThrustForce => thrustForce;
    public Entity WarpTargetEntity => warpTargetEntity;
    public float WarpAlignProgress => isAligning ? (warpAlignTimer / warpAlignTime) : 0f;

    /// <summary>
    /// Gets the current distance to the warp target (for debugging)
    /// </summary>
    public string GetCurrentTargetDistance()
    {
        if (warpTargetEntity != null && SpaceCoordinateManager.Instance != null)
        {
            float distance = SpaceCoordinateManager.Instance.GetDistanceBetweenEntities(shipEntity, warpTargetEntity);
            return SpaceCoordinates.FormatDistance(distance);
        }
        else if (warpTargetEntity != null)
        {
            // Fallback
            float distance = Vector3.Distance(transform.position, warpTargetEntity.transform.position);
            return SpaceCoordinates.FormatDistance(distance);
        }
        return "No target";
    }
}
