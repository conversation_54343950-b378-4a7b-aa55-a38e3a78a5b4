using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// Debug visualization tool for the space grid system
/// Provides visual feedback and debugging information for grids and entities
/// </summary>
public class GridDebugger : MonoBehaviour
{
    [Header("Debug Settings")]
    [SerializeField] private bool showGrids = true;
    [SerializeField] private bool showEntityConnections = true;
    [SerializeField] private bool showGridInfo = true;
    [SerializeField] private bool showEntityInfo = false;
    [SerializeField] private bool showPerformanceInfo = true;
    
    [Header("Visual Settings")]
    [SerializeField] private float gridLineWidth = 2f;
    [SerializeField] private float connectionLineWidth = 1f;
    [SerializeField] private bool useDepthTest = false;
    
    [Header("Info Display")]
    [SerializeField] private bool showOnScreenInfo = true;
    [SerializeField] private int maxInfoLines = 20;
    [SerializeField] private float infoUpdateInterval = 0.5f;
    
    // Debug state
    private float lastInfoUpdate;
    private List<string> debugInfo = new List<string>();
    private Dictionary<int, GridDebugInfo> gridDebugData = new Dictionary<int, GridDebugInfo>();
    
    // Performance tracking
    private int frameCount;
    private float deltaTime;
    private float fps;
    
    private struct GridDebugInfo
    {
        public Vector3 center;
        public float radius;
        public int entityCount;
        public bool isPlayerGrid;
        public Color color;
    }
    
    void Update()
    {
        UpdatePerformanceInfo();
        
        if (Time.time - lastInfoUpdate > infoUpdateInterval)
        {
            UpdateDebugInfo();
            lastInfoUpdate = Time.time;
        }
        
        HandleDebugInput();
    }
    
    /// <summary>
    /// Updates performance tracking information
    /// </summary>
    void UpdatePerformanceInfo()
    {
        frameCount++;
        deltaTime += (Time.unscaledDeltaTime - deltaTime) * 0.1f;
        fps = 1.0f / deltaTime;
    }
    
    /// <summary>
    /// Updates debug information from the grid system
    /// </summary>
    void UpdateDebugInfo()
    {
        debugInfo.Clear();
        gridDebugData.Clear();
        
        if (SpaceGridSystem.Instance == null)
        {
            debugInfo.Add("SpaceGridSystem not found!");
            return;
        }
        
        var gridSystem = SpaceGridSystem.Instance;
        var activeGrids = gridSystem.ActiveGrids;
        
        // Grid system info
        debugInfo.Add($"=== SPACE GRID SYSTEM ===");
        debugInfo.Add($"Active Grids: {activeGrids.Count}");
        debugInfo.Add($"Player Position: {gridSystem.UniversalPlayerPosition}");
        debugInfo.Add($"Player Grid: {gridSystem.PlayerGrid?.GridId ?? -1}");
        debugInfo.Add("");
        
        // Individual grid info
        foreach (var grid in activeGrids.Values)
        {
            var entities = grid.GetEntities();
            
            GridDebugInfo debugData = new GridDebugInfo
            {
                center = grid.Center,
                radius = grid.Radius,
                entityCount = entities.Count,
                isPlayerGrid = grid.IsPlayerGrid,
                color = grid.IsPlayerGrid ? SpaceConstants.PLAYER_GRID_COLOR : SpaceConstants.GRID_COLOR
            };
            
            gridDebugData[grid.GridId] = debugData;
            
            if (showGridInfo && debugInfo.Count < maxInfoLines)
            {
                debugInfo.Add($"Grid {grid.GridId}: {entities.Count} entities, R={grid.Radius:F1}km");
                
                if (showEntityInfo)
                {
                    foreach (var entity in entities)
                    {
                        if (entity != null && debugInfo.Count < maxInfoLines)
                        {
                            float distance = Vector3.Distance(entity.transform.position, Vector3.zero);
                            debugInfo.Add($"  - {entity.name}: {SpaceCoordinates.FormatDistance(distance)}");
                        }
                    }
                }
            }
        }
        
        // Performance info
        if (showPerformanceInfo)
        {
            debugInfo.Add("");
            debugInfo.Add($"=== PERFORMANCE ===");
            debugInfo.Add($"FPS: {fps:F1}");
            debugInfo.Add($"Frame Time: {deltaTime * 1000:F1}ms");
            debugInfo.Add($"Total Entities: {GetTotalEntityCount()}");
        }
    }
    
    /// <summary>
    /// Handles debug input controls
    /// </summary>
    void HandleDebugInput()
    {
        // Toggle debug displays
        if (Input.GetKeyDown(KeyCode.F1))
        {
            showGrids = !showGrids;
            Debug.Log($"Grid visualization: {(showGrids ? "ON" : "OFF")}");
        }
        
        if (Input.GetKeyDown(KeyCode.F2))
        {
            showEntityConnections = !showEntityConnections;
            Debug.Log($"Entity connections: {(showEntityConnections ? "ON" : "OFF")}");
        }
        
        if (Input.GetKeyDown(KeyCode.F3))
        {
            showOnScreenInfo = !showOnScreenInfo;
            Debug.Log($"On-screen info: {(showOnScreenInfo ? "ON" : "OFF")}");
        }
        
        if (Input.GetKeyDown(KeyCode.F4))
        {
            showEntityInfo = !showEntityInfo;
            Debug.Log($"Entity info: {(showEntityInfo ? "ON" : "OFF")}");
        }
        
        // Log detailed grid info
        if (Input.GetKeyDown(KeyCode.F5))
        {
            LogDetailedGridInfo();
        }
    }
    
    /// <summary>
    /// Gets the total number of entities across all grids
    /// </summary>
    int GetTotalEntityCount()
    {
        int total = 0;
        foreach (var debugData in gridDebugData.Values)
        {
            total += debugData.entityCount;
        }
        return total;
    }
    
    /// <summary>
    /// Logs detailed information about all grids
    /// </summary>
    void LogDetailedGridInfo()
    {
        if (SpaceGridSystem.Instance == null) return;
        
        Debug.Log("=== DETAILED GRID INFORMATION ===");
        
        foreach (var grid in SpaceGridSystem.Instance.ActiveGrids.Values)
        {
            Debug.Log($"Grid {grid.GridId}:");
            Debug.Log($"  Center: {grid.Center}");
            Debug.Log($"  Radius: {grid.Radius}km");
            Debug.Log($"  Entities: {grid.EntityCount}");
            Debug.Log($"  Is Player Grid: {grid.IsPlayerGrid}");
            
            var entities = grid.GetEntities();
            foreach (var entity in entities)
            {
                if (entity != null)
                {
                    Vector3 universalPos = entity.GetUniversalPosition();
                    Debug.Log($"    - {entity.name} ({entity.Type}): {universalPos}");
                }
            }
        }
    }
    
    void OnDrawGizmos()
    {
        if (!showGrids && !showEntityConnections) return;
        
        DrawGridGizmos();
        DrawEntityConnections();
    }
    
    /// <summary>
    /// Draws grid visualization gizmos
    /// </summary>
    void DrawGridGizmos()
    {
        if (!showGrids) return;
        
        foreach (var debugData in gridDebugData.Values)
        {
            Gizmos.color = debugData.color;
            
            // Draw grid sphere
            Gizmos.DrawWireSphere(debugData.center, debugData.radius * SpaceConstants.UNITY_UNITS_PER_KM);
            
            // Draw grid center
            Gizmos.DrawWireCube(debugData.center, Vector3.one * 100f);
            
            // Draw grid info
            if (showGridInfo)
            {
                Vector3 labelPos = debugData.center + Vector3.up * debugData.radius * SpaceConstants.UNITY_UNITS_PER_KM * 1.1f;
                
                #if UNITY_EDITOR
                UnityEditor.Handles.Label(labelPos, $"Grid\n{debugData.entityCount} entities\nR: {debugData.radius:F1}km");
                #endif
            }
        }
    }
    
    /// <summary>
    /// Draws entity connection lines
    /// </summary>
    void DrawEntityConnections()
    {
        if (!showEntityConnections || SpaceGridSystem.Instance == null) return;
        
        foreach (var grid in SpaceGridSystem.Instance.ActiveGrids.Values)
        {
            Gizmos.color = SpaceConstants.ENTITY_CONNECTION_COLOR;
            
            var entities = grid.GetEntities();
            foreach (var entity in entities)
            {
                if (entity != null)
                {
                    // Draw line from grid center to entity
                    Gizmos.DrawLine(grid.Center, entity.transform.position);
                    
                    // Draw entity radius
                    float entityRadiusInUnityUnits = entity.RadiusKm * SpaceConstants.UNITY_UNITS_PER_KM;
                    Gizmos.DrawWireSphere(entity.transform.position, entityRadiusInUnityUnits);
                }
            }
        }
    }
    
    void OnGUI()
    {
        if (!showOnScreenInfo) return;
        
        DrawDebugInfo();
        DrawDebugControls();
    }
    
    /// <summary>
    /// Draws debug information on screen
    /// </summary>
    void DrawDebugInfo()
    {
        GUIStyle style = new GUIStyle(GUI.skin.label);
        style.fontSize = 12;
        style.normal.textColor = Color.white;
        
        float yOffset = 10f;
        float lineHeight = 16f;
        
        foreach (string info in debugInfo)
        {
            if (yOffset > Screen.height - 100f) break; // Don't draw off screen
            
            GUI.Label(new Rect(10f, yOffset, 400f, lineHeight), info, style);
            yOffset += lineHeight;
        }
    }
    
    /// <summary>
    /// Draws debug control instructions
    /// </summary>
    void DrawDebugControls()
    {
        GUIStyle style = new GUIStyle(GUI.skin.label);
        style.fontSize = 10;
        style.normal.textColor = Color.gray;
        
        float yStart = Screen.height - 120f;
        
        GUI.Label(new Rect(10f, yStart, 300f, 16f), "Debug Controls:", style);
        GUI.Label(new Rect(10f, yStart + 16f, 300f, 16f), "F1 - Toggle Grid Visualization", style);
        GUI.Label(new Rect(10f, yStart + 32f, 300f, 16f), "F2 - Toggle Entity Connections", style);
        GUI.Label(new Rect(10f, yStart + 48f, 300f, 16f), "F3 - Toggle On-Screen Info", style);
        GUI.Label(new Rect(10f, yStart + 64f, 300f, 16f), "F4 - Toggle Entity Details", style);
        GUI.Label(new Rect(10f, yStart + 80f, 300f, 16f), "F5 - Log Detailed Grid Info", style);
    }
    
    /// <summary>
    /// Creates a debug visualizer for the grid system
    /// </summary>
    [ContextMenu("Create Grid Debugger")]
    public static void CreateGridDebugger()
    {
        GameObject debuggerGO = new GameObject("GridDebugger");
        debuggerGO.AddComponent<GridDebugger>();
        
        Debug.Log("Grid Debugger created. Use F1-F5 keys to toggle debug features.");
    }
}
