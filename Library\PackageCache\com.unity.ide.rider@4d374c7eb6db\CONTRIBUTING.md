# Contributing

## All contributions are subject to the [Unity Contribution Agreement(UCA)](https://unity3d.com/legal/licenses/Unity_Contribution_Agreement)
By making a pull request, you are confirming agreement to the terms and conditions of the UCA, including that your Contributions are your original creation and that you have complete right and authority to make your Contributions.

## Once you have a change ready following these ground rules. Simply make a pull request

## How to contribute
- The main working branch is `next/master-3.0`. All pull requests should target this branch when trying to land.
- Once all changes needed for a version release lands in `next/master-3.0`, a release branch can be made. The version release branch should be named `release/3.0.x`, where `x` is a version bump from previous version. This branch must target `release/3.0`. Also, it needs to contain the version bump in necessary files and updated changelog.
- Once the version release branch lands in `release/3.0`, the new package version is ready to release.