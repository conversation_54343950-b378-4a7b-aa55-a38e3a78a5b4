{"ErrorExceptions": [{"ValidationTest": "API Validation", "ExceptionMessage": "Breaking changes require a new major version.", "PackageVersion": "2.5.7"}, {"ValidationTest": "API Validation", "ExceptionMessage": "Assembly \"Unity.PerformanceTesting\" no longer exists or is no longer included in build. This change requires a new major version.", "PackageVersion": "2.5.7"}, {"ValidationTest": "API Validation", "ExceptionMessage": "Additions require a new minor or major version.", "PackageVersion": "2.5.7"}], "WarningExceptions": []}