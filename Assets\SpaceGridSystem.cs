using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// EVE Online-inspired space grid system that handles large-scale coordinates and scaling
/// </summary>
public class SpaceGridSystem : MonoBehaviour
{
    [Header("Grid Configuration")]
    [SerializeField] private float baseGridRadius = 150f; // 300km diameter (150km radius)
    [SerializeField] private float maxGridRadius = 500f; // 1000km diameter max
    [SerializeField] private float solarSystemRadius = 18f; // 18 AU in scaled units
    
    [Header("Scaling Configuration")]
    [SerializeField] private float maxRenderDistance = 50000f; // 50km max render distance
    [SerializeField] private float defaultZoomDistance = 1000f; // 1km default zoom
    [SerializeField] private float maxZoomDistance = 200000f; // 200km max zoom out
    [SerializeField] private float auToUnityScale = 1000000f; // 1 AU = 1,000,000 Unity units
    
    [Header("Player Reference")]
    [SerializeField] private Transform playerTransform;
    
    // Grid management
    private Dictionary<int, SpaceGrid> activeGrids = new Dictionary<int, SpaceGrid>();
    private SpaceGrid playerGrid;
    private int nextGridId = 0;
    
    // Coordinate system
    private Vector3 universalPlayerPosition = Vector3.zero; // Player's position in universal coordinates
    private Vector3 lastPlayerPosition = Vector3.zero;
    
    public static SpaceGridSystem Instance { get; private set; }
    
    void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        if (playerTransform == null)
        {
            Debug.LogError("SpaceGridSystem: Player transform not assigned!");
            return;
        }
        
        // Create initial player grid
        CreatePlayerGrid();
    }
    
    void Update()
    {
        if (playerTransform == null) return;
        
        UpdatePlayerPosition();
        CheckGridMerging();
        UpdateGridPositions();
    }
    
    /// <summary>
    /// Creates the initial grid centered on the player
    /// </summary>
    void CreatePlayerGrid()
    {
        playerGrid = new SpaceGrid(nextGridId++, Vector3.zero, baseGridRadius);
        activeGrids.Add(playerGrid.GridId, playerGrid);
        
        // Add player to the grid
        Entity playerEntity = playerTransform.GetComponent<Entity>();
        if (playerEntity != null)
        {
            playerGrid.AddEntity(playerEntity);
        }
        
        // Keep player at Unity world origin
        playerTransform.position = Vector3.zero;
        lastPlayerPosition = Vector3.zero;
    }
    
    /// <summary>
    /// Updates the player's universal position based on movement
    /// </summary>
    void UpdatePlayerPosition()
    {
        Vector3 deltaMovement = playerTransform.position - lastPlayerPosition;
        universalPlayerPosition += deltaMovement;
        lastPlayerPosition = playerTransform.position;
        
        // Keep player centered at origin
        if (playerTransform.position.magnitude > 1f)
        {
            Vector3 offset = playerTransform.position;
            playerTransform.position = Vector3.zero;
            
            // Move all other entities by the inverse offset
            MoveAllEntitiesExceptPlayer(-offset);
            
            lastPlayerPosition = Vector3.zero;
        }
    }
    
    /// <summary>
    /// Moves all entities except the player by the given offset
    /// </summary>
    void MoveAllEntitiesExceptPlayer(Vector3 offset)
    {
        foreach (var grid in activeGrids.Values)
        {
            foreach (var entity in grid.GetEntities())
            {
                if (entity.transform != playerTransform)
                {
                    entity.transform.position += offset;
                }
            }
        }
    }
    
    /// <summary>
    /// Checks if grids should merge when entities come close
    /// </summary>
    void CheckGridMerging()
    {
        List<SpaceGrid> gridsToMerge = new List<SpaceGrid>();
        
        foreach (var grid1 in activeGrids.Values)
        {
            foreach (var grid2 in activeGrids.Values)
            {
                if (grid1.GridId >= grid2.GridId) continue;
                
                float distance = Vector3.Distance(grid1.Center, grid2.Center);
                float combinedRadius = grid1.Radius + grid2.Radius;
                
                // Check if grids overlap
                if (distance < combinedRadius)
                {
                    // Calculate new merged grid radius
                    float newRadius = Mathf.Min(distance / 2f + combinedRadius / 2f, maxGridRadius);
                    
                    if (newRadius <= maxGridRadius)
                    {
                        MergeGrids(grid1, grid2, newRadius);
                        return; // Only merge one pair per frame
                    }
                }
            }
        }
    }
    
    /// <summary>
    /// Merges two grids into a larger one
    /// </summary>
    void MergeGrids(SpaceGrid grid1, SpaceGrid grid2, float newRadius)
    {
        Vector3 newCenter = (grid1.Center + grid2.Center) / 2f;
        SpaceGrid mergedGrid = new SpaceGrid(nextGridId++, newCenter, newRadius);
        
        // Transfer all entities to the new grid
        foreach (var entity in grid1.GetEntities())
        {
            mergedGrid.AddEntity(entity);
        }
        foreach (var entity in grid2.GetEntities())
        {
            mergedGrid.AddEntity(entity);
        }
        
        // Update player grid reference if needed
        if (grid1 == playerGrid || grid2 == playerGrid)
        {
            playerGrid = mergedGrid;
        }
        
        // Remove old grids
        activeGrids.Remove(grid1.GridId);
        activeGrids.Remove(grid2.GridId);
        activeGrids.Add(mergedGrid.GridId, mergedGrid);
        
        Debug.Log($"Merged grids {grid1.GridId} and {grid2.GridId} into grid {mergedGrid.GridId} with radius {newRadius}km");
    }
    
    /// <summary>
    /// Updates all grid positions relative to the player
    /// </summary>
    void UpdateGridPositions()
    {
        // This is where we would implement grid repositioning logic
        // For now, we keep the player grid centered
        if (playerGrid != null)
        {
            playerGrid.Center = Vector3.zero;
        }
    }
    
    /// <summary>
    /// Converts universal coordinates to local Unity coordinates
    /// </summary>
    public Vector3 UniversalToLocal(Vector3 universalPosition)
    {
        return universalPosition - universalPlayerPosition;
    }
    
    /// <summary>
    /// Converts local Unity coordinates to universal coordinates
    /// </summary>
    public Vector3 LocalToUniversal(Vector3 localPosition)
    {
        return localPosition + universalPlayerPosition;
    }
    
    /// <summary>
    /// Gets the appropriate scale factor for rendering at a given distance
    /// </summary>
    public float GetScaleFactorForDistance(float distance)
    {
        if (distance <= maxRenderDistance)
            return 1f;
        
        // Scale down objects beyond render distance
        return maxRenderDistance / distance;
    }
    
    /// <summary>
    /// Creates a new grid for an entity that's outside existing grids
    /// </summary>
    public SpaceGrid CreateGridForEntity(Entity entity, Vector3 position)
    {
        SpaceGrid newGrid = new SpaceGrid(nextGridId++, position, baseGridRadius);
        newGrid.AddEntity(entity);
        activeGrids.Add(newGrid.GridId, newGrid);
        
        Debug.Log($"Created new grid {newGrid.GridId} at position {position}");
        return newGrid;
    }
    
    /// <summary>
    /// Gets the grid containing a specific position
    /// </summary>
    public SpaceGrid GetGridAtPosition(Vector3 position)
    {
        foreach (var grid in activeGrids.Values)
        {
            if (Vector3.Distance(grid.Center, position) <= grid.Radius)
            {
                return grid;
            }
        }
        return null;
    }
    
    // Public getters
    public Vector3 UniversalPlayerPosition => universalPlayerPosition;
    public SpaceGrid PlayerGrid => playerGrid;
    public Dictionary<int, SpaceGrid> ActiveGrids => activeGrids;
    public float MaxRenderDistance => maxRenderDistance;
    public float SolarSystemRadius => solarSystemRadius;
}
