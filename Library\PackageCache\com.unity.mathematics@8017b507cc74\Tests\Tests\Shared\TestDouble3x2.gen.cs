//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. To update the generation of this file, modify and re-run Unity.Mathematics.CodeGen.
// </auto-generated>
//------------------------------------------------------------------------------
using NUnit.Framework;
using static Unity.Mathematics.math;
using Burst.Compiler.IL.Tests;

namespace Unity.Mathematics.Tests
{
    [TestFixture]
    public class TestDouble3x2
    {
        [TestCompiler]
        public static void double3x2_zero()
        {
            TestUtils.AreEqual(0.0, double3x2.zero.c0.x);
            TestUtils.AreEqual(0.0, double3x2.zero.c0.y);
            TestUtils.AreEqual(0.0, double3x2.zero.c0.z);
            TestUtils.AreEqual(0.0, double3x2.zero.c1.x);
            TestUtils.AreEqual(0.0, double3x2.zero.c1.y);
            TestUtils.AreEqual(0.0, double3x2.zero.c1.z);
        }

        [TestCompiler]
        public static void double3x2_operator_equal_wide_wide()
        {
            double3x2 a0 = double3x2(492.15758275061728, -495.20632027797694, 227.45765195947968, -147.37405950733182, -222.68201909897942, 64.093720704360749);
            double3x2 b0 = double3x2(192.56880888369346, -235.61102472786376, -254.04311740307281, -412.62472052715009, 471.90480945627428, -6.4727852374654162);
            bool3x2 r0 = bool3x2(false, false, false, false, false, false);
            TestUtils.AreEqual(r0, a0 == b0);

            double3x2 a1 = double3x2(-23.890404473939157, -16.8197190839889, 163.23210890741655, -165.27101071424363, 470.87767980568003, -423.94255967808078);
            double3x2 b1 = double3x2(-339.10237447316865, 488.1875700839737, -379.5965842584132, -308.41700258311675, -82.333374300195544, -102.92108087563935);
            bool3x2 r1 = bool3x2(false, false, false, false, false, false);
            TestUtils.AreEqual(r1, a1 == b1);

            double3x2 a2 = double3x2(109.63436918595539, 462.69031283943468, -335.38147727371262, 357.23446934168896, 1.5455777652308598, -347.38824741327585);
            double3x2 b2 = double3x2(226.51573835430463, -356.90132896830391, -362.91277544708589, -427.89843746083716, 466.65013978753711, -102.79904680270658);
            bool3x2 r2 = bool3x2(false, false, false, false, false, false);
            TestUtils.AreEqual(r2, a2 == b2);

            double3x2 a3 = double3x2(-114.47217302884542, 435.84865804940864, 194.23808607563285, 138.76554710174241, -467.34914205379278, 370.43337767684523);
            double3x2 b3 = double3x2(-43.355954428834821, 85.045664111639212, -91.127054972167628, 422.19208856215334, -477.43130873024057, 1.8770024785198984);
            bool3x2 r3 = bool3x2(false, false, false, false, false, false);
            TestUtils.AreEqual(r3, a3 == b3);
        }

        [TestCompiler]
        public static void double3x2_operator_equal_wide_scalar()
        {
            double3x2 a0 = double3x2(-303.2300766926399, 451.52631327674089, -253.65587413201848, -105.20363502632995, -500.6910920090466, -426.19248338518315);
            double b0 = (123.5445759871717);
            bool3x2 r0 = bool3x2(false, false, false, false, false, false);
            TestUtils.AreEqual(r0, a0 == b0);

            double3x2 a1 = double3x2(159.87609656149334, -57.477391031327386, -182.04973968400139, 406.51375861024189, 370.88599866017978, -172.03530629539642);
            double b1 = (-59.558379439431405);
            bool3x2 r1 = bool3x2(false, false, false, false, false, false);
            TestUtils.AreEqual(r1, a1 == b1);

            double3x2 a2 = double3x2(455.40001198993991, 363.93823044557973, -27.150561106927, -325.97606507221985, -290.35904254129116, 180.19686635779067);
            double b2 = (-11.338988547836891);
            bool3x2 r2 = bool3x2(false, false, false, false, false, false);
            TestUtils.AreEqual(r2, a2 == b2);

            double3x2 a3 = double3x2(-374.12832015293105, -126.54608899287234, -197.2617896521752, -227.15933357326281, -479.8991937487848, -439.77767750237962);
            double b3 = (-439.35894295170851);
            bool3x2 r3 = bool3x2(false, false, false, false, false, false);
            TestUtils.AreEqual(r3, a3 == b3);
        }

        [TestCompiler]
        public static void double3x2_operator_equal_scalar_wide()
        {
            double a0 = (-253.39728534100453);
            double3x2 b0 = double3x2(19.952187785856495, -185.79199346610903, 407.8136052600172, -87.2766969610363, -206.27469382354741, 160.503138855334);
            bool3x2 r0 = bool3x2(false, false, false, false, false, false);
            TestUtils.AreEqual(r0, a0 == b0);

            double a1 = (-274.77081478516141);
            double3x2 b1 = double3x2(-2.6315281403397535, 448.35453602688131, -410.03524251004461, 247.32901465489022, 355.53915350303942, -298.06671180299793);
            bool3x2 r1 = bool3x2(false, false, false, false, false, false);
            TestUtils.AreEqual(r1, a1 == b1);

            double a2 = (414.10151429385951);
            double3x2 b2 = double3x2(-481.30262707234482, 196.55074438664633, 34.60100008668428, 113.76156645350227, -386.45337861890596, -124.49174672201821);
            bool3x2 r2 = bool3x2(false, false, false, false, false, false);
            TestUtils.AreEqual(r2, a2 == b2);

            double a3 = (243.8866447153905);
            double3x2 b3 = double3x2(-492.6181826501238, 145.424413033493, 421.55070968230757, -95.409988209330493, 336.80928746648567, 209.58380589707929);
            bool3x2 r3 = bool3x2(false, false, false, false, false, false);
            TestUtils.AreEqual(r3, a3 == b3);
        }

        [TestCompiler]
        public static void double3x2_operator_not_equal_wide_wide()
        {
            double3x2 a0 = double3x2(430.8425316432689, 104.69001798736394, 225.80243478799355, -310.57017841496048, -418.61945815506363, 304.12820281839379);
            double3x2 b0 = double3x2(210.02470622305975, -55.203330304102678, -269.92533672504373, -234.54673372700194, 25.917412054686565, -63.726991444699024);
            bool3x2 r0 = bool3x2(true, true, true, true, true, true);
            TestUtils.AreEqual(r0, a0 != b0);

            double3x2 a1 = double3x2(-509.32682561749908, -160.53807719076895, -203.30197606016975, -505.76325368590807, 162.17220623892365, 1.1561973100324394);
            double3x2 b1 = double3x2(-484.55371092471933, -425.333599050219, -53.274394775402925, 328.1944192984115, 15.963139303011417, 461.71412417931208);
            bool3x2 r1 = bool3x2(true, true, true, true, true, true);
            TestUtils.AreEqual(r1, a1 != b1);

            double3x2 a2 = double3x2(65.662074358045174, 102.78780250567377, 172.93008120960098, 26.621009123800832, 235.12595259171258, 128.54198533321824);
            double3x2 b2 = double3x2(-113.36304455313973, -240.07297264787974, 495.11916970420589, 203.5583661550462, 340.49345103860526, -241.90719448863865);
            bool3x2 r2 = bool3x2(true, true, true, true, true, true);
            TestUtils.AreEqual(r2, a2 != b2);

            double3x2 a3 = double3x2(-354.99697630246959, 334.35948220564023, -495.83200692377613, 468.30740163675853, 458.37094733601941, 299.93733300824522);
            double3x2 b3 = double3x2(459.56982896270688, 213.0737384357833, -384.7828506831, -255.07233846144396, 477.66343115161328, -248.03662621604121);
            bool3x2 r3 = bool3x2(true, true, true, true, true, true);
            TestUtils.AreEqual(r3, a3 != b3);
        }

        [TestCompiler]
        public static void double3x2_operator_not_equal_wide_scalar()
        {
            double3x2 a0 = double3x2(-16.914588697680529, 168.83411486858233, -462.71352145760949, 130.30776959765137, 214.50161443208424, -440.26328178879959);
            double b0 = (-145.37277109239847);
            bool3x2 r0 = bool3x2(true, true, true, true, true, true);
            TestUtils.AreEqual(r0, a0 != b0);

            double3x2 a1 = double3x2(-197.12796053529155, -386.61117595555783, -281.02101362916687, -270.26885593601912, -403.96372313236992, -269.80570877241234);
            double b1 = (-169.09985860115842);
            bool3x2 r1 = bool3x2(true, true, true, true, true, true);
            TestUtils.AreEqual(r1, a1 != b1);

            double3x2 a2 = double3x2(299.65422763473089, -432.75573917513515, -457.36312100727258, -13.519590622521719, 273.87305773136814, 185.042454567292);
            double b2 = (-71.750904831919286);
            bool3x2 r2 = bool3x2(true, true, true, true, true, true);
            TestUtils.AreEqual(r2, a2 != b2);

            double3x2 a3 = double3x2(-482.53069351731364, 511.73495578753523, 230.50753628020527, 100.27476768394683, 129.68240863163135, 321.17879048044733);
            double b3 = (116.39514427836764);
            bool3x2 r3 = bool3x2(true, true, true, true, true, true);
            TestUtils.AreEqual(r3, a3 != b3);
        }

        [TestCompiler]
        public static void double3x2_operator_not_equal_scalar_wide()
        {
            double a0 = (275.79582823244664);
            double3x2 b0 = double3x2(-57.196896341255353, -382.4325279586169, 97.820359990848374, -161.46364529499022, -458.39563367254829, -499.61786364932448);
            bool3x2 r0 = bool3x2(true, true, true, true, true, true);
            TestUtils.AreEqual(r0, a0 != b0);

            double a1 = (327.92217818271467);
            double3x2 b1 = double3x2(367.57121699283425, 59.7863667289663, -209.58068118318016, -62.580453186566217, -479.97497604786184, -49.494519495169868);
            bool3x2 r1 = bool3x2(true, true, true, true, true, true);
            TestUtils.AreEqual(r1, a1 != b1);

            double a2 = (-114.68521338081229);
            double3x2 b2 = double3x2(109.93924599044919, -176.28482755286842, -347.48529903380449, 85.540928165214609, -356.65954868712441, -104.24357490625397);
            bool3x2 r2 = bool3x2(true, true, true, true, true, true);
            TestUtils.AreEqual(r2, a2 != b2);

            double a3 = (-133.54918605347592);
            double3x2 b3 = double3x2(243.53971135036079, 13.141311890045813, -379.98594754747393, -41.281226892620907, 87.911684792447659, -339.07727996403224);
            bool3x2 r3 = bool3x2(true, true, true, true, true, true);
            TestUtils.AreEqual(r3, a3 != b3);
        }

        [TestCompiler]
        public static void double3x2_operator_less_wide_wide()
        {
            double3x2 a0 = double3x2(196.84256825076534, 336.40979997087732, 251.96372115424072, 257.65591466503963, 430.04588647840819, -62.419644146421774);
            double3x2 b0 = double3x2(-465.34502313348696, -256.15239751346053, -314.814018634527, 364.56673662949663, 100.21050290959442, 182.56098636545289);
            bool3x2 r0 = bool3x2(false, false, false, true, false, true);
            TestUtils.AreEqual(r0, a0 < b0);

            double3x2 a1 = double3x2(8.8392293494376872, -333.81671563434259, 164.67880662003472, -350.94487516532877, 3.84143662631584, 125.40972024081725);
            double3x2 b1 = double3x2(3.116978885194726, -259.43047893207074, -437.33490749696966, -456.0437321402336, -394.2559718537405, 401.91369099259077);
            bool3x2 r1 = bool3x2(false, true, false, false, false, true);
            TestUtils.AreEqual(r1, a1 < b1);

            double3x2 a2 = double3x2(-111.12994127680076, 70.005523475820951, 448.19828173527412, -419.98711200244122, -258.30166757213965, -34.832201735504043);
            double3x2 b2 = double3x2(313.43916454605721, 121.28668194696616, -28.012290729215522, -282.96589697663012, 330.06440631023816, 124.09937077579059);
            bool3x2 r2 = bool3x2(true, true, false, true, true, true);
            TestUtils.AreEqual(r2, a2 < b2);

            double3x2 a3 = double3x2(-69.859397682295821, 67.767227442826766, -139.77729207825723, 385.43464130229995, 133.707390609061, 506.18837117878184);
            double3x2 b3 = double3x2(-183.69031700104955, 373.0607623406969, 109.75094013556418, -203.57134232463841, 45.6486556742567, -360.95226280808089);
            bool3x2 r3 = bool3x2(false, true, true, false, false, false);
            TestUtils.AreEqual(r3, a3 < b3);
        }

        [TestCompiler]
        public static void double3x2_operator_less_wide_scalar()
        {
            double3x2 a0 = double3x2(-132.05731708000292, -192.46500477216438, -66.834607870706634, -379.01750081545561, -360.28242199508588, 20.927834282129879);
            double b0 = (-156.01021845452965);
            bool3x2 r0 = bool3x2(false, true, false, true, true, false);
            TestUtils.AreEqual(r0, a0 < b0);

            double3x2 a1 = double3x2(-158.24074537970159, -20.452607402788772, 225.29148517609178, 307.48418607725023, 274.01523292903562, 373.54965584983563);
            double b1 = (437.34587522845061);
            bool3x2 r1 = bool3x2(true, true, true, true, true, true);
            TestUtils.AreEqual(r1, a1 < b1);

            double3x2 a2 = double3x2(398.52368301829495, -58.010895994496934, 109.67008810381878, -108.853174498702, -44.971252223929014, 140.42607147080173);
            double b2 = (105.0301654827922);
            bool3x2 r2 = bool3x2(false, true, false, true, true, false);
            TestUtils.AreEqual(r2, a2 < b2);

            double3x2 a3 = double3x2(-500.08827638071415, -197.50074610370245, -7.27149987559369, -432.99049898283113, 62.158315449095426, -72.254720959931035);
            double b3 = (172.10334857371788);
            bool3x2 r3 = bool3x2(true, true, true, true, true, true);
            TestUtils.AreEqual(r3, a3 < b3);
        }

        [TestCompiler]
        public static void double3x2_operator_less_scalar_wide()
        {
            double a0 = (-423.117411095238);
            double3x2 b0 = double3x2(385.09483617595151, -123.93348532725753, 86.376572887588509, 133.44217378154497, 161.45794947513286, 229.75426660746064);
            bool3x2 r0 = bool3x2(true, true, true, true, true, true);
            TestUtils.AreEqual(r0, a0 < b0);

            double a1 = (222.57159934871436);
            double3x2 b1 = double3x2(315.53116360098647, -447.20351883731945, 271.83385790131695, -393.60531324595462, 317.48689737798964, -164.6051085761772);
            bool3x2 r1 = bool3x2(true, false, true, false, true, false);
            TestUtils.AreEqual(r1, a1 < b1);

            double a2 = (-282.87605370342544);
            double3x2 b2 = double3x2(296.97953071118309, -254.40115582868509, 365.61562054493265, -441.98425671178114, -131.42866021554391, 442.62897631275882);
            bool3x2 r2 = bool3x2(true, true, true, false, true, true);
            TestUtils.AreEqual(r2, a2 < b2);

            double a3 = (-29.792842163607872);
            double3x2 b3 = double3x2(-138.37379533535511, 9.2169721169476588, -226.7305482489665, 171.02944310523083, 376.62522595777421, -462.58872697436658);
            bool3x2 r3 = bool3x2(false, true, false, true, true, false);
            TestUtils.AreEqual(r3, a3 < b3);
        }

        [TestCompiler]
        public static void double3x2_operator_greater_wide_wide()
        {
            double3x2 a0 = double3x2(483.50140141113729, 310.81563415695712, 106.9661896726891, 295.73526038589671, 116.95757179938141, -478.29977653841479);
            double3x2 b0 = double3x2(-471.39802454011425, -371.98528617060992, 36.900723236101044, -316.76360407320954, 19.683055648432628, 207.3091381561519);
            bool3x2 r0 = bool3x2(true, true, true, true, true, false);
            TestUtils.AreEqual(r0, a0 > b0);

            double3x2 a1 = double3x2(-14.897393471979228, -33.817441717636484, -24.740548383789417, 319.78262701620474, -120.15856581561201, -289.00857962714906);
            double3x2 b1 = double3x2(362.79748861994483, 324.95341816775192, 340.94807140014507, 25.986035120666997, -114.2111352021858, 240.80346428640348);
            bool3x2 r1 = bool3x2(false, false, false, true, false, false);
            TestUtils.AreEqual(r1, a1 > b1);

            double3x2 a2 = double3x2(455.85146662958505, 144.70691139283917, 63.931990891663304, -285.68304099034663, -502.0907201720824, -337.19446412529538);
            double3x2 b2 = double3x2(273.42244757033063, 325.51576224226312, 27.341068995809678, 64.479532510265472, 200.94836983501375, 100.12266998184964);
            bool3x2 r2 = bool3x2(true, false, true, false, false, false);
            TestUtils.AreEqual(r2, a2 > b2);

            double3x2 a3 = double3x2(474.31734274063137, -507.14510679018923, -133.56559735795742, -443.10913654934109, -464.34137056038776, -68.361549647693323);
            double3x2 b3 = double3x2(-79.00710896356361, -315.137945560337, -122.98542815213347, -163.77920229908972, -492.56600617457462, -90.797273439726439);
            bool3x2 r3 = bool3x2(true, false, false, false, true, true);
            TestUtils.AreEqual(r3, a3 > b3);
        }

        [TestCompiler]
        public static void double3x2_operator_greater_wide_scalar()
        {
            double3x2 a0 = double3x2(64.317918092160426, -397.70346445483318, 431.87690826499693, 85.702980796668157, 246.26305233978803, 197.49155602114809);
            double b0 = (305.85991992888034);
            bool3x2 r0 = bool3x2(false, false, true, false, false, false);
            TestUtils.AreEqual(r0, a0 > b0);

            double3x2 a1 = double3x2(286.1994608781298, -405.78459210218148, 171.56538661362856, -241.80727326209063, 333.57817498481745, 370.27919524269146);
            double b1 = (280.81334818564972);
            bool3x2 r1 = bool3x2(true, false, false, false, true, true);
            TestUtils.AreEqual(r1, a1 > b1);

            double3x2 a2 = double3x2(-413.70138116073861, -353.03129522550444, 396.64532608382649, 467.22205541432936, -240.0134228393498, 502.91505193287276);
            double b2 = (-356.5923551789449);
            bool3x2 r2 = bool3x2(false, true, true, true, true, true);
            TestUtils.AreEqual(r2, a2 > b2);

            double3x2 a3 = double3x2(315.46759024051369, 281.23064554912537, 428.79219909608, 245.15306460352292, -279.17542494422543, -453.86309668694764);
            double b3 = (-259.28970134411458);
            bool3x2 r3 = bool3x2(true, true, true, true, false, false);
            TestUtils.AreEqual(r3, a3 > b3);
        }

        [TestCompiler]
        public static void double3x2_operator_greater_scalar_wide()
        {
            double a0 = (-282.67049635698572);
            double3x2 b0 = double3x2(358.09997360692353, -72.5964134077525, -232.16380106292843, -60.706723956720282, 75.156642710397364, 150.88350040786133);
            bool3x2 r0 = bool3x2(false, false, false, false, false, false);
            TestUtils.AreEqual(r0, a0 > b0);

            double a1 = (339.53917924479538);
            double3x2 b1 = double3x2(-498.19602965665797, 459.74610326241054, -227.96872316485678, 335.86213485145106, 76.178844248959308, 296.85993899817572);
            bool3x2 r1 = bool3x2(true, false, true, true, true, true);
            TestUtils.AreEqual(r1, a1 > b1);

            double a2 = (177.49000390688423);
            double3x2 b2 = double3x2(-281.20120657663847, 244.72285162877427, 137.32857257562159, -385.33824724021287, 443.16345879210326, -353.56254141105455);
            bool3x2 r2 = bool3x2(true, false, true, true, false, true);
            TestUtils.AreEqual(r2, a2 > b2);

            double a3 = (26.040673983302327);
            double3x2 b3 = double3x2(-331.7939499969566, -43.691963454565041, 20.949428806523542, -211.17984423934473, 227.42171894173214, -84.7797711290325);
            bool3x2 r3 = bool3x2(true, true, true, true, false, true);
            TestUtils.AreEqual(r3, a3 > b3);
        }

        [TestCompiler]
        public static void double3x2_operator_less_equal_wide_wide()
        {
            double3x2 a0 = double3x2(-438.52313753521219, 210.48942837980087, 4.8773329280677444, -137.29793817237857, 156.09410174009111, -363.92412035722475);
            double3x2 b0 = double3x2(-474.8141498392514, 304.3710555063426, 234.8241737982371, -390.48543209139513, -297.17535295019638, -326.29239121372461);
            bool3x2 r0 = bool3x2(false, true, true, false, false, true);
            TestUtils.AreEqual(r0, a0 <= b0);

            double3x2 a1 = double3x2(-97.948485181642923, 437.29539009430232, 458.53029153241323, -294.06474675520542, 23.622613679441884, -34.284056441059363);
            double3x2 b1 = double3x2(107.2538764976216, -413.13107342884462, 67.094432623635271, 470.07522724106684, -84.499104777583455, 392.78422683886447);
            bool3x2 r1 = bool3x2(true, false, false, true, false, true);
            TestUtils.AreEqual(r1, a1 <= b1);

            double3x2 a2 = double3x2(149.736484835733, -418.8866781754823, -197.50252899783783, -88.2055118494693, -376.71814292330208, 341.62712899857536);
            double3x2 b2 = double3x2(-263.53175485484849, 369.30090039284005, -333.32529298091555, 238.41347443238533, 486.24259279959028, 279.65021408705513);
            bool3x2 r2 = bool3x2(false, true, false, true, true, false);
            TestUtils.AreEqual(r2, a2 <= b2);

            double3x2 a3 = double3x2(-83.309179106405566, -107.49073295830317, 319.46688833807912, 205.35738501574724, 345.56372968552807, 395.32190746596177);
            double3x2 b3 = double3x2(236.05201803709008, 132.75898248178839, 66.294708998079727, 183.00210699020056, 200.13055071613314, 339.043800750302);
            bool3x2 r3 = bool3x2(true, true, false, false, false, false);
            TestUtils.AreEqual(r3, a3 <= b3);
        }

        [TestCompiler]
        public static void double3x2_operator_less_equal_wide_scalar()
        {
            double3x2 a0 = double3x2(193.4958237118534, 168.91555197952107, -313.9930695565385, 81.826965131716292, 18.503590830836288, -0.35819602029312136);
            double b0 = (443.85054299042122);
            bool3x2 r0 = bool3x2(true, true, true, true, true, true);
            TestUtils.AreEqual(r0, a0 <= b0);

            double3x2 a1 = double3x2(241.36115776810846, -1.3577692515020203, -268.89945591096739, 398.9919504593089, -471.253072242836, -264.93778264938749);
            double b1 = (-463.81641242644582);
            bool3x2 r1 = bool3x2(false, false, false, false, true, false);
            TestUtils.AreEqual(r1, a1 <= b1);

            double3x2 a2 = double3x2(82.258299150624453, 424.7040156911612, 426.48223157715154, 56.319978501796754, -196.28791126808522, 31.901173844887467);
            double b2 = (11.246050124636895);
            bool3x2 r2 = bool3x2(false, false, false, false, true, false);
            TestUtils.AreEqual(r2, a2 <= b2);

            double3x2 a3 = double3x2(-152.2575724833913, -37.104814785115821, -47.144214413661587, 333.6230348710078, -274.80387438219225, 358.67627804292192);
            double b3 = (-437.92645975478297);
            bool3x2 r3 = bool3x2(false, false, false, false, false, false);
            TestUtils.AreEqual(r3, a3 <= b3);
        }

        [TestCompiler]
        public static void double3x2_operator_less_equal_scalar_wide()
        {
            double a0 = (393.60626644343427);
            double3x2 b0 = double3x2(-75.688363825757222, -44.2638714519627, 125.86491566797019, 191.96488174794467, 13.543054825413492, -197.0519259893577);
            bool3x2 r0 = bool3x2(false, false, false, false, false, false);
            TestUtils.AreEqual(r0, a0 <= b0);

            double a1 = (-423.945100743298);
            double3x2 b1 = double3x2(-330.04861680141119, 420.16553779140372, 105.54730777887039, 174.82126363311954, 296.71757831085358, -469.70041845259277);
            bool3x2 r1 = bool3x2(true, true, true, true, true, false);
            TestUtils.AreEqual(r1, a1 <= b1);

            double a2 = (123.26718979853536);
            double3x2 b2 = double3x2(112.9969695140594, 495.14339493920249, -488.65789364681478, 388.53941148730894, -493.24077080806751, 16.451064832718657);
            bool3x2 r2 = bool3x2(false, true, false, true, false, false);
            TestUtils.AreEqual(r2, a2 <= b2);

            double a3 = (-387.6516336815672);
            double3x2 b3 = double3x2(-229.1773127192526, -373.01533930982248, -391.142134610164, 90.994149488859875, -178.39613517485378, -69.621067317957568);
            bool3x2 r3 = bool3x2(true, true, false, true, true, true);
            TestUtils.AreEqual(r3, a3 <= b3);
        }

        [TestCompiler]
        public static void double3x2_operator_greater_equal_wide_wide()
        {
            double3x2 a0 = double3x2(-507.92858409692, 504.49748181947393, -385.43449205226938, -262.32340944107784, -37.550928848586466, -111.59527759980193);
            double3x2 b0 = double3x2(-81.346509732933043, 297.66615047010885, 171.06540616371922, -431.03805538222105, -6.859075311040101, 319.72570362674333);
            bool3x2 r0 = bool3x2(false, true, false, true, false, false);
            TestUtils.AreEqual(r0, a0 >= b0);

            double3x2 a1 = double3x2(-463.70202157632542, 387.44885772627265, 456.96878573716094, -211.01015506079892, 182.41135391146474, -53.596053863687473);
            double3x2 b1 = double3x2(254.079170106947, 396.5724000393285, 178.83927615864172, -447.06336304501787, 288.49268569075161, 474.88929460704765);
            bool3x2 r1 = bool3x2(false, false, true, true, false, false);
            TestUtils.AreEqual(r1, a1 >= b1);

            double3x2 a2 = double3x2(-309.57021608463032, -136.02249127999994, 280.73629082401112, -96.9958942388165, -174.05950673579213, 88.9019382413951);
            double3x2 b2 = double3x2(-321.75022831640683, -395.97722048125104, -158.69246037243516, 391.48869318118727, -368.10924141859135, 89.1238043723273);
            bool3x2 r2 = bool3x2(true, true, true, false, true, false);
            TestUtils.AreEqual(r2, a2 >= b2);

            double3x2 a3 = double3x2(43.816040774721728, -446.07842585354967, 16.645595796706857, 409.83252043734888, -191.32987245886113, 222.99782548798146);
            double3x2 b3 = double3x2(-510.27932214656812, -486.92979525352354, -81.215552606254619, 274.21882046117389, -212.88155494112596, 288.99530591117);
            bool3x2 r3 = bool3x2(true, true, true, true, true, false);
            TestUtils.AreEqual(r3, a3 >= b3);
        }

        [TestCompiler]
        public static void double3x2_operator_greater_equal_wide_scalar()
        {
            double3x2 a0 = double3x2(465.15218732559686, -424.8860745024337, -209.22109685150025, 58.779852656079356, -302.26910533675414, 140.12558252183976);
            double b0 = (-5.5998842742293391);
            bool3x2 r0 = bool3x2(true, false, false, true, false, true);
            TestUtils.AreEqual(r0, a0 >= b0);

            double3x2 a1 = double3x2(16.353385694489475, 393.27804846003562, -315.70155086913218, 441.0115565923096, -509.78156757251435, -36.994287269652943);
            double b1 = (-344.55997316192838);
            bool3x2 r1 = bool3x2(true, true, true, true, false, true);
            TestUtils.AreEqual(r1, a1 >= b1);

            double3x2 a2 = double3x2(494.82028865014217, -466.12009046325466, -123.8137477020797, 215.65121779947128, 104.99569730879534, 314.34603014325069);
            double b2 = (-164.97393830352183);
            bool3x2 r2 = bool3x2(true, false, true, true, true, true);
            TestUtils.AreEqual(r2, a2 >= b2);

            double3x2 a3 = double3x2(190.51609882643265, -23.836435567511444, 143.04935962662535, -264.91997945724052, -169.70222457205051, 329.70751610850334);
            double b3 = (-83.111429014760745);
            bool3x2 r3 = bool3x2(true, true, true, false, false, true);
            TestUtils.AreEqual(r3, a3 >= b3);
        }

        [TestCompiler]
        public static void double3x2_operator_greater_equal_scalar_wide()
        {
            double a0 = (374.82703393270594);
            double3x2 b0 = double3x2(-1.609757185731894, 338.61524049314448, -116.18140392945213, -332.15732375353451, -355.9793509710484, -468.90144107719021);
            bool3x2 r0 = bool3x2(true, true, true, true, true, true);
            TestUtils.AreEqual(r0, a0 >= b0);

            double a1 = (38.579884785497484);
            double3x2 b1 = double3x2(-332.34754697063357, 2.8901150240051265, 467.77776477661814, 121.40638762405445, -305.02337303060267, -58.428812292604164);
            bool3x2 r1 = bool3x2(true, true, false, false, true, true);
            TestUtils.AreEqual(r1, a1 >= b1);

            double a2 = (-226.51955209789776);
            double3x2 b2 = double3x2(-47.020994446715804, 305.3026770582901, -427.40123315686418, 92.263649745035764, -497.17853736187266, -408.62564225151465);
            bool3x2 r2 = bool3x2(false, false, true, false, true, true);
            TestUtils.AreEqual(r2, a2 >= b2);

            double a3 = (-455.23049113491106);
            double3x2 b3 = double3x2(396.42608637196292, -469.29488561548987, -485.7540130493017, -182.34619268325446, -291.54536284671417, 278.740809331993);
            bool3x2 r3 = bool3x2(false, true, true, false, false, false);
            TestUtils.AreEqual(r3, a3 >= b3);
        }

        [TestCompiler]
        public static void double3x2_operator_add_wide_wide()
        {
            double3x2 a0 = double3x2(506.12905263627374, -501.77980803967444, 420.08479638587903, -186.03206476291274, -9.3123953385801883, 328.51179686585056);
            double3x2 b0 = double3x2(-28.757987751047096, -337.135153689019, -340.676816860529, 152.31202633320913, 423.66745420157326, 90.374096674087468);
            double3x2 r0 = double3x2(477.37106488522664, -838.91496172869347, 79.407979525350015, -33.720038429703607, 414.35505886299308, 418.885893539938);
            TestUtils.AreEqual(r0, a0 + b0);

            double3x2 a1 = double3x2(424.34407659263536, 87.791079800478656, 462.41368148402012, -46.178705952213477, 401.17006296718966, -454.12414643453627);
            double3x2 b1 = double3x2(376.18866246574964, 1.7671887882831925, -120.18586045139745, -279.62936628965167, -344.66710273580026, 242.8391956029642);
            double3x2 r1 = double3x2(800.532739058385, 89.558268588761848, 342.22782103262267, -325.80807224186515, 56.502960231389409, -211.28495083157208);
            TestUtils.AreEqual(r1, a1 + b1);

            double3x2 a2 = double3x2(69.195687564646732, -177.95734485329939, 299.60415544156183, 340.7048587001417, 219.91602740991675, -321.90838232725321);
            double3x2 b2 = double3x2(418.5930504363929, -23.312797318823982, -95.099945827899489, 147.92812568877275, 331.03287926830023, -82.502564230236487);
            double3x2 r2 = double3x2(487.78873800103963, -201.27014217212337, 204.50420961366234, 488.63298438891445, 550.948906678217, -404.41094655748969);
            TestUtils.AreEqual(r2, a2 + b2);

            double3x2 a3 = double3x2(286.35534037573041, -333.41949311523672, -118.93216973120911, 68.607509406566351, 23.190902005504313, -205.57787547147734);
            double3x2 b3 = double3x2(279.44956291813844, 342.6227215931857, -300.35853185335105, -209.69408736456842, 446.55942150883345, -351.98918955027557);
            double3x2 r3 = double3x2(565.80490329386885, 9.2032284779489828, -419.29070158456017, -141.08657795800207, 469.75032351433777, -557.56706502175291);
            TestUtils.AreEqual(r3, a3 + b3);
        }

        [TestCompiler]
        public static void double3x2_operator_add_wide_scalar()
        {
            double3x2 a0 = double3x2(-194.51420387742769, 338.54838696985894, 246.97140252169754, 100.51093797595752, -45.724677822424439, -478.11131094308166);
            double b0 = (124.121678171736);
            double3x2 r0 = double3x2(-70.3925257056917, 462.67006514159493, 371.09308069343354, 224.63261614769351, 78.397000349311554, -353.98963277134567);
            TestUtils.AreEqual(r0, a0 + b0);

            double3x2 a1 = double3x2(30.916145577522116, -242.1187475855084, 82.50134495762245, 6.7993848355483806, -484.69981287638649, -188.26501068298938);
            double b1 = (60.37435224483454);
            double3x2 r1 = double3x2(91.290497822356656, -181.74439534067386, 142.875697202457, 67.173737080382921, -424.32546063155195, -127.89065843815484);
            TestUtils.AreEqual(r1, a1 + b1);

            double3x2 a2 = double3x2(-213.52673087526426, 189.25996669999324, 198.53359684652355, 187.53610023648298, -424.92567582844089, 302.10236730338181);
            double b2 = (-267.78430688929944);
            double3x2 r2 = double3x2(-481.31103776456371, -78.5243401893062, -69.250710042775893, -80.248206652816464, -692.70998271774033, 34.318060414082368);
            TestUtils.AreEqual(r2, a2 + b2);

            double3x2 a3 = double3x2(300.39907970111778, -200.16134295247559, 31.37822701007974, 362.52213518811493, -423.98885961248953, 432.41331907380993);
            double b3 = (124.02158909850823);
            double3x2 r3 = double3x2(424.420668799626, -76.139753853967363, 155.39981610858797, 486.54372428662316, -299.9672705139813, 556.43490817231816);
            TestUtils.AreEqual(r3, a3 + b3);
        }

        [TestCompiler]
        public static void double3x2_operator_add_scalar_wide()
        {
            double a0 = (-340.35468284243473);
            double3x2 b0 = double3x2(511.36225652665007, -146.21663791789518, -106.21042661844308, -363.45024960276214, 199.08958325120136, -27.108407271610758);
            double3x2 r0 = double3x2(171.00757368421534, -486.57132076032991, -446.56510946087781, -703.80493244519687, -141.26509959123337, -367.46309011404549);
            TestUtils.AreEqual(r0, a0 + b0);

            double a1 = (419.84900041103788);
            double3x2 b1 = double3x2(284.95503748811552, -164.92418129971446, -249.19032561461921, 150.92817718858282, 298.17509784278229, -457.15341803857751);
            double3x2 r1 = double3x2(704.8040378991534, 254.92481911132342, 170.65867479641867, 570.7771775996207, 718.02409825382017, -37.304417627539635);
            TestUtils.AreEqual(r1, a1 + b1);

            double a2 = (424.71807094324288);
            double3x2 b2 = double3x2(-301.85750283946163, 230.28885208363124, -423.58759351428023, -67.060037882560891, 68.7241366229598, -164.02241833695325);
            double3x2 r2 = double3x2(122.86056810378125, 655.00692302687412, 1.1304774289626494, 357.658033060682, 493.44220756620268, 260.69565260628963);
            TestUtils.AreEqual(r2, a2 + b2);

            double a3 = (318.93515339444161);
            double3x2 b3 = double3x2(7.8045504129512437, 187.69836029210046, -3.6569664495331153, -446.0830535581722, -209.28724227160552, -38.212905186327589);
            double3x2 r3 = double3x2(326.73970380739286, 506.63351368654207, 315.2781869449085, -127.14790016373058, 109.64791112283609, 280.722248208114);
            TestUtils.AreEqual(r3, a3 + b3);
        }

        [TestCompiler]
        public static void double3x2_operator_sub_wide_wide()
        {
            double3x2 a0 = double3x2(160.4922617229131, 11.223957305412682, 359.20010607279846, -498.22830485656311, -355.25362913462038, -94.534852787170053);
            double3x2 b0 = double3x2(115.46876078260539, -130.98230630298252, 241.54083716196044, 9.9870860623135513, 419.89512582304656, 59.124466208333388);
            double3x2 r0 = double3x2(45.023500940307713, 142.2062636083952, 117.65926891083802, -508.21539091887666, -775.14875495766694, -153.65931899550344);
            TestUtils.AreEqual(r0, a0 - b0);

            double3x2 a1 = double3x2(-410.46404786150163, -401.38464398001537, 317.70681944382693, 447.0604133303558, -489.07414482956477, -230.00838218909149);
            double3x2 b1 = double3x2(-402.38163847587145, -75.370143687059226, 320.97960796997859, -73.908757482612884, -31.444742455819949, -389.25194734579509);
            double3x2 r1 = double3x2(-8.0824093856301715, -326.01450029295614, -3.2727885261516576, 520.96917081296874, -457.62940237374482, 159.2435651567036);
            TestUtils.AreEqual(r1, a1 - b1);

            double3x2 a2 = double3x2(24.875419389864192, 366.61447136784648, -107.3741567634857, -219.0081404275299, 473.90756891384137, 259.63620793988753);
            double3x2 b2 = double3x2(-375.02884000122026, 259.18275821357167, 276.648654351313, -453.06919905779381, -272.57653225240136, -191.14805301984217);
            double3x2 r2 = double3x2(399.90425939108445, 107.4317131542748, -384.02281111479869, 234.06105863026391, 746.48410116624268, 450.78426095972969);
            TestUtils.AreEqual(r2, a2 - b2);

            double3x2 a3 = double3x2(-360.119631219711, 7.8096120393879573, 437.42847439154446, -59.1991718091067, 418.74433322378638, 183.14215072576985);
            double3x2 b3 = double3x2(87.136884968325944, 430.02477594373033, 343.65711538105143, 121.02942067060133, -354.1881703595576, 249.05200373802893);
            double3x2 r3 = double3x2(-447.25651618803693, -422.21516390434238, 93.771359010493029, -180.22859247970803, 772.932503583344, -65.909853012259077);
            TestUtils.AreEqual(r3, a3 - b3);
        }

        [TestCompiler]
        public static void double3x2_operator_sub_wide_scalar()
        {
            double3x2 a0 = double3x2(207.38960108877609, 248.45773684627272, -384.82393211164697, -205.34476122881506, -374.81156152058929, 191.64204820973896);
            double b0 = (-36.112476604111691);
            double3x2 r0 = double3x2(243.50207769288778, 284.57021345038441, -348.71145550753528, -169.23228462470337, -338.6990849164776, 227.75452481385065);
            TestUtils.AreEqual(r0, a0 - b0);

            double3x2 a1 = double3x2(18.856238135535364, 480.85798738936796, 16.338193185784917, -366.86545269883493, -35.523088233323335, 349.39776460705218);
            double b1 = (-44.96160151667965);
            double3x2 r1 = double3x2(63.817839652215014, 525.81958890604756, 61.299794702464567, -321.90385118215528, 9.4385132833563148, 394.35936612373183);
            TestUtils.AreEqual(r1, a1 - b1);

            double3x2 a2 = double3x2(439.07729336203886, 195.02405104181923, -384.84940952102158, 189.05188545447402, 55.602777745389744, -54.931482579061537);
            double b2 = (490.2222661870635);
            double3x2 r2 = double3x2(-51.144972825024638, -295.19821514524426, -875.07167570808508, -301.17038073258948, -434.61948844167375, -545.15374876612509);
            TestUtils.AreEqual(r2, a2 - b2);

            double3x2 a3 = double3x2(53.088051582261983, -273.80670917863335, 256.88723695319482, 297.17363156805447, 101.82901363346218, 136.60794765157993);
            double b3 = (316.80250730961677);
            double3x2 r3 = double3x2(-263.71445572735479, -590.60921648825013, -59.915270356421956, -19.628875741562297, -214.97349367615459, -180.19455965803684);
            TestUtils.AreEqual(r3, a3 - b3);
        }

        [TestCompiler]
        public static void double3x2_operator_sub_scalar_wide()
        {
            double a0 = (-86.008225719448262);
            double3x2 b0 = double3x2(466.42511413359318, 298.48694219183506, -300.95010652251085, 315.38003006362362, -381.09218543632522, -125.00837546447684);
            double3x2 r0 = double3x2(-552.43333985304139, -384.49516791128332, 214.94188080306259, -401.38825578307188, 295.08395971687696, 39.00014974502858);
            TestUtils.AreEqual(r0, a0 - b0);

            double a1 = (58.466194418476107);
            double3x2 b1 = double3x2(214.74609361158036, -257.54942739082009, 480.22459505508868, -443.35507723472784, 260.79503858312728, 29.681931747906788);
            double3x2 r1 = double3x2(-156.27989919310426, 316.01562180929619, -421.75840063661258, 501.82127165320395, -202.32884416465117, 28.784262670569319);
            TestUtils.AreEqual(r1, a1 - b1);

            double a2 = (139.85773164586055);
            double3x2 b2 = double3x2(-247.78996216868512, -248.4662297929014, 91.445112509394562, 86.384162704639266, 373.81828206303453, 260.41195428576873);
            double3x2 r2 = double3x2(387.64769381454568, 388.32396143876196, 48.412619136465992, 53.473568941221288, -233.96055041717398, -120.55422263990818);
            TestUtils.AreEqual(r2, a2 - b2);

            double a3 = (114.35393171867076);
            double3x2 b3 = double3x2(-464.40545318294573, -109.74146156652898, -311.67535057276268, 107.86401586787031, -258.7951592219971, 14.097560173877355);
            double3x2 r3 = double3x2(578.7593849016165, 224.09539328519975, 426.02928229143345, 6.4899158508004575, 373.14909094066786, 100.25637154479341);
            TestUtils.AreEqual(r3, a3 - b3);
        }

        [TestCompiler]
        public static void double3x2_operator_mul_wide_wide()
        {
            double3x2 a0 = double3x2(-482.71381710596097, -407.29348559272171, 137.70058995937029, 208.54113278563182, 194.296573967811, -484.24241684574747);
            double3x2 b0 = double3x2(-236.36788355389979, 260.72759139757954, -416.38629718142852, -364.49561541364324, -253.14750897751537, -369.20287220981106);
            double3x2 r0 = double3x2(114098.04331156026, -106192.64949051509, -57336.638772880389, -76012.328533757158, -49185.69370281692, 178783.69114527057);
            TestUtils.AreEqual(r0, a0 * b0);

            double3x2 a1 = double3x2(183.98730739578014, -241.33547770294149, 45.868758938214114, 363.32610266438041, -328.11893692990714, -471.02307413100408);
            double3x2 b1 = double3x2(193.54791531038836, 169.08491976982214, 201.96966442930034, 249.45608317547294, -308.19319810913555, -385.57964843585137);
            double3x2 r1 = double3x2(35610.359790024842, -40806.189885013562, 9264.0978505395742, 90633.9064860661, 101124.02453259782, 181616.91132860651);
            TestUtils.AreEqual(r1, a1 * b1);

            double3x2 a2 = double3x2(-262.68257415605831, -379.26274674910246, -374.09058182970182, 481.44738720424812, 104.62807397946165, 412.93539948618752);
            double3x2 b2 = double3x2(-183.27959522198864, 22.275629292370581, -265.52144229855458, -95.677454277722859, 133.25437146669924, 148.31146080247663);
            double3x2 r2 = double3x2(48144.355863192381, -8448.3163509892329, 99329.070837727879, -46063.660376363579, 13942.148235904471, 61243.052314850727);
            TestUtils.AreEqual(r2, a2 * b2);

            double3x2 a3 = double3x2(477.87724731763694, 20.377821216535722, 291.99596299417124, -138.48832399141429, -393.46498483860165, 9.36312318284206);
            double3x2 b3 = double3x2(249.284127113076, 500.00547503866505, -19.331578978957396, -36.691062705913112, 30.5238278054278, -401.36701054189678);
            double3x2 r3 = double3x2(119127.21246477668, 10189.022177626932, -5644.7430201585421, 5081.2837796057929, -12010.057444678736, -3758.048761232847);
            TestUtils.AreEqual(r3, a3 * b3);
        }

        [TestCompiler]
        public static void double3x2_operator_mul_wide_scalar()
        {
            double3x2 a0 = double3x2(-96.318821236639678, -277.14229239017811, -239.93690191951436, 509.53140544776409, 255.85810172551226, 215.73149667295229);
            double b0 = (-301.20720424373042);
            double3x2 r0 = double3x2(29011.922860739887, 83477.255068544036, 72270.723422079071, -153474.5301092997, -77066.303503849529, -64979.880980175592);
            TestUtils.AreEqual(r0, a0 * b0);

            double3x2 a1 = double3x2(-455.50827500573746, -338.29248658674419, 53.796284939067618, 243.75734459783757, 135.35469991311186, -207.35010275959507);
            double b1 = (-389.24327367788334);
            double3x2 r1 = double3x2(177303.53215059882, 131678.07493965575, -20939.842061390889, -94880.9067942902, -52685.906501867168, 80709.6327955903);
            TestUtils.AreEqual(r1, a1 * b1);

            double3x2 a2 = double3x2(-383.93960946795517, 42.676120539510634, 260.38388049806645, 176.86755927692525, 25.672123205695357, -290.50059689697838);
            double b2 = (-31.425238862366086);
            double3x2 r2 = double3x2(12065.393936254042, -1341.1072816732492, -8182.625640561525, -5558.1052972810685, -806.752603843068, 9129.05064714747);
            TestUtils.AreEqual(r2, a2 * b2);

            double3x2 a3 = double3x2(207.09101805793637, -208.4020064847553, 370.94506400215676, -341.59844247512444, 10.270311121954705, -176.88876565587185);
            double b3 = (-156.52330858843555);
            double3x2 r3 = double3x2(-32414.571325375655, 32619.7715714625, -58061.548722166561, 53468.118424862856, -1607.5430770409582, 27687.214852581492);
            TestUtils.AreEqual(r3, a3 * b3);
        }

        [TestCompiler]
        public static void double3x2_operator_mul_scalar_wide()
        {
            double a0 = (37.432166355397612);
            double3x2 b0 = double3x2(96.747546479454058, 492.18539427788244, -274.05458534604617, -452.87096926796761, 420.85330434369541, 102.18292694081686);
            double3x2 r0 = double3x2(3621.4702542954869, 18423.565556306661, -10258.456829132712, -16951.941459168724, 15753.450899411988, 3824.9283199300971);
            TestUtils.AreEqual(r0, a0 * b0);

            double a1 = (-114.94887762654054);
            double3x2 b1 = double3x2(-351.12003843445336, -464.66496799172131, 444.08484646495663, 447.10525605040846, 130.82935124767448, -321.41334191030512);
            double3x2 r1 = double3x2(40360.854330228191, 53412.716543020746, -51047.054672101345, -51394.247363921473, -15038.687086528622, 36946.1029067851);
            TestUtils.AreEqual(r1, a1 * b1);

            double a2 = (445.30131861441828);
            double3x2 b2 = double3x2(478.24357317306271, 358.57170622356784, -144.89011222910608, -438.89383741789209, -3.536441089369589, -471.80755470311624);
            double3x2 r2 = double3x2(212962.49375283587, 159672.45359917657, -64519.758029811986, -195440.00453392946, -1574.7818802984877, -210096.52624154196);
            TestUtils.AreEqual(r2, a2 * b2);

            double a3 = (-42.560401697904069);
            double3x2 b3 = double3x2(119.91104155402218, 271.9000023677479, 239.6840079946835, 487.44143389511919, -79.188288010278825, -112.92564468873928);
            double3x2 r3 = double3x2(-5103.4620965532513, -11572.173322432418, -10201.047660817379, -20745.703230778625, 3370.2853474867875, 4806.1607999475309);
            TestUtils.AreEqual(r3, a3 * b3);
        }

        [TestCompiler]
        public static void double3x2_operator_div_wide_wide()
        {
            double3x2 a0 = double3x2(-353.13144390337703, -102.79985456485292, 51.319128298814917, -191.87167868012176, 8.0418245829836223, -128.73764210973758);
            double3x2 b0 = double3x2(-178.73954805114283, -302.09628381491467, -199.40583739029518, 278.85077561012042, 502.33758782890516, -361.48483078623417);
            double3x2 r0 = double3x2(1.97567604793504, 0.34028837848212429, -0.25736021056579439, -0.68808013268139567, 0.016008805189634039, 0.35613566917796119);
            TestUtils.AreEqual(r0, a0 / b0);

            double3x2 a1 = double3x2(-136.05959779399427, -370.4710053738537, -237.69456326109105, -432.54687496300176, 200.26549181727012, 361.44157068871039);
            double3x2 b1 = double3x2(353.121059820578, -38.894930142394685, -75.764737402910725, -195.21784719974636, -405.03399224068687, -394.2300085473014);
            double3x2 r1 = double3x2(-0.3853058151307277, 9.5249176182488586, 3.1372716570909582, 2.2157137842034547, -0.49444119667433889, -0.9168291678773689);
            TestUtils.AreEqual(r1, a1 / b1);

            double3x2 a2 = double3x2(-416.22613234828509, -450.01919362042992, -273.49744594911925, -286.90817011841955, -314.25606241554772, 177.76210340194507);
            double3x2 b2 = double3x2(-375.82771342612227, -121.24548655433836, 447.623344391409, 338.28628007429018, -405.54420752336466, -431.16893526127978);
            double3x2 r2 = double3x2(1.1074918572499153, 3.7116366671409717, -0.61099906735420106, -0.84812239519560884, 0.77489964493560781, -0.41227947763496636);
            TestUtils.AreEqual(r2, a2 / b2);

            double3x2 a3 = double3x2(97.626988217992221, -68.107280047660367, -386.45074027890837, 263.69934690357161, -297.0270885420158, -501.77703046322659);
            double3x2 b3 = double3x2(296.20513095343722, 437.939790691221, 39.21061684527001, 331.2897075765253, -310.61955156485533, 207.26946959610541);
            double3x2 r3 = double3x2(0.32959249525403717, -0.15551745124635385, -9.855767936625206, 0.79597808465769837, 0.95624080018671487, -2.420892143165184);
            TestUtils.AreEqual(r3, a3 / b3);
        }

        [TestCompiler]
        public static void double3x2_operator_div_wide_scalar()
        {
            double3x2 a0 = double3x2(171.34242184988341, 0.10338377957384637, 57.888263967767443, -256.13074529177078, 95.6696842162263, -290.38690461329509);
            double b0 = (171.79682191265601);
            double3x2 r0 = double3x2(0.99735501473360411, 0.00060177934855167557, 0.33695771157628673, -1.4908933846400916, 0.55687691513214455, -1.6902926455818372);
            TestUtils.AreEqual(r0, a0 / b0);

            double3x2 a1 = double3x2(-127.44869118903239, 146.46688110496234, -499.84355687529012, 58.686315802245531, -453.20579859856787, -205.03382143985192);
            double b1 = (-79.7448890580539);
            double3x2 r1 = double3x2(1.5982051350808244, -1.8366930198916591, 6.2680325069034382, -0.73592573135968842, 5.6831955496061468, 2.5711217842511296);
            TestUtils.AreEqual(r1, a1 / b1);

            double3x2 a2 = double3x2(481.73814247629514, -293.46349753693841, -158.50557930697948, -289.5822156824089, 494.12860535743118, 203.58342680874443);
            double b2 = (464.47907159499778);
            double3x2 r2 = double3x2(1.0371579085835463, -0.631812099798597, -0.34125451285174013, -0.62345589584477534, 1.0638339498497067, 0.4383048435522599);
            TestUtils.AreEqual(r2, a2 / b2);

            double3x2 a3 = double3x2(180.97040160976837, 460.84470603468117, 490.95625924084163, -280.47805536933151, -320.24387112271222, 192.41448912043802);
            double b3 = (259.11918723728468);
            double3x2 r3 = double3x2(0.69840602519352346, 1.778504752767186, 1.8947120993832676, -1.082428740070444, -1.2358940861814818, 0.7425713671455646);
            TestUtils.AreEqual(r3, a3 / b3);
        }

        [TestCompiler]
        public static void double3x2_operator_div_scalar_wide()
        {
            double a0 = (-264.44250095283729);
            double3x2 b0 = double3x2(105.58908157497137, -142.34910137129441, -288.94890679463231, 39.644133824689334, -363.99138396046658, -149.71822006521666);
            double3x2 r0 = double3x2(-2.5044492954044237, 1.85770404172122, 0.915187753732487, -6.670406827961755, 0.72650758398599513, 1.7662679988958405);
            TestUtils.AreEqual(r0, a0 / b0);

            double a1 = (-395.72912306139671);
            double3x2 b1 = double3x2(258.71868693955184, -9.6662514254759344, 117.72553282497711, -331.38655797177296, -509.98602676297821, 427.8964666928614);
            double3x2 r1 = double3x2(-1.5295730190291843, 40.939254075104124, -3.3614553577746666, 1.1941616626921372, 0.77596071714591563, -0.92482447008716717);
            TestUtils.AreEqual(r1, a1 / b1);

            double a2 = (467.61712882836218);
            double3x2 b2 = double3x2(-407.12461943511136, 252.69070994699871, 444.59937664708093, -88.313306134340053, 199.95503411067421, -218.34692607556792);
            double3x2 r2 = double3x2(-1.1485847490067898, 1.8505513278523131, 1.0517718948570469, -5.2949793105586425, 2.33861143285614, -2.1416245111988621);
            TestUtils.AreEqual(r2, a2 / b2);

            double a3 = (-13.417186028052697);
            double3x2 b3 = double3x2(-296.13107575854804, 0.561349630617201, -289.29929865957206, 196.21833929615946, 334.73346845001606, -282.39273203648293);
            double3x2 r3 = double3x2(0.045308267609821762, -23.901656465508974, 0.04637821830270366, -0.068378858348207977, -0.04008319242823552, 0.04751250477055232);
            TestUtils.AreEqual(r3, a3 / b3);
        }

        [TestCompiler]
        public static void double3x2_operator_mod_wide_wide()
        {
            double3x2 a0 = double3x2(-388.81249422059045, 181.68118842955732, -167.07872470052854, 432.82015319951813, -258.43895995730486, -170.11079629236406);
            double3x2 b0 = double3x2(436.94417187056695, 58.940049437312382, -201.11623368091705, 279.2893537391393, -397.07975954426445, 377.89994758083481);
            double3x2 r0 = double3x2(-388.81249422059045, 4.8610401176201776, -167.07872470052854, 153.53079946037883, -258.43895995730486, -170.11079629236406);
            TestUtils.AreEqual(r0, a0 % b0);

            double3x2 a1 = double3x2(283.318293464984, 122.71651297561664, 335.27101413126616, -503.60851668920765, 191.02251848532933, 289.74269379756538);
            double3x2 b1 = double3x2(174.69386657266591, -228.17652736798698, -317.06019106370405, -417.48011107811709, -249.9759434433542, -397.57157177364991);
            double3x2 r1 = double3x2(108.62442689231807, 122.71651297561664, 18.210823067562103, -86.128405611090557, 191.02251848532933, 289.74269379756538);
            TestUtils.AreEqual(r1, a1 % b1);

            double3x2 a2 = double3x2(-124.03371745163281, 259.27395761165485, -274.35845030208975, -140.03080398404541, 324.5775689205982, -200.51308903494527);
            double3x2 b2 = double3x2(-358.74544947163452, -198.1592100589346, 208.73709378425826, -12.119406944196385, 25.27141596063575, -194.12068495253135);
            double3x2 r2 = double3x2(-124.03371745163281, 61.114747552720246, -65.621356517831487, -6.7173275978851734, 21.3205773929692, -6.3924040824139183);
            TestUtils.AreEqual(r2, a2 % b2);

            double3x2 a3 = double3x2(211.42317328761476, -51.272212767634642, -230.63392483006879, 99.989400671790122, 399.18986649028489, 24.903281461868119);
            double3x2 b3 = double3x2(-493.8717965995296, -312.3016990685378, -216.98060546488529, 413.57096047586344, -436.39440151508637, 3.4912750737235);
            double3x2 r3 = double3x2(211.42317328761476, -51.272212767634642, -13.653319365183506, 99.989400671790122, 399.18986649028489, 0.46435594580361794);
            TestUtils.AreEqual(r3, a3 % b3);
        }

        [TestCompiler]
        public static void double3x2_operator_mod_wide_scalar()
        {
            double3x2 a0 = double3x2(-244.49962889612635, -211.81931958525411, -145.92677576184587, -304.91822090042672, 155.47946436492703, -133.90778428591221);
            double b0 = (39.634963769295723);
            double3x2 r0 = double3x2(-6.6898462803520147, -13.644500738775491, -27.021884453958705, -27.473474515356656, 36.574573057039856, -15.002892978025045);
            TestUtils.AreEqual(r0, a0 % b0);

            double3x2 a1 = double3x2(281.30965412841624, 335.16613046041039, 101.70649032560482, 319.47152033423606, -285.40231646476423, -355.84685985923136);
            double b1 = (-226.53575311719243);
            double3x2 r1 = double3x2(54.773901011223813, 108.63037734321796, 101.70649032560482, 92.935767217043633, -58.8665633475718, -129.31110674203893);
            TestUtils.AreEqual(r1, a1 % b1);

            double3x2 a2 = double3x2(259.37800061860025, -284.34358109363518, -102.68343811048356, -172.14173921017988, 206.41684517935698, -416.71365447375626);
            double b2 = (-330.87193957477433);
            double3x2 r2 = double3x2(259.37800061860025, -284.34358109363518, -102.68343811048356, -172.14173921017988, 206.41684517935698, -85.841714898981934);
            TestUtils.AreEqual(r2, a2 % b2);

            double3x2 a3 = double3x2(-339.256669917729, 132.55290490600885, 226.94410215455298, -306.11827268550093, 115.43844633709568, 281.88292015804109);
            double b3 = (435.29751440291182);
            double3x2 r3 = double3x2(-339.256669917729, 132.55290490600885, 226.94410215455298, -306.11827268550093, 115.43844633709568, 281.88292015804109);
            TestUtils.AreEqual(r3, a3 % b3);
        }

        [TestCompiler]
        public static void double3x2_operator_mod_scalar_wide()
        {
            double a0 = (-66.945025236785909);
            double3x2 b0 = double3x2(-249.77609479137516, -396.07375664081133, 386.49204582091977, 168.93948109864232, -199.4182442163202, 261.7517141130528);
            double3x2 r0 = double3x2(-66.945025236785909, -66.945025236785909, -66.945025236785909, -66.945025236785909, -66.945025236785909, -66.945025236785909);
            TestUtils.AreEqual(r0, a0 % b0);

            double a1 = (16.127438791155555);
            double3x2 b1 = double3x2(257.66814744550186, -75.788451945310669, 170.95630439136005, -242.85828005655588, 425.94531913564788, 303.27240409668184);
            double3x2 r1 = double3x2(16.127438791155555, 16.127438791155555, 16.127438791155555, 16.127438791155555, 16.127438791155555, 16.127438791155555);
            TestUtils.AreEqual(r1, a1 % b1);

            double a2 = (3.033060790520608);
            double3x2 b2 = double3x2(-505.74352788633831, 461.95706126743789, 205.97275672013529, 270.04063642678807, -47.480711720642034, -150.254496405951);
            double3x2 r2 = double3x2(3.033060790520608, 3.033060790520608, 3.033060790520608, 3.033060790520608, 3.033060790520608, 3.033060790520608);
            TestUtils.AreEqual(r2, a2 % b2);

            double a3 = (149.49949009227544);
            double3x2 b3 = double3x2(-220.29804263836616, 31.118842377848409, 400.63568348467152, 6.2314283876826266, -39.050740021770252, -71.941097054603063);
            double3x2 r3 = double3x2(149.49949009227544, 25.0241205808818, 149.49949009227544, 6.1766371755750242, 32.347270026964679, 5.6172959830693117);
            TestUtils.AreEqual(r3, a3 % b3);
        }

        [TestCompiler]
        public static void double3x2_operator_plus()
        {
            double3x2 a0 = double3x2(-418.82956357432045, -405.79894823851015, -34.041791216489742, 236.99924456188421, -459.83910129025537, 210.8614223985287);
            double3x2 r0 = double3x2(-418.82956357432045, -405.79894823851015, -34.041791216489742, 236.99924456188421, -459.83910129025537, 210.8614223985287);
            TestUtils.AreEqual(r0, +a0);

            double3x2 a1 = double3x2(293.74197902052754, -386.059833944803, 4.9544198536101476, -418.64524932328857, 504.47483062393724, -170.74650843941907);
            double3x2 r1 = double3x2(293.74197902052754, -386.059833944803, 4.9544198536101476, -418.64524932328857, 504.47483062393724, -170.74650843941907);
            TestUtils.AreEqual(r1, +a1);

            double3x2 a2 = double3x2(439.55937572920664, 116.40075665172219, 421.40964742256779, -258.5960806620289, 447.86609122150867, 124.16434031546316);
            double3x2 r2 = double3x2(439.55937572920664, 116.40075665172219, 421.40964742256779, -258.5960806620289, 447.86609122150867, 124.16434031546316);
            TestUtils.AreEqual(r2, +a2);

            double3x2 a3 = double3x2(222.17254386757156, 239.04183947250328, 498.4495329793773, -139.382530515726, 279.07295549990283, 108.7758186370022);
            double3x2 r3 = double3x2(222.17254386757156, 239.04183947250328, 498.4495329793773, -139.382530515726, 279.07295549990283, 108.7758186370022);
            TestUtils.AreEqual(r3, +a3);
        }

        [TestCompiler]
        public static void double3x2_operator_neg()
        {
            double3x2 a0 = double3x2(148.46174890755753, -467.12267873581624, 132.04719954917539, 183.52262290917463, 473.7010145009034, -407.99109024926605);
            double3x2 r0 = double3x2(-148.46174890755753, 467.12267873581624, -132.04719954917539, -183.52262290917463, -473.7010145009034, 407.99109024926605);
            TestUtils.AreEqual(r0, -a0);

            double3x2 a1 = double3x2(-54.958759571872065, -299.09338893512887, -383.01406377508027, 407.70980305583669, 168.73550351370852, 466.44152829909763);
            double3x2 r1 = double3x2(54.958759571872065, 299.09338893512887, 383.01406377508027, -407.70980305583669, -168.73550351370852, -466.44152829909763);
            TestUtils.AreEqual(r1, -a1);

            double3x2 a2 = double3x2(171.90249474900895, -78.85761622286293, 318.69633522569029, -39.91539694737429, 140.34000284054321, 132.19563180403577);
            double3x2 r2 = double3x2(-171.90249474900895, 78.85761622286293, -318.69633522569029, 39.91539694737429, -140.34000284054321, -132.19563180403577);
            TestUtils.AreEqual(r2, -a2);

            double3x2 a3 = double3x2(-505.89525127126615, -237.05693375182193, -137.617827241131, -245.34998547534923, 422.52133222227974, -434.57134386271764);
            double3x2 r3 = double3x2(505.89525127126615, 237.05693375182193, 137.617827241131, 245.34998547534923, -422.52133222227974, 434.57134386271764);
            TestUtils.AreEqual(r3, -a3);
        }

        [TestCompiler]
        public static void double3x2_operator_prefix_inc()
        {
            double3x2 a0 = double3x2(-139.84208137348389, -56.743654039103376, -381.955324589254, 509.79634380237962, -222.89634452708827, 210.31986556310198);
            double3x2 r0 = double3x2(-138.84208137348389, -55.743654039103376, -380.955324589254, 510.79634380237962, -221.89634452708827, 211.31986556310198);
            TestUtils.AreEqual(r0, ++a0);

            double3x2 a1 = double3x2(-392.73151058365193, 362.21273939787068, 401.614830919362, 130.90919429199266, -450.23016402229212, 243.54693114177644);
            double3x2 r1 = double3x2(-391.73151058365193, 363.21273939787068, 402.614830919362, 131.90919429199266, -449.23016402229212, 244.54693114177644);
            TestUtils.AreEqual(r1, ++a1);

            double3x2 a2 = double3x2(46.19202735190845, 299.18547000511808, 154.35656530892311, -281.23327435237974, 200.70599922943211, 92.957765384091886);
            double3x2 r2 = double3x2(47.19202735190845, 300.18547000511808, 155.35656530892311, -280.23327435237974, 201.70599922943211, 93.957765384091886);
            TestUtils.AreEqual(r2, ++a2);

            double3x2 a3 = double3x2(448.60215565590283, 18.499063262016989, -215.71113381893895, 471.94723651928234, 257.07660090973445, 41.625937719655212);
            double3x2 r3 = double3x2(449.60215565590283, 19.499063262016989, -214.71113381893895, 472.94723651928234, 258.07660090973445, 42.625937719655212);
            TestUtils.AreEqual(r3, ++a3);
        }

        [TestCompiler]
        public static void double3x2_operator_postfix_inc()
        {
            double3x2 a0 = double3x2(-396.6697396695007, 511.20749378167443, 249.11127030528678, -128.81731301584153, -259.49027669592306, 278.00817764830219);
            double3x2 r0 = double3x2(-396.6697396695007, 511.20749378167443, 249.11127030528678, -128.81731301584153, -259.49027669592306, 278.00817764830219);
            TestUtils.AreEqual(r0, a0++);

            double3x2 a1 = double3x2(-81.393423356764686, 167.85212691493894, 147.94395048354932, -326.10758486674524, 41.033564825092185, 128.5304239394751);
            double3x2 r1 = double3x2(-81.393423356764686, 167.85212691493894, 147.94395048354932, -326.10758486674524, 41.033564825092185, 128.5304239394751);
            TestUtils.AreEqual(r1, a1++);

            double3x2 a2 = double3x2(73.155582223625629, -446.22976490772783, -296.93783797739906, 267.29380071689081, 446.22930714405572, 49.200223230384381);
            double3x2 r2 = double3x2(73.155582223625629, -446.22976490772783, -296.93783797739906, 267.29380071689081, 446.22930714405572, 49.200223230384381);
            TestUtils.AreEqual(r2, a2++);

            double3x2 a3 = double3x2(-326.64314738225335, 471.64748762159024, -171.01308186865089, 310.72735967800361, -298.91717185588425, 489.98497008252184);
            double3x2 r3 = double3x2(-326.64314738225335, 471.64748762159024, -171.01308186865089, 310.72735967800361, -298.91717185588425, 489.98497008252184);
            TestUtils.AreEqual(r3, a3++);
        }

        [TestCompiler]
        public static void double3x2_operator_prefix_dec()
        {
            double3x2 a0 = double3x2(123.12869626056806, 256.8437465433235, 156.33078844674435, 461.73742530389563, 325.86799755965728, 392.01561731473339);
            double3x2 r0 = double3x2(122.12869626056806, 255.8437465433235, 155.33078844674435, 460.73742530389563, 324.86799755965728, 391.01561731473339);
            TestUtils.AreEqual(r0, --a0);

            double3x2 a1 = double3x2(187.87412580655609, 125.10963517292851, 469.8447313112415, 45.536655685648611, 376.04684680329956, -363.07547991493504);
            double3x2 r1 = double3x2(186.87412580655609, 124.10963517292851, 468.8447313112415, 44.536655685648611, 375.04684680329956, -364.07547991493504);
            TestUtils.AreEqual(r1, --a1);

            double3x2 a2 = double3x2(-22.028951416736902, 168.0950144120003, 168.26565011230559, -190.284744112885, 166.9455474200405, 183.95795854551625);
            double3x2 r2 = double3x2(-23.028951416736902, 167.0950144120003, 167.26565011230559, -191.284744112885, 165.9455474200405, 182.95795854551625);
            TestUtils.AreEqual(r2, --a2);

            double3x2 a3 = double3x2(485.69469259944492, 89.569894117102876, -267.42982090051743, 201.75623450137505, -141.21688682456357, -217.48409782046645);
            double3x2 r3 = double3x2(484.69469259944492, 88.569894117102876, -268.42982090051743, 200.75623450137505, -142.21688682456357, -218.48409782046645);
            TestUtils.AreEqual(r3, --a3);
        }

        [TestCompiler]
        public static void double3x2_operator_postfix_dec()
        {
            double3x2 a0 = double3x2(379.68831723727669, 302.69287814884115, -176.07134040448409, -291.25267066212962, 470.56758401848731, -402.92594666170231);
            double3x2 r0 = double3x2(379.68831723727669, 302.69287814884115, -176.07134040448409, -291.25267066212962, 470.56758401848731, -402.92594666170231);
            TestUtils.AreEqual(r0, a0--);

            double3x2 a1 = double3x2(-63.655158787805192, -27.889220489137415, -100.76183824462902, 156.14034969924967, 479.94519613680677, -200.30429491787419);
            double3x2 r1 = double3x2(-63.655158787805192, -27.889220489137415, -100.76183824462902, 156.14034969924967, 479.94519613680677, -200.30429491787419);
            TestUtils.AreEqual(r1, a1--);

            double3x2 a2 = double3x2(-445.0269393609031, 327.67032519340069, 48.0602071509046, -209.66798100698179, -38.435048836485976, 283.941595924991);
            double3x2 r2 = double3x2(-445.0269393609031, 327.67032519340069, 48.0602071509046, -209.66798100698179, -38.435048836485976, 283.941595924991);
            TestUtils.AreEqual(r2, a2--);

            double3x2 a3 = double3x2(-94.802087112703418, -287.262531175866, -215.94803939384781, -407.04635567546188, 159.23357136511879, -359.45648663093175);
            double3x2 r3 = double3x2(-94.802087112703418, -287.262531175866, -215.94803939384781, -407.04635567546188, 159.23357136511879, -359.45648663093175);
            TestUtils.AreEqual(r3, a3--);
        }

        [TestCase /* For player builds */]
        public static void double3x2_EqualsObjectOverride()
        {
            TestUtils.IsFalse(new double3x2().Equals((object)new int()));
            TestUtils.IsTrue(new double3x2().Equals((object)new double3x2()));
        }


    }
}
