using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// Represents a spatial grid with precise coordinate system
/// Grid position uses float coordinates where 1.0 = 1000km
/// Entities have local positions within the grid (-1000km to +1000km)
/// </summary>
[System.Serializable]
public class SpaceGrid
{
    [SerializeField] private int gridId;
    [SerializeField] private Vector3 center; // Unity world position (for rendering)
    [SerializeField] private Vector3 gridPosition; // Precise grid position (1.0 = 1000km)
    [SerializeField] private float radius;
    [SerializeField] private List<Entity> entities;
    [SerializeField] private bool isPlayerGrid;

    // Grid coordinate constants - scaled coordinate system
    public const float GRID_SCALE = 1000000f; // 1.0 grid unit = 1000km = 1,000,000 meters
    public const float DEFAULT_GRID_RADIUS_KM = 150f; // Default grid radius: 150km
    public const float MAX_GRID_RADIUS_KM = 1000f; // Maximum grid radius: 1000km
    public const float DEFAULT_GRID_RADIUS_SCALED = DEFAULT_GRID_RADIUS_KM / 1000f; // 0.15 scaled units
    public const float MAX_GRID_RADIUS_SCALED = MAX_GRID_RADIUS_KM / 1000f; // 1.0 scaled units
    
    // Grid state
    private float lastUpdateTime;
    private Vector3 lastCenter;

    public SpaceGrid(int id, Vector3 gridCenter, float gridRadius)
    {
        gridId = id;
        center = gridCenter;
        radius = gridRadius;
        entities = new List<Entity>();
        isPlayerGrid = false;
        lastUpdateTime = Time.time;
        lastCenter = gridCenter;

        // Calculate grid position from world center
        gridPosition = WorldToGridPosition(gridCenter);
    }

    
    /// <summary>
    /// Adds an entity to this grid
    /// </summary>
    public void AddEntity(Entity entity)
    {
        if (!entities.Contains(entity))
        {
            entities.Add(entity);
            entity.SetCurrentGrid(this);
            
            // Check if this is the player entity
            if (entity.IsPlayer)
            {
                isPlayerGrid = true;
            }
        }
    }
    
    /// <summary>
    /// Removes an entity from this grid
    /// </summary>
    public void RemoveEntity(Entity entity)
    {
        if (entities.Remove(entity))
        {
            entity.SetCurrentGrid(null);
            
            // Update player grid status
            if (entity.IsPlayer)
            {
                isPlayerGrid = false;
            }
        }
    }
    
    /// <summary>
    /// Checks if a position is within this grid's bounds
    /// </summary>
    public bool ContainsPosition(Vector3 position)
    {
        return Vector3.Distance(center, position) <= radius;
    }
    
    /// <summary>
    /// Gets all entities within a specific distance from a point
    /// </summary>
    public List<Entity> GetEntitiesInRange(Vector3 position, float range)
    {
        List<Entity> entitiesInRange = new List<Entity>();
        
        foreach (var entity in entities)
        {
            if (entity != null && Vector3.Distance(entity.transform.position, position) <= range)
            {
                entitiesInRange.Add(entity);
            }
        }
        
        return entitiesInRange;
    }
    
    /// <summary>
    /// Gets the closest entity to a given position
    /// </summary>
    public Entity GetClosestEntity(Vector3 position, Entity excludeEntity = null)
    {
        Entity closest = null;
        float closestDistance = float.MaxValue;
        
        foreach (var entity in entities)
        {
            if (entity == null || entity == excludeEntity) continue;
            
            float distance = Vector3.Distance(entity.transform.position, position);
            if (distance < closestDistance)
            {
                closestDistance = distance;
                closest = entity;
            }
        }
        
        return closest;
    }
    
    /// <summary>
    /// Updates the grid's center position
    /// </summary>
    public void UpdateCenter(Vector3 newCenter)
    {
        Vector3 offset = newCenter - center;
        center = newCenter;
        
        // Move all entities by the offset to maintain relative positions
        foreach (var entity in entities)
        {
            if (entity != null && !entity.IsPlayer) // Don't move the player
            {
                entity.transform.position += offset;
            }
        }
        
        lastCenter = center;
        lastUpdateTime = Time.time;
    }
    
    /// <summary>
    /// Checks if this grid overlaps with another grid
    /// </summary>
    public bool OverlapsWith(SpaceGrid otherGrid)
    {
        float distance = Vector3.Distance(center, otherGrid.center);
        return distance < (radius + otherGrid.radius);
    }
    
    /// <summary>
    /// Calculates the optimal merge radius for combining with another grid
    /// </summary>
    public float CalculateMergeRadius(SpaceGrid otherGrid)
    {
        float distance = Vector3.Distance(center, otherGrid.center);
        return (distance + radius + otherGrid.radius) / 2f;
    }
    
    /// <summary>
    /// Gets the center point for merging with another grid
    /// </summary>
    public Vector3 CalculateMergeCenter(SpaceGrid otherGrid)
    {
        // Weight the center based on the number of entities in each grid
        float thisWeight = entities.Count;
        float otherWeight = otherGrid.entities.Count;
        float totalWeight = thisWeight + otherWeight;
        
        if (totalWeight == 0) return (center + otherGrid.center) / 2f;
        
        return (center * thisWeight + otherGrid.center * otherWeight) / totalWeight;
    }
    
    /// <summary>
    /// Removes null or destroyed entities from the grid
    /// </summary>
    public void CleanupEntities()
    {
        entities.RemoveAll(entity => entity == null);
    }
    
    /// <summary>
    /// Gets the grid's bounds as a sphere
    /// </summary>
    public Bounds GetBounds()
    {
        return new Bounds(center, Vector3.one * radius * 2f);
    }
    
    /// <summary>
    /// Checks if the grid is empty (no entities)
    /// </summary>
    public bool IsEmpty()
    {
        CleanupEntities();
        return entities.Count == 0;
    }
    
    /// <summary>
    /// Gets the total mass of all entities in the grid
    /// </summary>
    public float GetTotalMass()
    {
        float totalMass = 0f;
        foreach (var entity in entities)
        {
            if (entity != null)
            {
                totalMass += entity.Mass;
            }
        }
        return totalMass;
    }
    
    /// <summary>
    /// Draws debug gizmos for this grid
    /// </summary>
    public void DrawDebugGizmos()
    {
        Gizmos.color = isPlayerGrid ? Color.green : Color.blue;
        Gizmos.DrawWireSphere(center, radius);
        
        // Draw connections to entities
        Gizmos.color = Color.yellow;
        foreach (var entity in entities)
        {
            if (entity != null)
            {
                Gizmos.DrawLine(center, entity.transform.position);
            }
        }
    }
    
    // Properties
    public int GridId => gridId;
    public Vector3 Center
    {
        get => center;
        set
        {
            center = value;
            gridPosition = WorldToGridPosition(value);
        }
    }
    public Vector3 GridPosition
    {
        get => gridPosition;
        set
        {
            gridPosition = value;
            center = GridToWorldPosition(value);
        }
    }
    public float Radius
    {
        get => radius;
        set => radius = value;
    }
    public List<Entity> GetEntities() => new List<Entity>(entities);
    public int EntityCount => entities.Count;
    public bool IsPlayerGrid => isPlayerGrid;
    public float LastUpdateTime => lastUpdateTime;

    /// <summary>
    /// Converts world position to grid position (1.0 = 1000km)
    /// </summary>
    public static Vector3 WorldToGridPosition(Vector3 worldPos)
    {
        return worldPos / GRID_SCALE;
    }

    /// <summary>
    /// Converts grid position to world position
    /// </summary>
    public static Vector3 GridToWorldPosition(Vector3 gridPos)
    {
        return gridPos * GRID_SCALE;
    }

    /// <summary>
    /// Gets the precise universal position of this grid
    /// </summary>
    public Vector3 GetUniversalPosition()
    {
        return gridPosition;
    }

    /// <summary>
    /// Sets the grid position precisely
    /// </summary>
    public void SetGridPosition(Vector3 newGridPosition)
    {
        gridPosition = newGridPosition;
        center = GridToWorldPosition(newGridPosition);
    }

    /// <summary>
    /// Moves the grid by a precise offset
    /// </summary>
    public void MoveGrid(Vector3 gridOffset)
    {
        gridPosition += gridOffset;
        center = GridToWorldPosition(gridPosition);
    }
    
    public override string ToString()
    {
        return $"Grid {gridId}: Center={center}, Radius={radius}km, Entities={entities.Count}, IsPlayerGrid={isPlayerGrid}";
    }
}
