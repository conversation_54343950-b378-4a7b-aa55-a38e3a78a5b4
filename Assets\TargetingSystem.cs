using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// EVE-style targeting system with multiple target support
/// </summary>
public class TargetingSystem : MonoBehaviour
{
    [Header("Targeting Settings")]
    [SerializeField] private int maxTargets = 8;
    [SerializeField] private float maxTargetingRange = 150000f; // 150km in meters
    [SerializeField] private LayerMask targetableLayers = -1;
    
    // Targeting state
    private List<Entity> targetedEntities = new List<Entity>();
    private Entity primaryTarget = null;
    
    // Events
    public System.Action<Entity> OnTargetAdded;
    public System.Action<Entity> OnTargetRemoved;
    public System.Action<Entity> OnPrimaryTargetChanged;
    
    // Singleton
    public static TargetingSystem Instance { get; private set; }
    
    void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    void Update()
    {
        HandleTargetingInput();
        UpdateTargetValidation();
    }
    
    void HandleTargetingInput()
    {
        // Ctrl + Left Click to target
        if (Input.GetKey(KeyCode.LeftControl) && Input.GetMouseButtonDown(0))
        {
            TryTargetAtMousePosition();
        }
        
        // Clear all targets with Escape
        if (Input.GetKeyDown(KeyCode.Escape))
        {
            ClearAllTargets();
        }
        
        // Cycle primary target with Tab
        if (Input.GetKeyDown(KeyCode.Tab))
        {
            CyclePrimaryTarget();
        }
    }
    
    void TryTargetAtMousePosition()
    {
        Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
        RaycastHit hit;
        
        if (Physics.Raycast(ray, out hit, Mathf.Infinity, targetableLayers))
        {
            Entity entity = hit.collider.GetComponent<Entity>();
            if (entity != null)
            {
                ToggleTarget(entity);
            }
        }
    }
    
    public bool ToggleTarget(Entity entity)
    {
        if (entity == null) return false;
        
        // Don't target self
        if (IsPlayerEntity(entity)) return false;
        
        // Check range
        if (!IsInTargetingRange(entity)) return false;
        
        if (IsTargeted(entity))
        {
            RemoveTarget(entity);
            return false;
        }
        else
        {
            AddTarget(entity);
            return true;
        }
    }
    
    public bool AddTarget(Entity entity)
    {
        if (entity == null || IsTargeted(entity)) return false;
        
        // Don't target self
        if (IsPlayerEntity(entity)) return false;
        
        // Check range
        if (!IsInTargetingRange(entity)) return false;
        
        // Check max targets
        if (targetedEntities.Count >= maxTargets) return false;
        
        targetedEntities.Add(entity);
        
        // Set as primary if first target
        if (primaryTarget == null)
        {
            SetPrimaryTarget(entity);
        }
        
        OnTargetAdded?.Invoke(entity);
        return true;
    }
    
    public void RemoveTarget(Entity entity)
    {
        if (entity == null || !IsTargeted(entity)) return;
        
        targetedEntities.Remove(entity);
        
        // Update primary target if needed
        if (primaryTarget == entity)
        {
            primaryTarget = targetedEntities.Count > 0 ? targetedEntities[0] : null;
            OnPrimaryTargetChanged?.Invoke(primaryTarget);
        }
        
        OnTargetRemoved?.Invoke(entity);
    }
    
    public void SetPrimaryTarget(Entity entity)
    {
        if (entity != null && IsTargeted(entity))
        {
            primaryTarget = entity;
            OnPrimaryTargetChanged?.Invoke(primaryTarget);
        }
    }
    
    public void ClearAllTargets()
    {
        var targets = new List<Entity>(targetedEntities);
        foreach (var target in targets)
        {
            RemoveTarget(target);
        }
    }
    
    void CyclePrimaryTarget()
    {
        if (targetedEntities.Count <= 1) return;
        
        int currentIndex = targetedEntities.IndexOf(primaryTarget);
        int nextIndex = (currentIndex + 1) % targetedEntities.Count;
        SetPrimaryTarget(targetedEntities[nextIndex]);
    }
    
    void UpdateTargetValidation()
    {
        // Remove targets that are out of range or destroyed
        var targetsToRemove = new List<Entity>();
        
        foreach (var target in targetedEntities)
        {
            if (target == null || !IsInTargetingRange(target))
            {
                targetsToRemove.Add(target);
            }
        }
        
        foreach (var target in targetsToRemove)
        {
            RemoveTarget(target);
        }
    }
    
    public bool IsTargeted(Entity entity)
    {
        return targetedEntities.Contains(entity);
    }
    
    bool IsPlayerEntity(Entity entity)
    {
        return entity.GetComponent<SpaceshipController>() != null;
    }
    
    bool IsInTargetingRange(Entity entity)
    {
        if (SpaceCoordinateManager.Instance == null || SpaceCoordinateManager.Instance.PlayerEntity == null)
            return false;
        
        // Check if in same grid first
        if (SpaceCoordinateManager.Instance.AreEntitiesInSameGrid(SpaceCoordinateManager.Instance.PlayerEntity, entity))
        {
            // Use grid-local distance
            float distance = SpaceCoordinateManager.Instance.GetGridLocalDistance(SpaceCoordinateManager.Instance.PlayerEntity, entity);
            return distance <= maxTargetingRange;
        }
        
        return false; // Can only target entities in same grid
    }
    
    // Public getters
    public List<Entity> GetTargetedEntities() => new List<Entity>(targetedEntities);
    public Entity GetPrimaryTarget() => primaryTarget;
    public bool HasTargets() => targetedEntities.Count > 0;
    public int GetTargetCount() => targetedEntities.Count;
    public int GetMaxTargets() => maxTargets;
    
    // Debug info
    public string GetDebugInfo()
    {
        string info = $"=== TARGETING SYSTEM ===\n";
        info += $"Targets: {targetedEntities.Count}/{maxTargets}\n";
        info += $"Primary: {(primaryTarget != null ? primaryTarget.name : "None")}\n";
        info += $"Max Range: {maxTargetingRange / 1000f:F1}km\n";
        
        if (targetedEntities.Count > 0)
        {
            info += "Targeted Entities:\n";
            for (int i = 0; i < targetedEntities.Count; i++)
            {
                var target = targetedEntities[i];
                bool isPrimary = target == primaryTarget;
                float distance = SpaceCoordinateManager.Instance?.GetGridLocalDistance(
                    SpaceCoordinateManager.Instance.PlayerEntity, target) ?? 0f;
                info += $"  {i + 1}. {target.name} ({distance / 1000f:F1}km) {(isPrimary ? "[PRIMARY]" : "")}\n";
            }
        }
        
        return info;
    }
}
